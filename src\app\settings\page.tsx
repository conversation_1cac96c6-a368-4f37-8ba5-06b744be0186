
"use client";

import { useState, useEffect, useRef } from "react";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button, buttonVariants } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { KeyRound, Bell, CreditCard, UserCircle, Info, Wifi, ShieldAlert, Trash2, Loader2, CheckCircle, XCircle, SettingsIcon, Eye, EyeOff, Server, Bitcoin, RefreshCw } from "lucide-react";
import { toast } from "@/hooks/use-toast";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"

interface BinanceConnectionData {
  accountType: string;
  canTrade: boolean;
  currentBTCPrice?: string | null;
  priceError?: string | null;
}

const PRICE_UPDATE_INTERVAL = 1000; // 1 second

export default function SettingsPage() {
  const [clientIp, setClientIp] = useState<string | null>(null);
  const [isLoadingIp, setIsLoadingIp] = useState(false);

  const [binanceApiKey, setBinanceApiKey] = useState<string>("");
  const [binanceApiSecret, setBinanceApiSecret] = useState<string>("");
  const [isBinanceConnected, setIsBinanceConnected] = useState<boolean>(false);
  const [showBinanceSecret, setShowBinanceSecret] = useState<boolean>(false);
  const [isTestingConnection, setIsTestingConnection] = useState<boolean>(false);
  const [lastConnectionTestSuccess, setLastConnectionTestSuccess] = useState<boolean | null>(null);
  const [lastConnectionTestData, setLastConnectionTestData] = useState<BinanceConnectionData | null>(null);
  const [isUpdatingPrice, setIsUpdatingPrice] = useState<boolean>(false);

  const priceUpdateIntervalRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    const storedApiKey = localStorage.getItem("binanceApiKey");
    const storedApiSecret = localStorage.getItem("binanceApiSecret");
    if (storedApiKey && storedApiSecret) {
      setBinanceApiKey(storedApiKey);
      setBinanceApiSecret(storedApiSecret);
      setIsBinanceConnected(true);
    }
  }, []);

  const resetConnectionTestData = () => {
    setLastConnectionTestSuccess(null);
    setLastConnectionTestData(null);
    if (priceUpdateIntervalRef.current) {
      clearInterval(priceUpdateIntervalRef.current);
      priceUpdateIntervalRef.current = null;
    }
     setIsUpdatingPrice(false);
  }
  
  useEffect(() => {
    // Cleanup interval on component unmount
    return () => {
      if (priceUpdateIntervalRef.current) {
        clearInterval(priceUpdateIntervalRef.current);
      }
    };
  }, []);

  useEffect(() => {
    if (priceUpdateIntervalRef.current) {
      clearInterval(priceUpdateIntervalRef.current);
      priceUpdateIntervalRef.current = null;
    }

    if (isBinanceConnected && lastConnectionTestSuccess && binanceApiKey && binanceApiSecret) {
      const fetchPrice = async () => {
        setIsUpdatingPrice(true);
        try {
          const response = await fetch('/api/binance/current-price', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ apiKey: binanceApiKey, apiSecret: binanceApiSecret }),
          });
          const result = await response.json();
          if (result.success && result.price) {
            setLastConnectionTestData(prevData => prevData ? {...prevData, currentBTCPrice: result.price, priceError: null } : null);
          } else if (result.message) {
             setLastConnectionTestData(prevData => prevData ? {...prevData, priceError: `Atualização de preço: ${result.message}` } : null);
          }
        } catch (error) {
            setLastConnectionTestData(prevData => prevData ? {...prevData, priceError: "Falha ao atualizar preço." } : null);
          console.error("Erro ao buscar preço em tempo real:", error);
        } finally {
          setIsUpdatingPrice(false);
        }
      };
      
      // Initial fetch is handled by handleTestBinanceConnection which populates price
      // Start interval for subsequent fetches
      priceUpdateIntervalRef.current = setInterval(fetchPrice, PRICE_UPDATE_INTERVAL);
    }

    // This return function acts as a cleanup for this effect
    return () => {
      if (priceUpdateIntervalRef.current) {
        clearInterval(priceUpdateIntervalRef.current);
        priceUpdateIntervalRef.current = null;
      }
    };
  }, [isBinanceConnected, lastConnectionTestSuccess, binanceApiKey, binanceApiSecret]);


  const handleFetchIp = async () => {
    setIsLoadingIp(true);
    setClientIp(null);
    try {
      const response = await fetch('/api/ip');
      if (!response.ok) {
        throw new Error('Failed to fetch IP');
      }
      const data = await response.json();
      setClientIp(data.ip || "N/A");
    } catch (error) {
      console.error("Error fetching IP:", error);
      setClientIp("Erro ao buscar IP.");
      toast({
        title: "Erro",
        description: "Não foi possível obter o endereço IP no momento.",
        variant: "destructive",
      });
    } finally {
      setIsLoadingIp(false);
    }
  };

  const handleSaveBinanceKeys = () => {
    if (!binanceApiKey.trim() || !binanceApiSecret.trim()) {
      toast({
        title: "Campos Obrigatórios",
        description: "Por favor, insira a Chave de API e o Segredo da API da Binance.",
        variant: "destructive",
      });
      return;
    }
    localStorage.setItem("binanceApiKey", binanceApiKey);
    localStorage.setItem("binanceApiSecret", binanceApiSecret);
    setIsBinanceConnected(true);
    resetConnectionTestData(); // This will also clear any existing price update interval
    toast({
      title: "Chaves da Binance Salvas!",
      description: "Suas chaves de API da Binance foram salvas localmente. Teste a conexão para verificar.",
    });
  };

  const handleRemoveBinanceKeys = () => {
    localStorage.removeItem("binanceApiKey");
    localStorage.removeItem("binanceApiSecret");
    setBinanceApiKey("");
    setBinanceApiSecret("");
    setIsBinanceConnected(false);
    setShowBinanceSecret(false);
    resetConnectionTestData(); // This will also clear any existing price update interval
    toast({
      title: "Chaves da Binance Removidas",
      description: "Suas chaves de API da Binance foram removidas do armazenamento local.",
      variant: "destructive"
    });
  };

  const handleTestBinanceConnection = async () => {
    if (!isBinanceConnected) {
      toast({
        title: "Chaves não configuradas",
        description: "Por favor, salve suas chaves da Binance antes de testar a conexão.",
        variant: "destructive",
      });
      return;
    }

    setIsTestingConnection(true);
    resetConnectionTestData(); // Clear previous data and stop price interval
    
    try {
      const response = await fetch('/api/binance/test-connection', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ apiKey: binanceApiKey, apiSecret: binanceApiSecret }),
      });
      
      const result = await response.json();

      if (response.ok && result.success) {
        toast({
          title: "Conexão Bem-Sucedida!",
          description: result.message || "A conexão com a API da Binance foi verificada e dados da conta obtidos.",
        });
        setLastConnectionTestSuccess(true);
        if(result.data) {
          setLastConnectionTestData(result.data); // This will also trigger the price update useEffect
        }
      } else {
        throw new Error(result.message || `Falha ao testar a conexão com a Binance. Status: ${response.status}`);
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Ocorreu um erro desconhecido.";
      toast({
        title: "Falha na Conexão",
        description: `${errorMessage} (Verifique suas chaves, permissões de API e conexão de rede).`,
        variant: "destructive",
      });
      setLastConnectionTestSuccess(false);
      console.error("Erro ao testar conexão Binance:", error);
    } finally {
      setIsTestingConnection(false);
    }
  };


  return (
    <div className="space-y-8">
      <div>
        <h1 className="text-3xl font-bold tracking-tight mb-2 flex items-center gap-2"><SettingsIcon className="h-8 w-8 text-primary"/>Configurações</h1>
        <p className="text-muted-foreground">
          Gerencie suas preferências de conta, chaves de API e assinatura.
        </p>
      </div>

      <Card className="card-interactive">
        <CardHeader>
          <CardTitle className="flex items-center gap-2"><UserCircle className="h-5 w-5 text-primary"/>Configurações de Perfil</CardTitle>
          <CardDescription>Atualize suas informações pessoais e senha.</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="name">Nome Completo</Label>
              <Input id="name" defaultValue="Satoshi Nakamoto" />
            </div>
            <div>
              <Label htmlFor="email">Endereço de Email</Label>
              <Input id="email" type="email" defaultValue="<EMAIL>" />
            </div>
          </div>
          <Button variant="outline">Mudar Senha</Button>
          <Button className="ml-2">Salvar Perfil</Button>
        </CardContent>
      </Card>

      <Separator />

      <Card className="card-interactive">
        <CardHeader>
          <CardTitle className="flex items-center gap-2"><KeyRound className="h-5 w-5 text-primary"/>Gerenciamento de Chaves de API</CardTitle>
          <CardDescription>Conecte e gerencie suas chaves de API de exchanges.</CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="p-4 border border-amber-500/30 bg-amber-500/10 rounded-md flex items-start gap-3">
            <ShieldAlert className="h-6 w-6 text-amber-500 flex-shrink-0 mt-1" />
            <div>
              <h4 className="font-semibold text-amber-600 dark:text-amber-400">Aviso de Segurança Importante</h4>
              <p className="text-xs text-amber-700 dark:text-amber-500">
                Suas chaves de API são armazenadas localmente no seu navegador (`localStorage`). Este método **não é recomendado para ambientes de produção**. Para máxima segurança, utilize um backend seguro para gerenciar chaves de API. Nunca compartilhe suas chaves de API. A funcionalidade de teste de conexão **tenta realizar uma chamada real à API da Binance** usando as chaves fornecidas para verificar sua validade, permissões básicas e obter dados da conta, incluindo o preço atual do BTC para demonstração. Contudo, a forma como as chaves são manuseadas aqui (armazenadas no seu navegador e enviadas diretamente para uma API route deste protótipo) não é segura para produção.
              </p>
            </div>
          </div>
          
          <Separator />

          <div>
            <h3 className="text-xl font-semibold mb-1">Binance</h3>
            {!isBinanceConnected ? (
              <div className="space-y-4 mt-4">
                <p className="text-sm text-muted-foreground">Conecte sua conta Binance para habilitar o rastreamento de portfólio e funcionalidades de trading.</p>
                <div>
                  <Label htmlFor="binanceApiKey">Chave de API da Binance</Label>
                  <Input 
                    id="binanceApiKey" 
                    placeholder="Sua Chave de API" 
                    value={binanceApiKey} 
                    onChange={(e) => { setBinanceApiKey(e.target.value); resetConnectionTestData(); }} 
                  />
                </div>
                <div>
                  <Label htmlFor="binanceApiSecret">Segredo da API da Binance</Label>
                   <div className="relative">
                    <Input 
                      id="binanceApiSecret" 
                      type={showBinanceSecret ? "text" : "password"} 
                      placeholder="Seu Segredo da API" 
                      value={binanceApiSecret} 
                      onChange={(e) => { setBinanceApiSecret(e.target.value); resetConnectionTestData(); }} 
                    />
                    <Button 
                      variant="ghost" 
                      size="icon" 
                      onClick={() => setShowBinanceSecret(!showBinanceSecret)} 
                      className="absolute right-1 top-1/2 -translate-y-1/2 h-7 w-7"
                      type="button"
                    >
                      {showBinanceSecret ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                      <span className="sr-only">{showBinanceSecret ? "Ocultar segredo" : "Mostrar segredo"}</span>
                    </Button>
                  </div>
                </div>
                <Button onClick={handleSaveBinanceKeys} className="w-full sm:w-auto bg-accent hover:bg-accent/90 text-accent-foreground">
                  Salvar Chaves da Binance
                </Button>
              </div>
            ) : (
              <div className="space-y-4 mt-4 p-4 bg-muted/30 rounded-md border">
                <div className="flex flex-col sm:flex-row justify-between items-start gap-2">
                    <div>
                        <p className="text-green-600 dark:text-green-400 font-medium">Binance API Conectada.</p>
                        {lastConnectionTestSuccess === true && <p className="text-xs text-green-500 flex items-center gap-1"><CheckCircle className="h-3 w-3"/>Conexão verificada com sucesso.</p>}
                        {lastConnectionTestSuccess === false && <p className="text-xs text-red-500 flex items-center gap-1"><XCircle className="h-3 w-3"/>Falha na última verificação da conexão.</p>}
                        {lastConnectionTestSuccess === null && !isTestingConnection && <p className="text-xs text-muted-foreground">Pendente de teste de conexão.</p>}
                    </div>
                     <Button 
                        onClick={handleTestBinanceConnection} 
                        disabled={isTestingConnection} 
                        variant="outline"
                        size="sm"
                        className="w-full sm:w-auto"
                      >
                        {isTestingConnection ? (
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        ) : <Server className="mr-2 h-4 w-4"/>}
                        Testar Conexão
                      </Button>
                </div>

                {isTestingConnection && (
                  <div className="mt-3 p-3 bg-background/50 rounded-md border border-dashed text-center">
                    <Loader2 className="mx-auto h-5 w-5 animate-spin text-primary mb-1" />
                    <p className="text-sm text-muted-foreground">Testando conexão com a Binance...</p>
                  </div>
                )}

                {lastConnectionTestData && lastConnectionTestSuccess && !isTestingConnection && (
                  <div className="mt-3 p-3 bg-background/50 rounded-md border border-dashed border-green-500/30 space-y-1">
                    <div className="flex justify-between items-center">
                      <h4 className="text-sm font-semibold text-green-600 dark:text-green-400 mb-1">Dados da Conexão Verificada:</h4>
                      {isUpdatingPrice && <Loader2 className="h-3.5 w-3.5 animate-spin text-muted-foreground" />}
                    </div>
                    <p className="text-xs flex items-center"><UserCircle className="h-3.5 w-3.5 mr-1.5 text-muted-foreground"/><strong>Tipo de Conta:</strong> {lastConnectionTestData.accountType}</p>
                    <p className="text-xs flex items-center"><CheckCircle className="h-3.5 w-3.5 mr-1.5 text-muted-foreground"/><strong>Permissão para Negociar:</strong> {lastConnectionTestData.canTrade ? "Sim" : "Não"}</p>
                    {lastConnectionTestData.currentBTCPrice && (
                       <p className="text-xs flex items-center">
                         <Bitcoin className="h-3.5 w-3.5 mr-1.5 text-muted-foreground"/>
                         <strong>Preço BTCUSDT:</strong> {lastConnectionTestData.currentBTCPrice}
                       </p>
                    )}
                    {lastConnectionTestData.priceError && (
                       <p className="text-xs flex items-center text-amber-600 dark:text-amber-500"><Info className="h-3.5 w-3.5 mr-1.5"/>{lastConnectionTestData.priceError}</p>
                    )}
                  </div>
                )}


                <div>
                  <Label className="text-xs">Chave de API:</Label>
                  <p className="font-mono text-sm bg-background p-2 rounded border break-all">
                    {binanceApiKey ? `${binanceApiKey.substring(0, 8)}...${binanceApiKey.substring(binanceApiKey.length - 8)}` : "Não configurada"}
                  </p>
                </div>
                <div>
                  <Label className="text-xs">Segredo da API:</Label>
                  <div className="relative">
                    <Input 
                      type={showBinanceSecret ? "text" : "password"} 
                      readOnly 
                      value={binanceApiSecret || "Não configurado"} 
                      className="font-mono text-sm bg-background !opacity-100"
                    />
                     <Button 
                      variant="ghost" 
                      size="icon" 
                      onClick={() => setShowBinanceSecret(!showBinanceSecret)} 
                      className="absolute right-1 top-1/2 -translate-y-1/2 h-7 w-7"
                      type="button"
                      disabled={!binanceApiSecret}
                    >
                      {showBinanceSecret ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                       <span className="sr-only">{showBinanceSecret ? "Ocultar segredo" : "Mostrar segredo"}</span>
                    </Button>
                  </div>
                </div>
                 <AlertDialog>
                  <AlertDialogTrigger asChild>
                    <Button variant="destructive" className="w-full sm:w-auto">
                      <Trash2 className="mr-2 h-4 w-4" />
                      Remover Chaves da Binance
                    </Button>
                  </AlertDialogTrigger>
                  <AlertDialogContent>
                    <AlertDialogHeader>
                      <AlertDialogTitle>Confirmar Remoção</AlertDialogTitle>
                      <AlertDialogDescription>
                        Tem certeza de que deseja remover suas chaves de API da Binance? Esta ação não pode ser desfeita e você precisará inseri-las novamente para usar os recursos da Binance.
                      </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                      <AlertDialogCancel>Cancelar</AlertDialogCancel>
                      <AlertDialogAction onClick={handleRemoveBinanceKeys} className={buttonVariants({variant: "destructive"})}>
                        Remover
                      </AlertDialogAction>
                    </AlertDialogFooter>
                  </AlertDialogContent>
                </AlertDialog>
              </div>
            )}
          </div>

          <Separator />

          <div>
            <h3 className="text-lg font-medium mb-2 flex items-center gap-2">
              <Wifi className="h-5 w-5 text-primary/80" />
              Whitelist de IP para Chaves de API
            </h3>
            <p className="text-sm text-muted-foreground mb-3">
              Para maior segurança, algumas exchanges exigem que você adicione endereços IP à lista de permissões (whitelist) para suas chaves de API. Isso garante que apenas esses IPs possam usar a chave.
            </p>
            <Button onClick={handleFetchIp} disabled={isLoadingIp} variant="outline">
              {isLoadingIp ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : null}
              {isLoadingIp ? "Buscando IP..." : "Mostrar Meu IP Público Atual"}
            </Button>
            {clientIp && (
              <div className="mt-4 p-3 bg-muted/50 rounded-md border border-dashed">
                <p className="text-sm font-medium">Seu IP público atual é:</p>
                <p className="text-lg font-mono font-semibold text-primary my-1">{clientIp}</p>
                <div className="flex items-start text-xs text-muted-foreground mt-2 p-2 bg-background/50 rounded border border-dashed border-amber-500/30">
                  <Info className="h-4 w-4 mr-2 mt-0.5 text-amber-500 flex-shrink-0" />
                  <span>
                    <strong>Importante:</strong> Este é o IP detectado para sua conexão atual. Se você tem um IP dinâmico, ele pode mudar. Se for usar a chave de API em um servidor, você precisará usar o IP de saída público desse servidor. Consulte seu provedor de hospedagem ou use comandos como `curl ifconfig.me` no seu servidor para obter o IP correto.
                  </span>
                </div>
              </div>
            )}
             {isLoadingIp && (
              <div className="mt-4 p-3 bg-muted/50 rounded-md text-center">
                <Loader2 className="mx-auto h-6 w-6 animate-spin text-muted-foreground mb-2" />
                <p className="text-sm text-muted-foreground">Carregando seu endereço IP...</p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
      
      <Separator />

      <Card className="card-interactive">
        <CardHeader>
          <CardTitle className="flex items-center gap-2"><Bell className="h-5 w-5 text-primary"/>Preferências de Notificação</CardTitle>
          <CardDescription>Escolha como você quer receber alertas.</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
                <Label htmlFor="email-notifications" className="flex flex-col space-y-1">
                    <span>Notificações por Email</span>
                    <span className="font-normal leading-snug text-muted-foreground">
                        Receba alertas de preço e atualizações importantes por email.
                    </span>
                </Label>
                <Switch id="email-notifications" defaultChecked />
            </div>
            <div className="flex items-center justify-between">
                <Label htmlFor="push-notifications" className="flex flex-col space-y-1">
                    <span>Notificações Push</span>
                    <span className="font-normal leading-snug text-muted-foreground">
                        Receba alertas em tempo real nos seus dispositivos (requer instalação do app ou permissão do navegador).
                    </span>
                </Label>
                <Switch id="push-notifications" />
            </div>
        </CardContent>
      </Card>

      <Separator />

      <Card className="card-interactive">
        <CardHeader>
          <CardTitle className="flex items-center gap-2"><CreditCard className="h-5 w-5 text-primary"/>Gerenciamento de Assinatura</CardTitle>
          <CardDescription>Veja seu plano atual e gerencie o faturamento.</CardDescription>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground">Plano Atual: <span className="font-semibold text-foreground">Pro Tier</span></p>
          <p className="text-muted-foreground">Renova em: 1 de Janeiro, 2025</p>
          <Button variant="outline" className="mt-4">Gerenciar Assinatura</Button>
        </CardContent>
      </Card>
    </div>
  );
}

