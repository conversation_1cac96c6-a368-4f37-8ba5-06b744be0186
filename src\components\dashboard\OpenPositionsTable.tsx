"use client"; 

import { useState } from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import type { OpenPosition } from "@/lib/types";
import { ArrowUpRight, ArrowDownRight, MoreHorizontal, XOctagon, Edit2, Loader2 } from "lucide-react";
import { Button, buttonVariants } from "@/components/ui/button"; 
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger, DropdownMenuSeparator } from "@/components/ui/dropdown-menu";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { toast } from "@/hooks/use-toast";
import { cn } from '@/lib/utils';
import { ModifyPositionModal } from '@/components/trading/ModifyPositionModal';

interface OpenPositionsTableProps {
  positions: OpenPosition[];
  apiKey?: string | null;
  apiSecret?: string | null;
  onPositionUpdate?: () => void;
}

const formatPrice = (price: number) => {
  if (price === 0) return "$0.00";
  // For prices less than $0.01, show more precision
  if (Math.abs(price) < 0.01) {
    return price.toLocaleString(undefined, {
      style: "currency",
      currency: "USD",
      minimumFractionDigits: 2,
      maximumFractionDigits: 8, // Allow up to 8 for very small prices
    });
  }
  // For prices >= $0.01 and < $1, show up to 4-6 decimal places
  if (Math.abs(price) < 1) {
    return price.toLocaleString(undefined, {
      style: "currency",
      currency: "USD",
      minimumFractionDigits: 2,
      maximumFractionDigits: 6, 
    });
  }
  // For prices >= $1, show 2 decimal places
  return price.toLocaleString(undefined, {
    style: "currency",
    currency: "USD",
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  });
};

const formatQuantity = (quantity: number) => {
  return quantity.toLocaleString(undefined, {
    minimumFractionDigits: 0, // Min 0 for whole numbers
    maximumFractionDigits: 8, // Allow up to 8 for crypto quantities
  });
};


export function OpenPositionsTable({ positions, apiKey, apiSecret, onPositionUpdate }: OpenPositionsTableProps) {
  const [selectedPosition, setSelectedPosition] = useState<OpenPosition | null>(null);
  const [isModifyModalOpen, setIsModifyModalOpen] = useState(false);
  const [isClosingPosition, setIsClosingPosition] = useState<string | null>(null);

  if (!positions || positions.length === 0) {
    return <p className="text-muted-foreground text-center py-4">Sem dados de posições abertas obtidos.</p>;
  }

  const handleClosePosition = async (position: OpenPosition) => {
    if (!apiKey || !apiSecret) {
      toast({
        title: "Chaves de API Ausentes",
        description: "Configure suas chaves de API nas configurações para fechar posições.",
        variant: "destructive",
      });
      return;
    }

    setIsClosingPosition(position.id);
    try {
      const response = await fetch('/api/binance/futures/close-position', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          apiKey,
          apiSecret,
          symbol: position.cryptoSymbol + 'USDT',
        }),
      });

      const result = await response.json();

      if (response.ok && result.success) {
        toast({
          title: "Sucesso!",
          description: result.message,
          className: "bg-green-100 dark:bg-green-800 border-green-500",
        });
        if (onPositionUpdate) {
          onPositionUpdate();
        }
      } else {
        throw new Error(result.message || 'Falha ao fechar posição');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Erro desconhecido";
      toast({
        title: "Erro ao Fechar Posição",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsClosingPosition(null);
    }
  };

  const handleModifyPosition = (position: OpenPosition) => {
    if (!apiKey || !apiSecret) {
      toast({
        title: "Chaves de API Ausentes",
        description: "Configure suas chaves de API nas configurações para modificar posições.",
        variant: "destructive",
      });
      return;
    }

    setSelectedPosition(position);
    setIsModifyModalOpen(true);
  };

  const handleModifySuccess = () => {
    if (onPositionUpdate) {
      onPositionUpdate();
    }
  };

  return (
    <>
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Ativo</TableHead>
            <TableHead className="hidden md:table-cell">Exchange</TableHead>
            <TableHead>Tipo</TableHead>
            <TableHead className="text-right">Preço Entrada</TableHead>
            <TableHead className="text-right">Preço Atual</TableHead>
            <TableHead className="text-right hidden sm:table-cell">Quantidade</TableHead>
            <TableHead className="text-right">P&L (USD)</TableHead>
            <TableHead className="text-right">Ações</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {positions.map((pos) => (
            <TableRow key={pos.id} className="hover:bg-muted/50">
              <TableCell>
                <div className="font-medium">{pos.cryptoName}</div>
                <div className="text-xs text-muted-foreground">{pos.cryptoSymbol}</div>
              </TableCell>
              <TableCell className="hidden md:table-cell">
                <Badge variant="outline">{pos.exchange}</Badge>
              </TableCell>
              <TableCell>
                <Badge variant={pos.type === 'long' ? 'default' : 'secondary'} className={pos.type === 'long' ? 'bg-green-600/20 text-green-400 border-green-600/30 hover:bg-green-600/30' : 'bg-red-600/20 text-red-400 border-red-600/30 hover:bg-red-600/30'}>
                  {pos.type.charAt(0).toUpperCase() + pos.type.slice(1)}
                </Badge>
              </TableCell>
              <TableCell className={cn("text-right", "tabular-nums")}>{formatPrice(pos.entryPrice)}</TableCell>
              <TableCell className={cn("text-right", "tabular-nums")}>{formatPrice(pos.currentPrice)}</TableCell>
              <TableCell className={cn("text-right", "hidden sm:table-cell", "tabular-nums")}>{formatQuantity(pos.quantity)}</TableCell>
              <TableCell className={cn("text-right", "tabular-nums")}>
                <div className={pos.pnl >= 0 ? 'text-green-500' : 'text-red-500'}>
                  {pos.pnl.toLocaleString(undefined, { style: 'currency', currency: 'USD', minimumFractionDigits: 2, maximumFractionDigits: 2 })}
                  <span className="ml-1 text-xs">({pos.pnlPercentage.toFixed(2)}%)</span> 
                  {pos.pnl >= 0 ? <ArrowUpRight className="inline h-3 w-3 ml-0.5" /> : <ArrowDownRight className="inline h-3 w-3 ml-0.5" />}
                </div>
              </TableCell>
              <TableCell className="text-right">
                <AlertDialog>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button 
                        variant="ghost" 
                        size="icon" 
                        className="h-8 w-8"
                        disabled={isClosingPosition === pos.id}
                      >
                        {isClosingPosition === pos.id ? (
                          <Loader2 className="h-4 w-4 animate-spin" />
                        ) : (
                          <MoreHorizontal className="h-4 w-4" />
                        )}
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <AlertDialogTrigger asChild>
                        <DropdownMenuItem className="text-red-500 hover:!text-red-500 focus:!text-red-500">
                          <XOctagon className="mr-2 h-4 w-4" />
                          Fechar Posição
                        </DropdownMenuItem>
                      </AlertDialogTrigger>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem onClick={() => handleModifyPosition(pos)}>
                        <Edit2 className="mr-2 h-4 w-4" />
                        Modificar (SL/TP)
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                  <AlertDialogContent>
                    <AlertDialogHeader>
                      <AlertDialogTitle>Confirmar Fechamento de Posição</AlertDialogTitle>
                      <AlertDialogDescription>
                        Você tem certeza que deseja fechar sua posição em {pos.cryptoSymbol} ({pos.type.toUpperCase()})? 
                        Esta ação executará uma ordem real na Binance e fechará completamente sua posição.
                        <br /><br />
                        <strong>P&L Atual:</strong> {pos.pnl.toLocaleString(undefined, { style: 'currency', currency: 'USD' })} ({pos.pnlPercentage.toFixed(2)}%)
                      </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                      <AlertDialogCancel>Cancelar</AlertDialogCancel>
                      <AlertDialogAction 
                        onClick={() => handleClosePosition(pos)}
                        className={buttonVariants({ variant: "destructive" })}
                        disabled={!apiKey || !apiSecret}
                      >
                        Confirmar Fechamento
                      </AlertDialogAction>
                    </AlertDialogFooter>
                  </AlertDialogContent>
                </AlertDialog>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>

      <ModifyPositionModal
        position={selectedPosition}
        isOpen={isModifyModalOpen}
        onClose={() => {
          setIsModifyModalOpen(false);
          setSelectedPosition(null);
        }}
        apiKey={apiKey || null}
        apiSecret={apiSecret || null}
        onSuccess={handleModifySuccess}
      />
    </>
  );
}
