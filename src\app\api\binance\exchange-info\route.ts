
import type { NextRequest } from 'next/server';
import { NextResponse } from 'next/server';
import Binance from 'node-binance-api';

interface SymbolInfo {
  symbol: string;
  status: string;
  baseAsset: string;
  quoteAsset: string;
  // ... other fields from exchangeInfo
}

export async function GET(request: NextRequest) {
  // Note: For fetching general exchange info like symbols, API keys might not be strictly necessary
  // for public endpoints, but the library might require them for instantiation.
  // For simplicity in this prototype, we're not using user-specific keys here.
  // In a production app, you might have a server-side configured key for such general calls,
  // or the library might handle public calls gracefully.
  const binance = new Binance(); // Instantiate without keys for public data if library supports

  try {
    const exchangeInfo = await binance.exchangeInfo();
    if (!exchangeInfo || !exchangeInfo.symbols) {
      return NextResponse.json({ success: false, message: "Failed to fetch exchange information." }, { status: 500 });
    }

    const symbols: SymbolInfo[] = exchangeInfo.symbols;
    const popularQuoteAssets = ['USDT', 'BUSD', 'BTC', 'ETH']; // Prioritize these
    
    const cryptoList: { symbol: string; name: string }[] = symbols
      .filter(s => s.status === 'TRADING' && popularQuoteAssets.includes(s.quoteAsset))
      .map(s => ({
        symbol: s.baseAsset,
        // For name, we'll use the baseAsset. A more sophisticated approach might map symbols to full names.
        name: s.baseAsset, 
      }))
      // Deduplicate based on symbol
      .filter((value, index, self) => self.findIndex(v => v.symbol === value.symbol) === index)
      .sort((a, b) => a.symbol.localeCompare(b.symbol)); // Sort alphabetically

    return NextResponse.json({ success: true, data: cryptoList });

  } catch (error: any) {
    console.error("Error fetching exchange info from Binance:", error.message || error);
    // The library might throw an error if API keys are expected even for public data.
    // This error handling tries to catch common issues.
    let errorMessage = "Failed to fetch cryptocurrency list from Binance.";
    if (error && error.body && typeof error.body === 'string') {
        try {
            const errorBody = JSON.parse(error.body);
            errorMessage = `Binance API Error: ${errorBody.msg || 'Could not retrieve symbols.'}`;
        } catch (parseError) {
             errorMessage = `Binance API Error: ${error.body}`;
        }
    } else if (error && error.message) {
        errorMessage = error.message;
    }
    return NextResponse.json({ success: false, message: errorMessage }, { status: 500 });
  }
}
