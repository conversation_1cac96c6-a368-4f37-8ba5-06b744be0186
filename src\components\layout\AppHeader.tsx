"use client";

import { useState, useEffect } from "react";
import { SidebarTrigger } from "@/components/ui/sidebar";
import { Separator } from "@/components/ui/separator";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  Settings,
  User,
  LogOut,
  Wifi,
  WifiOff,
  TrendingUp,
  Bell,
  ChevronDown,
  RefreshCw,
  Sun,
  Moon,
  Activity,
  DollarSign,
  UserCircle,
  Smile,
  Bot,
  Rocket,
  Briefcase,
  ShieldCheck,
  Coffee,
  Gamepad2,
  Code2
} from "lucide-react";
import Link from "next/link";
import { cn } from "@/lib/utils";
import type { LucideIcon } from "lucide-react";

interface ProfileIcon {
  name: string;
  IconComponent: LucideIcon;
}

const availableIcons: ProfileIcon[] = [
  { name: "UserCircle", IconComponent: UserCircle },
  { name: "Smile", IconComponent: Smile },
  { name: "Bot", IconComponent: Bot },
  { name: "Rocket", IconComponent: Rocket },
  { name: "Briefcase", IconComponent: Briefcase },
  { name: "ShieldCheck", IconComponent: ShieldCheck },
  { name: "Coffee", IconComponent: Coffee },
  { name: "Gamepad2", IconComponent: Gamepad2 },
  { name: "Code2", IconComponent: Code2 },
];

interface AppHeaderProps {
  className?: string;
}

export function AppHeader({ className }: AppHeaderProps) {
  const [isOnline, setIsOnline] = useState(true);
  const [lastUpdate, setLastUpdate] = useState<Date>(new Date());
  const [apiStatus, setApiStatus] = useState<'connected' | 'disconnected' | 'checking'>('checking');
  const [marketStatus, setMarketStatus] = useState<'open' | 'closed' | 'unknown'>('unknown');
  const [isDarkMode, setIsDarkMode] = useState(true);
  const [isMounted, setIsMounted] = useState(false);
  const [userFullName, setUserFullName] = useState<string | null>(null);
  const [userAvatar, setUserAvatar] = useState<string | null>(null);
  const [userSelectedIcon, setUserSelectedIcon] = useState<string | null>(null);
  
  // Set mounted state to prevent hydration mismatch
  useEffect(() => {
    setIsMounted(true);

    // Load user profile data
    const storedFullName = localStorage.getItem("userFullName");
    setUserFullName(storedFullName || "Trader");

    const storedAvatar = localStorage.getItem("userUploadedAvatar");
    if (storedAvatar) {
      setUserAvatar(storedAvatar);
      setUserSelectedIcon(null); // Clear icon if avatar is set
    } else {
      // Load selected icon if no uploaded avatar
      const storedIcon = localStorage.getItem("userSelectedAvatarIcon");
      if (storedIcon && availableIcons.some(icon => icon.name === storedIcon)) {
        setUserSelectedIcon(storedIcon);
      } else {
        setUserSelectedIcon("UserCircle"); // Default icon
      }
    }

    // Listen for profile updates
    const handleProfileUpdate = () => {
      const updatedName = localStorage.getItem("userFullName");
      const updatedAvatar = localStorage.getItem("userUploadedAvatar");
      const updatedIcon = localStorage.getItem("userSelectedAvatarIcon");

      setUserFullName(updatedName || "Trader");

      if (updatedAvatar) {
        setUserAvatar(updatedAvatar);
        setUserSelectedIcon(null); // Clear icon if avatar is set
      } else {
        setUserAvatar(null);
        // Set icon if no uploaded avatar
        if (updatedIcon && availableIcons.some(icon => icon.name === updatedIcon)) {
          setUserSelectedIcon(updatedIcon);
        } else {
          setUserSelectedIcon("UserCircle"); // Default icon
        }
      }
    };

    window.addEventListener('profileUpdated', handleProfileUpdate);

    return () => {
      window.removeEventListener('profileUpdated', handleProfileUpdate);
    };
  }, []);

  // Check online status
  useEffect(() => {
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);
    
    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);
    
    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  // Check API status periodically
  useEffect(() => {
    const checkApiStatus = async () => {
      try {
        const response = await fetch('/api/binance/test-connection', {
          method: 'GET',
        });
        setApiStatus(response.ok ? 'connected' : 'disconnected');
      } catch {
        setApiStatus('disconnected');
      }
    };

    checkApiStatus();
    const interval = setInterval(checkApiStatus, 30000); // Check every 30 seconds

    return () => clearInterval(interval);
  }, []);

  // Update timestamp
  useEffect(() => {
    const interval = setInterval(() => {
      setLastUpdate(new Date());
    }, 1000);

    return () => clearInterval(interval);
  }, []);

  const handleRefresh = () => {
    window.location.reload();
  };

  const handleLogout = () => {
    localStorage.removeItem('binanceApiKey');
    localStorage.removeItem('binanceApiSecret');
    window.location.reload();
  };

  const toggleTheme = () => {
    setIsDarkMode(!isDarkMode);
    // In a real app, you'd implement theme switching here
  };

  const getApiStatusColor = () => {
    switch (apiStatus) {
      case 'connected': return 'status-online';
      case 'disconnected': return 'status-offline';
      case 'checking': return 'status-warning';
      default: return 'status-offline';
    }
  };

  const getApiStatusText = () => {
    switch (apiStatus) {
      case 'connected': return 'API Connected';
      case 'disconnected': return 'API Disconnected';
      case 'checking': return 'Checking...';
      default: return 'Unknown';
    }
  };

  return (
    <TooltipProvider>
      <header className={cn(
        "sticky top-0 z-40 w-full border-b bg-background/80 backdrop-blur-md supports-[backdrop-filter]:bg-background/80",
        className
      )}>
        <div className="flex h-16 items-center justify-between px-4 md:px-6">
          {/* Left section */}
          <div className="flex items-center gap-4">
            <SidebarTrigger className="h-8 w-8" />
            <Separator orientation="vertical" className="h-6" />
            
            {/* Status indicators */}
            <div className="flex items-center gap-3">
              <Tooltip>
                <TooltipTrigger asChild>
                  <div className="flex items-center gap-2">
                    <div className={cn("status-dot", isOnline ? "status-online" : "status-offline")} />
                    {isOnline ? (
                      <Wifi className="h-4 w-4 text-green-400" />
                    ) : (
                      <WifiOff className="h-4 w-4 text-red-400" />
                    )}
                  </div>
                </TooltipTrigger>
                <TooltipContent>
                  {isOnline ? 'Online' : 'Offline'}
                </TooltipContent>
              </Tooltip>

              <Tooltip>
                <TooltipTrigger asChild>
                  <div className="flex items-center gap-2">
                    <div className={cn("status-dot", getApiStatusColor())} />
                    <Activity className="h-4 w-4" />
                  </div>
                </TooltipTrigger>
                <TooltipContent>
                  {getApiStatusText()}
                </TooltipContent>
              </Tooltip>
            </div>

            {/* Market info */}
            <div className="hidden md:flex items-center gap-2">
              <Badge variant="outline" className="flex items-center gap-1">
                <TrendingUp className="h-3 w-3" />
                Market
              </Badge>
            </div>
          </div>

          {/* Center section - Page title or navigation */}
          <div className="flex-1 flex justify-center">
            <div className="text-sm text-muted-foreground">
              {isMounted ? `Last updated: ${lastUpdate.toLocaleTimeString()}` : 'Loading...'}
            </div>
          </div>

          {/* Right section */}
          <div className="flex items-center gap-2">
            {/* Refresh button */}
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleRefresh}
                  className="h-8 w-8 p-0"
                >
                  <RefreshCw className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                Refresh Data
              </TooltipContent>
            </Tooltip>

            {/* Notifications */}
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-8 w-8 p-0 relative"
                >
                  <Bell className="h-4 w-4" />
                  <div className="absolute -top-1 -right-1 h-2 w-2 bg-red-500 rounded-full" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                Notifications (2)
              </TooltipContent>
            </Tooltip>

            {/* Quick actions */}
            <div className="hidden lg:flex items-center gap-1">
              <Link href="/settings">
                <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                  <Settings className="h-4 w-4" />
                </Button>
              </Link>
            </div>

            <Separator orientation="vertical" className="h-6" />

            {/* User menu */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" className="h-8 w-8 rounded-full p-0">
                  <Avatar className="h-8 w-8">
                    {!isMounted ? (
                      // Show consistent fallback during SSR and initial client render
                      <AvatarFallback className="text-xs">
                        <User className="h-4 w-4" />
                      </AvatarFallback>
                    ) : userAvatar ? (
                      <>
                        <AvatarImage
                          src={userAvatar}
                          alt={userFullName || "User"}
                        />
                        <AvatarFallback className="text-xs">
                          {userFullName ? userFullName.substring(0, 1).toUpperCase() : "U"}
                        </AvatarFallback>
                      </>
                    ) : (
                      <>
                        {(() => {
                          const SelectedIconComponent = userSelectedIcon
                            ? availableIcons.find(icon => icon.name === userSelectedIcon)?.IconComponent || UserCircle
                            : UserCircle;
                          return <SelectedIconComponent className="h-full w-full p-1 text-primary" />;
                        })()}
                        <AvatarFallback className="text-xs">
                          {userFullName ? userFullName.substring(0, 1).toUpperCase() : <User className="h-4 w-4" />}
                        </AvatarFallback>
                      </>
                    )}
                  </Avatar>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-56">
                <DropdownMenuLabel className="font-normal">
                  <div className="flex flex-col space-y-1">
                    <p className="text-sm font-medium leading-none">
                      {isMounted ? (userFullName || "Trader") : "Trader"}
                    </p>
                    <p className="text-xs leading-none text-muted-foreground">
                      API Status: {getApiStatusText()}
                    </p>
                  </div>
                </DropdownMenuLabel>
                <DropdownMenuSeparator />
                
                <DropdownMenuItem asChild>
                  <Link href="/profile" className="flex items-center gap-2">
                    <User className="h-4 w-4" />
                    Profile
                  </Link>
                </DropdownMenuItem>
                
                <DropdownMenuItem asChild>
                  <Link href="/settings" className="flex items-center gap-2">
                    <Settings className="h-4 w-4" />
                    Settings
                  </Link>
                </DropdownMenuItem>

                <DropdownMenuItem onClick={toggleTheme} className="flex items-center gap-2">
                  {isDarkMode ? (
                    <Sun className="h-4 w-4" />
                  ) : (
                    <Moon className="h-4 w-4" />
                  )}
                  Toggle Theme
                </DropdownMenuItem>

                <DropdownMenuSeparator />
                
                <DropdownMenuItem 
                  onClick={handleLogout}
                  className="flex items-center gap-2 text-red-400 focus:text-red-400"
                >
                  <LogOut className="h-4 w-4" />
                  Logout
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>

        {/* Bottom status bar */}
        <div className="border-t bg-muted/30 px-4 py-2">
          <div className="flex items-center justify-between text-xs text-muted-foreground">
            <div className="flex items-center gap-4">
              <span>CryptoPilot v2.0</span>
              <div className="flex items-center gap-1">
                <DollarSign className="h-3 w-3" />
                <span>Portfolio synced</span>
              </div>
            </div>
            <div className="flex items-center gap-4">
              <span>Trading enabled</span>
              <span>Real-time data</span>
            </div>
          </div>
        </div>
      </header>
    </TooltipProvider>
  );
}
    