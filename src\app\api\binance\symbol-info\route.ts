
import type { NextRequest } from 'next/server';
import { NextResponse } from 'next/server';
import Binance from 'node-binance-api';

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const symbol = searchParams.get('symbol');

  if (!symbol) {
    return NextResponse.json({ success: false, message: "Parâmetro 'symbol' é obrigatório." }, { status: 400 });
  }

  const binance = new Binance();

  try {
    // Get both spot and futures exchange info
    const exchangeInfo = await binance.exchangeInfo();
    let futuresExchangeInfo;
    try {
      futuresExchangeInfo = await binance.futuresExchangeInfo();
    } catch (e) {
      console.warn("Não foi possível buscar informações de futuros:", e);
    }

    if (!exchangeInfo || !exchangeInfo.symbols) {
      return NextResponse.json({ success: false, message: "Falha ao buscar informações da exchange." }, { status: 500 });
    }

    let symbolInfo = exchangeInfo.symbols.find((s: any) => s.symbol === symbol.toUpperCase());
    const futuresSymbolInfo = futuresExchangeInfo?.symbols?.find((s: any) => s.symbol === symbol.toUpperCase());

    // Prefer futures info if available
    if (futuresSymbolInfo) {
      symbolInfo = futuresSymbolInfo;
    }

    if (!symbolInfo) {
      return NextResponse.json({ success: false, message: `Símbolo ${symbol} não encontrado.` }, { status: 404 });
    }

    let minNotional = '5'; // Default fallback
    
    // First try to get from futures exchange info if available
    if (futuresSymbolInfo) {
      const futuresMinNotionalFilter = futuresSymbolInfo.filters?.find((f: any) => f.filterType === 'MIN_NOTIONAL' || f.filterType === 'NOTIONAL');
      if (futuresMinNotionalFilter) {
        if (futuresMinNotionalFilter.notional) {
          minNotional = futuresMinNotionalFilter.notional;
        } else if (futuresMinNotionalFilter.minNotional) {
          minNotional = futuresMinNotionalFilter.minNotional;
        }
      }
    }
    
    // Fallback to spot if futures not available
    if (minNotional === '5') {
      const spotMinNotionalFilter = symbolInfo.filters?.find((f: any) => f.filterType === 'MIN_NOTIONAL' || f.filterType === 'NOTIONAL');
      if (spotMinNotionalFilter) {
        if (spotMinNotionalFilter.notional) {
          minNotional = spotMinNotionalFilter.notional;
        } else if (spotMinNotionalFilter.minNotional) {
          minNotional = spotMinNotionalFilter.minNotional;
        }
      }
    }

    // Special handling for BTCUSDT futures - ensure minimum is at least 10 USDT
    if (symbol.toUpperCase() === 'BTCUSDT' && futuresSymbolInfo) {
      const currentMinNotional = parseFloat(minNotional);
      if (currentMinNotional < 10) {
        minNotional = '10';
        console.log(`BTCUSDT futures: Adjusting minNotional from ${currentMinNotional} to 10 USDT`);
      }
    }


    let lotSizeInfo: { minQty: string | null; maxQty: string | null; stepSize: string | null } = { 
        minQty: null, 
        maxQty: null, 
        stepSize: null 
    };
    const lotSizeFilter = symbolInfo.filters?.find((f: any) => f.filterType === 'LOT_SIZE');
    
    if (lotSizeFilter && lotSizeFilter.stepSize && parseFloat(lotSizeFilter.stepSize) > 0 && lotSizeFilter.minQty && parseFloat(lotSizeFilter.minQty) >= 0) {
        lotSizeInfo = {
            minQty: lotSizeFilter.minQty,
            maxQty: lotSizeFilter.maxQty,
            stepSize: lotSizeFilter.stepSize,
        };
    } else if (lotSizeFilter) {
        console.warn(`Símbolo ${symbol}: Filtro LOT_SIZE encontrado, mas stepSize ('${lotSizeFilter.stepSize}') ou minQty ('${lotSizeFilter.minQty}') são inválidos. Dados de lotSize serão tratados como ausentes.`);
    }
    
    let priceFilterInfo: { minPrice: string | null; maxPrice: string | null; tickSize: string | null } = { 
        minPrice: null, 
        maxPrice: null, 
        tickSize: null 
    };
    const priceFilter = symbolInfo.filters?.find((f: any) => f.filterType === 'PRICE_FILTER');
    if (priceFilter && priceFilter.tickSize && parseFloat(priceFilter.tickSize) > 0) {
        priceFilterInfo = {
            minPrice: priceFilter.minPrice,
            maxPrice: priceFilter.maxPrice,
            tickSize: priceFilter.tickSize,
        };
    } else if (priceFilter) {
        console.warn(`Símbolo ${symbol}: Filtro PRICE_FILTER encontrado, mas tickSize ('${priceFilter?.tickSize}') é inválido. Dados de priceFilter (especialmente tickSize) serão tratados como ausentes.`);
    }


    return NextResponse.json({ 
        success: true, 
        data: { 
            symbol: symbolInfo.symbol,
            minNotional: minNotional,
            lotSize: lotSizeInfo,
            priceFilter: priceFilterInfo, // Includes tickSize
        } 
    });

  } catch (error: any) {
    console.error("Erro ao buscar informações do símbolo na Binance:", error.message || error);
    let errorMessage = "Falha ao buscar informações do símbolo.";
     if (error && error.body && typeof error.body === 'string') {
        try {
            const errorBody = JSON.parse(error.body);
            errorMessage = `Erro da API Binance: ${errorBody.msg || 'Não foi possível obter informações do símbolo.'}`;
        } catch (parseError) {
             errorMessage = `Erro da API Binance: ${error.body}`;
        }
    } else if (error && error.message) {
        errorMessage = error.message;
    }
    return NextResponse.json({ success: false, message: errorMessage }, { status: 500 });
  }
}
