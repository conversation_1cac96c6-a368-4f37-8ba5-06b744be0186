"use client";

import { cn } from "@/lib/utils";
import { LucideIcon } from "lucide-react";

export interface StatusIndicatorProps {
  status: "online" | "offline" | "warning" | "error" | "success";
  label?: string;
  icon?: LucideIcon;
  size?: "sm" | "md" | "lg";
  showDot?: boolean;
  className?: string;
}

const statusConfig = {
  online: {
    color: "text-green-400",
    bgColor: "bg-green-400",
    borderColor: "border-green-400",
    dotClass: "status-online",
  },
  offline: {
    color: "text-red-400", 
    bgColor: "bg-red-400",
    borderColor: "border-red-400",
    dotClass: "status-offline",
  },
  warning: {
    color: "text-yellow-400",
    bgColor: "bg-yellow-400", 
    borderColor: "border-yellow-400",
    dotClass: "status-warning",
  },
  error: {
    color: "text-red-500",
    bgColor: "bg-red-500",
    borderColor: "border-red-500", 
    dotClass: "status-offline",
  },
  success: {
    color: "text-green-500",
    bgColor: "bg-green-500",
    borderColor: "border-green-500",
    dotClass: "status-online",
  },
};

const sizeConfig = {
  sm: {
    dot: "w-2 h-2",
    icon: "h-3 w-3",
    text: "text-xs",
  },
  md: {
    dot: "w-3 h-3", 
    icon: "h-4 w-4",
    text: "text-sm",
  },
  lg: {
    dot: "w-4 h-4",
    icon: "h-5 w-5", 
    text: "text-base",
  },
};

export function StatusIndicator({
  status,
  label,
  icon: Icon,
  size = "md",
  showDot = true,
  className,
}: StatusIndicatorProps) {
  const statusStyles = statusConfig[status];
  const sizeStyles = sizeConfig[size];

  return (
    <div className={cn("flex items-center gap-2", className)}>
      {showDot && (
        <div className={cn("status-dot", statusStyles.dotClass, sizeStyles.dot)} />
      )}
      
      {Icon && (
        <Icon className={cn(statusStyles.color, sizeStyles.icon)} />
      )}
      
      {label && (
        <span className={cn(statusStyles.color, sizeStyles.text, "font-medium")}>
          {label}
        </span>
      )}
    </div>
  );
}

export function StatusBadge({
  status,
  label,
  icon: Icon,
  size = "md",
  className,
}: Omit<StatusIndicatorProps, "showDot">) {
  const statusStyles = statusConfig[status];
  const sizeStyles = sizeConfig[size];

  return (
    <div className={cn(
      "inline-flex items-center gap-1.5 px-2 py-1 rounded-full border",
      statusStyles.borderColor,
      "bg-opacity-10",
      statusStyles.bgColor,
      className
    )}>
      <div className={cn("status-dot", statusStyles.dotClass, sizeStyles.dot)} />
      
      {Icon && (
        <Icon className={cn(statusStyles.color, sizeStyles.icon)} />
      )}
      
      {label && (
        <span className={cn(statusStyles.color, sizeStyles.text, "font-medium")}>
          {label}
        </span>
      )}
    </div>
  );
} 