import type { NextRequest } from 'next/server';
import { NextResponse } from 'next/server';
import Binance from 'node-binance-api';

export async function POST(request: NextRequest) {
  try {
    let body;
    try {
      body = await request.json();
    } catch (parseError) {
      console.error("Erro ao fazer parse do JSON da requisição:", parseError);
      return NextResponse.json({ success: false, message: "Corpo da requisição inválido ou ausente." }, { status: 400 });
    }
    
    const { apiKey, apiSecret, symbol, stopLoss, takeProfit, positionSide } = body;

    if (!apiKey || !apiSecret) {
      return NextResponse.json({ success: false, message: "API Key ou Secret ausentes." }, { status: 400 });
    }

    if (!symbol) {
      return NextResponse.json({ success: false, message: "Símbolo é obrigatório." }, { status: 400 });
    }

    if (!stopLoss && !takeProfit) {
      return NextResponse.json({ success: false, message: "<PERSON>elo menos Stop Loss ou Take Profit deve ser especificado." }, { status: 400 });
    }

    const binance = new Binance().options({
      APIKEY: apiKey,
      APISECRET: apiSecret,
      family: 4,
    });

    console.log(`========= MODIFY POSITION - INCOMING REQUEST =========`);
    console.log(`Symbol: ${symbol}`);
    console.log(`Stop Loss: ${stopLoss}`);
    console.log(`Take Profit: ${takeProfit}`);
    console.log(`Position Side: ${positionSide || 'BOTH'}`);
    console.log(`=====================================================`);

    // Get current position information
    const positions = await binance.futuresPositionRisk({ symbol });
    const currentPosition = positions.find((pos: any) => 
      pos.symbol === symbol && 
      (positionSide ? pos.positionSide === positionSide : parseFloat(pos.positionAmt) !== 0)
    );

    if (!currentPosition || parseFloat(currentPosition.positionAmt) === 0) {
      return NextResponse.json({ 
        success: false, 
        message: `Nenhuma posição aberta encontrada para ${symbol}` 
      }, { status: 400 });
    }

    const positionAmount = parseFloat(currentPosition.positionAmt);
    const isLongPosition = positionAmount > 0;
    const positionQuantity = Math.abs(positionAmount);

    // Get current orders to cancel existing stop/take profit orders
    const openOrders = await binance.futuresOpenOrders({ symbol });
    const existingOrders = openOrders.filter((order: any) => 
      (order.type === 'STOP_MARKET' || order.type === 'TAKE_PROFIT_MARKET') &&
      order.symbol === symbol
    );

    // Cancel existing stop/take profit orders
    for (const order of existingOrders) {
      try {
        await binance.futuresCancelOrder({
          symbol: order.symbol,
          orderId: order.orderId
        });
        console.log(`Cancelled existing order ${order.orderId} (${order.type})`);
      } catch (cancelError: any) {
        console.warn(`Failed to cancel order ${order.orderId}:`, cancelError.message);
      }
    }

    const results = [];

    // Place Stop Loss order
    if (stopLoss) {
      try {
        const stopLossOrder = await binance.futuresOrder({
          symbol: symbol,
          side: isLongPosition ? 'SELL' : 'BUY',
          type: 'STOP_MARKET',
          quantity: positionQuantity.toString(),
          stopPrice: parseFloat(stopLoss).toString(),
          reduceOnly: true,
          timeInForce: 'GTC',
          workingType: 'MARK_PRICE'
        });

        results.push({
          type: 'STOP_LOSS',
          orderId: stopLossOrder.orderId,
          status: stopLossOrder.status,
          price: stopLoss
        });

        console.log(`Stop Loss order placed successfully: ${stopLossOrder.orderId} at ${stopLoss}`);
      } catch (slError: any) {
        console.error('Error placing Stop Loss order:', slError);
        return NextResponse.json({ 
          success: false, 
          message: `Erro ao colocar Stop Loss: ${slError.body ? JSON.parse(slError.body).msg : slError.message}` 
        }, { status: 400 });
      }
    }

    // Place Take Profit order
    if (takeProfit) {
      try {
        const takeProfitOrder = await binance.futuresOrder({
          symbol: symbol,
          side: isLongPosition ? 'SELL' : 'BUY',
          type: 'TAKE_PROFIT_MARKET',
          quantity: positionQuantity.toString(),
          stopPrice: parseFloat(takeProfit).toString(),
          reduceOnly: true,
          timeInForce: 'GTC',
          workingType: 'MARK_PRICE'
        });

        results.push({
          type: 'TAKE_PROFIT',
          orderId: takeProfitOrder.orderId,
          status: takeProfitOrder.status,
          price: takeProfit
        });

        console.log(`Take Profit order placed successfully: ${takeProfitOrder.orderId} at ${takeProfit}`);
      } catch (tpError: any) {
        console.error('Error placing Take Profit order:', tpError);
        return NextResponse.json({ 
          success: false, 
          message: `Erro ao colocar Take Profit: ${tpError.body ? JSON.parse(tpError.body).msg : tpError.message}` 
        }, { status: 400 });
      }
    }

    return NextResponse.json({
      success: true,
      message: `Stop Loss/Take Profit configurado com sucesso para ${symbol}`,
      data: {
        symbol,
        positionSide: currentPosition.positionSide || 'BOTH',
        positionAmount: positionAmount,
        orders: results
      }
    });

  } catch (error: any) {
    console.error("Erro interno ao modificar posição:", error);
    
    if (error && error.body && typeof error.body === 'string') {
      try {
        const errorBody = JSON.parse(error.body);
        if (errorBody.msg) {
          return NextResponse.json({ 
            success: false, 
            message: `Erro da API Binance: ${errorBody.msg}` 
          }, { status: 401 });
        }
      } catch (parseError) { /* Ignore parse error, fall through to generic */ }
    }
    
    return NextResponse.json({ 
      success: false, 
      message: "Falha interna ao modificar posição." 
    }, { status: 500 });
  }
} 