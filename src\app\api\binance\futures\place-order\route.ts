
import type { NextRequest } from 'next/server';
import { NextResponse } from 'next/server';
import Binance from 'node-binance-api';
import crypto from 'crypto';

interface OrderRequest {
  symbol: string;
  orderType: 'market' | 'limit';
  side: 'buy' | 'sell';
  quantity: number; // This is the USDT amount from frontend (represents orderUsdtAmount)
  price?: number; // This is the limit price from frontend
  leverage: number;
  apiKey: string;
  apiSecret: string;
  stepSize: string | null; // Provided by frontend from symbol-info
  minQty: string | null;   // Provided by frontend from symbol-info
  tickSize?: string | null; // Provided by frontend from symbol-info for limit orders
}


// Helper function to get the number of decimal places from a string like "0.001" (returns 3) or "1" (returns 0) or "1e-5" (returns 5)
// This version normalizes the number first to correctly determine significant decimal places.
function getDecimalPlaces(valueString: string | null | undefined): number {
  console.log(`getDecimalPlaces: Called with valueString = '${valueString}'`);
  if (!valueString) {
    console.warn(`getDecimalPlaces: received null or empty valueString. Defaulting to 0 decimal places.`);
    return 0;
  }

  // First, check for e-notation in the original string, as parseFloat can alter it.
  if (valueString.toLowerCase().includes('e-')) {
    const parts = valueString.toLowerCase().split('e-');
    if (parts.length === 2) {
      const exponent = parseInt(parts[1]);
      if (!isNaN(exponent) && exponent > 0) {
        console.log(`getDecimalPlaces (e-notation original): For ${valueString}, decimal places: ${exponent}`);
        return exponent;
      }
    }
  }

  // Normalize the string by parsing to float and converting back to string.
  // This handles cases like "0.0100" -> 0.01, "1.0" -> 1.
  const num = parseFloat(valueString);
  if (isNaN(num)) {
    console.warn(`getDecimalPlaces: Could not parse '${valueString}' as float. Defaulting to 0 decimal places.`);
    return 0;
  }

  const normalizedNumString = num.toString();
  console.log(`getDecimalPlaces: Original '${valueString}', Parsed num: ${num}, Normalized num string: '${normalizedNumString}'`);


  // Check for e-notation in the normalized string (e.g., if original was "0.00000001")
  if (normalizedNumString.toLowerCase().includes('e-')) {
    const parts = normalizedNumString.toLowerCase().split('e-');
    if (parts.length === 2) {
      const exponent = parseInt(parts[1]);
      if (!isNaN(exponent) && exponent > 0) {
        console.log(`getDecimalPlaces (e-notation normalized): For ${valueString} (norm: ${normalizedNumString}), decimal places: ${exponent}`);
        return exponent;
      }
    }
  }

  // Check for decimal part in the normalized string
  if (normalizedNumString.includes('.')) {
    const decimalPart = normalizedNumString.split('.')[1];
    const dp = decimalPart ? decimalPart.length : 0;
    console.log(`getDecimalPlaces (decimal): For ${valueString} (norm: ${normalizedNumString}), decimal places: ${dp}`);
    return dp;
  }

  // If no decimal point and no e-notation, it's an integer.
  console.log(`getDecimalPlaces (integer): For ${valueString} (norm: ${normalizedNumString}), decimal places: 0`);
  return 0;
}


export async function POST(request: NextRequest) {
  try {
    const body = await request.json() as OrderRequest;
    const {
      symbol,
      orderType,
      side,
      quantity: usdtAmount, // This is the USDT amount from frontend 'quantity'
      price: limitPriceInput, // This is the limit price from frontend 'price'
      leverage,
      apiKey,
      apiSecret,
      stepSize, // For quantity precision
      minQty,   // For minimum order quantity
      tickSize, // For price precision (limit orders)
    } = body;

    console.log("========= PLACE ORDER - INCOMING REQUEST (V8 - Pass Number to Lib) =========");
    console.log("Symbol:", symbol);
    console.log("Order Type:", orderType);
    console.log("Side:", side);
    console.log("USDT Amount (from frontend 'quantity'):", usdtAmount);
    console.log("Limit Price Input (from frontend 'price'):", limitPriceInput);
    console.log("Leverage:", leverage);
    console.log("Received stepSize:", stepSize);
    console.log("Received minQty:", minQty);
    console.log("Received tickSize:", tickSize);
    console.log("==========================================================================");


    if (!apiKey || !apiSecret) {
      return NextResponse.json({
        success: false,
        message: "Erro de Configuração: Chaves de API da Binance não fornecidas na requisição."
      }, { status: 400 });
    }

    if (!symbol || !orderType || !side || usdtAmount === undefined || usdtAmount <= 0 || !leverage || leverage < 1) {
      return NextResponse.json({
        success: false,
        message: "Dados da ordem inválidos. Verifique os campos obrigatórios."
      }, { status: 400 });
    }

    // Get symbol info to validate minimum notional
    let symbolMinNotional = 5; // Default fallback
    try {
      const binanceExchangeInfo = new Binance();
      const futuresExchangeInfo = await binanceExchangeInfo.futuresExchangeInfo();
      if (futuresExchangeInfo && futuresExchangeInfo.symbols) {
        const futuresSymbolInfo = futuresExchangeInfo.symbols.find((s: any) => s.symbol === symbol.toUpperCase());
        if (futuresSymbolInfo) {
          const minNotionalFilter = futuresSymbolInfo.filters?.find((f: any) => f.filterType === 'MIN_NOTIONAL' || f.filterType === 'NOTIONAL');
          if (minNotionalFilter && minNotionalFilter.notional) {
            symbolMinNotional = parseFloat(minNotionalFilter.notional);
          } else if (minNotionalFilter && minNotionalFilter.minNotional) {
            symbolMinNotional = parseFloat(minNotionalFilter.minNotional);
          }
          
          // Special handling for BTCUSDT
          if (symbol.toUpperCase() === 'BTCUSDT' && symbolMinNotional < 10) {
            symbolMinNotional = 10;
          }
        }
      }
    } catch (e) {
      console.warn("Aviso: Não foi possível buscar informações de exchange para validação de minNotional:", e);
    }

    // Add minimum USDT amount validation based on symbol's actual minimum
    if (usdtAmount < symbolMinNotional) {
      return NextResponse.json({
        success: false,
        message: `O valor mínimo para ordens de ${symbol} é de ${symbolMinNotional.toFixed(2)} USDT.`
      }, { status: 400 });
    }

    if (orderType === 'limit' && (limitPriceInput === undefined || limitPriceInput <= 0)) {
        return NextResponse.json({
        success: false,
        message: "Preço é obrigatório e deve ser positivo para ordens limite."
      }, { status: 400 });
    }

    if (!stepSize) {
        console.error(`PLACE ORDER ERROR: StepSize não fornecido para ${symbol}.`);
        return NextResponse.json({ success: false, message: `StepSize não fornecido para o símbolo ${symbol}. Não é possível calcular a quantidade da ordem.` }, { status: 400 });
    }
    const actualStepSize = parseFloat(stepSize);
    if (isNaN(actualStepSize) || actualStepSize <= 0) {
        console.error(`PLACE ORDER ERROR: StepSize inválido ou não positivo ('${stepSize}', parsed: ${actualStepSize}) para ${symbol}.`);
        return NextResponse.json({ success: false, message: `Valor de stepSize ('${stepSize}') inválido ou não positivo para o símbolo ${symbol}. Não é possível calcular a quantidade da ordem.` }, { status: 400 });
    }

    if (minQty === null || minQty === undefined) {
        console.error(`PLACE ORDER ERROR: MinQty não fornecido para ${symbol}.`);
        return NextResponse.json({ success: false, message: `MinQty não fornecido para o símbolo ${symbol}.` }, { status: 400 });
    }
    const actualMinQty = parseFloat(minQty);
    if (isNaN(actualMinQty) || actualMinQty < 0) { // minQty can be 0 for some assets
        console.error(`PLACE ORDER ERROR: MinQty inválido ('${minQty}', parsed: ${actualMinQty}) para ${symbol}.`);
        return NextResponse.json({ success: false, message: `Valor de minQty ('${minQty}') inválido para o símbolo ${symbol}.` }, { status: 400 });
    }
    
    let actualTickSize: number | null = null;
    if (orderType === 'limit') {
        if (!tickSize) {
            console.error(`PLACE ORDER ERROR: TickSize não fornecido para ordem limite em ${symbol}.`);
            return NextResponse.json({ success: false, message: `TickSize não fornecido para ordem limite em ${symbol}. Não é possível formatar o preço.` }, { status: 400 });
        }
        actualTickSize = parseFloat(tickSize);
        if (isNaN(actualTickSize) || actualTickSize <= 0) {
            console.error(`PLACE ORDER ERROR: TickSize inválido ou não positivo ('${tickSize}', parsed: ${actualTickSize}) para ordem limite em ${symbol}.`);
            return NextResponse.json({ success: false, message: `TickSize inválido ou não positivo ('${tickSize}') para ordem limite em ${symbol}. Não é possível formatar o preço.` }, { status: 400 });
        }
    }
    
    console.log("Parsed actualStepSize:", actualStepSize);
    console.log("Parsed actualMinQty:", actualMinQty);
    if(actualTickSize !== null) console.log("Parsed actualTickSize:", actualTickSize);


    const binance = new Binance().options({
      APIKEY: apiKey,
      APISECRET: apiSecret,
      family: 4,
    });

    try {
      console.log(`Tentando definir alavancagem para ${symbol} para ${leverage}x`);
      await binance.futuresLeverage(symbol, leverage);
      console.log(`Alavancagem definida com sucesso para ${symbol} para ${leverage}x`);
    } catch (leverageError: any) {
      console.error(`Falha ao definir alavancagem para ${symbol} para ${leverage}x:`, leverageError.body || leverageError.message || leverageError);
      const errorMsg = leverageError.body ? (typeof leverageError.body === 'string' ? JSON.parse(leverageError.body).msg : leverageError.body.msg) : leverageError.message;
      return NextResponse.json({
        success: false,
        message: `Falha ao definir alavancagem: ${errorMsg || 'Erro desconhecido ao definir alavancagem.'}`
      }, { status: 500 });
    }

    let orderExecutionPrice: number | undefined = limitPriceInput;

    if (orderType === 'market') {
      try {
        const ticker = await binance.futuresMarkPrice(symbol);
        if (!ticker || !ticker.markPrice) {
          throw new Error('Não foi possível obter o preço de referência (mark price).');
        }
        orderExecutionPrice = parseFloat(ticker.markPrice);
        console.log(`Market Order: Fetched Mark Price for ${symbol}: ${orderExecutionPrice}`);
      } catch (priceError: any) {
        console.error(`Falha ao obter preço de mercado para ${symbol}:`, priceError.message || priceError);
        return NextResponse.json({
          success: false,
          message: `Falha ao obter preço de mercado para ${symbol}: ${priceError.message || 'Erro desconhecido.'}`
        }, { status: 500 });
      }
    } else { 
        if (limitPriceInput === undefined || limitPriceInput <= 0) { 
            console.error("PLACE ORDER ERROR: Preço limite inválido para ordem limite (cálculo de quantidade):", limitPriceInput);
            return NextResponse.json({ success: false, message: "Preço limite inválido ou não fornecido para ordem limite (cálculo de quantidade)." }, { status: 400 });
        }
        orderExecutionPrice = limitPriceInput; 
        console.log(`Limit Order: Using input limit price for quantity calculation: ${orderExecutionPrice}`);
    }

    if (orderExecutionPrice === undefined || orderExecutionPrice <= 0) {
        console.error("PLACE ORDER ERROR: Preço de execução inválido para cálculo da quantidade:", orderExecutionPrice);
        return NextResponse.json({ success: false, message: "Preço de execução inválido para cálculo da quantidade." }, { status: 500 });
    }

    // Calculate minimum USDT value needed based on current price and minQty
    const minNotionalValue = actualMinQty * orderExecutionPrice;
    if (usdtAmount < minNotionalValue) {
        console.error(`PLACE ORDER ERROR: USDT amount ${usdtAmount} is less than minimum notional value ${minNotionalValue.toFixed(2)} for ${symbol}`);
        return NextResponse.json({
            success: false,
            message: `O valor de ${usdtAmount.toFixed(2)} USDT é insuficiente. O valor mínimo necessário para ${symbol} é de ${minNotionalValue.toFixed(2)} USDT.`
        }, { status: 400 });
    }

    const baseAssetQuantityNotRounded = usdtAmount / orderExecutionPrice;
    console.log("Base Asset Quantity (Not Rounded, from USDT Amount / Order Exec Price):", baseAssetQuantityNotRounded);

    // Check if the base quantity is too small before adjusting
    if (baseAssetQuantityNotRounded < actualMinQty) {
      console.warn(`Base quantity ${baseAssetQuantityNotRounded} is smaller than minQty ${actualMinQty}. Using minQty as base.`);
    }

    // Round to step size properly - use Math.round instead of Math.floor for better precision
    let quantityAdjustedForStep = Math.round(baseAssetQuantityNotRounded / actualStepSize) * actualStepSize;
    
    // Ensure we meet minimum quantity requirements
    if (quantityAdjustedForStep < actualMinQty) {
      quantityAdjustedForStep = actualMinQty;
      console.log(`Adjusted quantity to minimum required: ${quantityAdjustedForStep}`);
    }
    
    console.log("Quantity Adjusted for StepSize (numeric):", quantityAdjustedForStep);

    const quantityDecimalPlaces = getDecimalPlaces(stepSize);
    const quantityToSend = quantityAdjustedForStep.toFixed(quantityDecimalPlaces);
    console.log("Quantity to Send to Binance API (formatted string):", quantityToSend);

    // Use the final quantity that will be sent (already adjusted for minQty)
    const finalQuantity = parseFloat(quantityToSend);
    
    if (finalQuantity < actualMinQty) {
      const quantityDecimalPlacesForMinQtyDisplay = getDecimalPlaces(minQty); // For user-friendly display
      const quantityDecimalPlacesForQtyAdjustedDisplay = getDecimalPlaces(stepSize);
      
      console.error(`PLACE ORDER ERROR: Final quantity ${finalQuantity.toFixed(quantityDecimalPlacesForQtyAdjustedDisplay)} is still less than minQty ${actualMinQty.toFixed(quantityDecimalPlacesForMinQtyDisplay)} for ${symbol}. Initial USDT: ${usdtAmount}, Price: ${orderExecutionPrice}, BaseQtyNotRounded: ${baseAssetQuantityNotRounded}`);
      return NextResponse.json({
        success: false,
        message: `O valor nocional de ${usdtAmount.toFixed(2)} USDT resulta em uma quantidade (${finalQuantity.toFixed(quantityDecimalPlacesForQtyAdjustedDisplay)}) que é menor que a quantidade mínima permitida (${actualMinQty.toFixed(quantityDecimalPlacesForMinQtyDisplay)}) para ${symbol}. Aumente o valor em USDT ou ajuste o preço para ordens limite.`
      }, { status: 400 });
    }

    if (finalQuantity <= 0) {
        console.error(`PLACE ORDER ERROR: Quantidade final do ativo base é muito pequena ou zero (${finalQuantity}).`);
        return NextResponse.json({ success: false, message: `Quantidade final do ativo base é muito pequena ou zero (${finalQuantity}). Aumente o valor em USDT.` }, { status: 400 });
    }

    let priceAdjustedForTick: number | undefined = undefined;
    let priceToSend: string | undefined = undefined;

    if (orderType === 'limit' && limitPriceInput !== undefined && actualTickSize !== null && actualTickSize > 0) { 
        priceAdjustedForTick = Math.floor(limitPriceInput / actualTickSize) * actualTickSize;
        console.log(`Price Adjusted for TickSize (numeric): ${priceAdjustedForTick}`);

        const priceDecimalPlaces = getDecimalPlaces(tickSize);
        priceToSend = priceAdjustedForTick.toFixed(priceDecimalPlaces);
        console.log("Price to Send to Binance API (formatted string):", priceToSend);
        if (isNaN(priceAdjustedForTick) || priceAdjustedForTick <=0) {
            console.error("PLACE ORDER ERROR: Preço limite ajustado inválido:", priceAdjustedForTick);
            return NextResponse.json({ success: false, message: `Preço limite ajustado inválido (${priceAdjustedForTick}) após aplicar precisão de tickSize ${tickSize}.` }, { status: 400 });
        }
    }


    let orderResult: any;
    try {
      const orderParamsLog: any = { 
          symbol, 
          side: side.toUpperCase(), 
          quantity: quantityToSend, // Pass formatted string
          type: orderType.toUpperCase() 
      };
      if (orderType === 'limit') {
        orderParamsLog.price = priceToSend; // Pass formatted string
      }
      console.log("========= ATTEMPTING TO PLACE ORDER ON BINANCE (V9 - Pass String to Lib) =========");
      console.log("Order Parameters for API Call (strings sent to lib):", orderParamsLog);

      if (orderType === 'market') {
        if (side === 'buy') {
          orderResult = await binance.futuresMarketBuy(symbol, quantityToSend);
        } else {
          orderResult = await binance.futuresMarketSell(symbol, quantityToSend);
        }
      } else { // limit
        if (priceToSend === undefined) { // Redundant check, but safe
             console.error("PLACE ORDER ERROR: Preço limite formatado (string) não fornecido para ordem limite (final check).");
             return NextResponse.json({ success: false, message: "Preço limite formatado (string) não fornecido para ordem limite." }, { status: 400 });
        }
        if (side === 'buy') {
          orderResult = await binance.futuresLimitBuy(symbol, quantityToSend, priceToSend);
        } else {
          orderResult = await binance.futuresLimitSell(symbol, quantityToSend, priceToSend);
        }
      }

      console.log("========= BINANCE API RESPONSE - ORDER PLACEMENT (V9 - Pass String to Lib) =========");
      console.log("Full orderResult from Binance:", JSON.stringify(orderResult, null, 2));
      console.log("===================================================================================");

      // Stricter check for success, ensuring orderId is a positive number and no error code
      const isOrderSuccessfullyPlaced = orderResult &&
                                      typeof orderResult.orderId === 'number' &&
                                      orderResult.orderId > 0 &&
                                      orderResult.code === undefined; // Binance errors often have a 'code' field

      if (isOrderSuccessfullyPlaced) {
        return NextResponse.json({
          success: true,
          message: `Ordem colocada com sucesso na Binance! (ID da Ordem Binance: ${orderResult.orderId})`,
          orderId: String(orderResult.orderId), 
          orderDetails: orderResult
        });
      } else {
        let binanceErrorMessage = "Falha ao colocar ordem na Binance. Resposta inesperada ou ID da ordem ausente/inválido.";
        let statusCode = 500;

        if (orderResult && orderResult.msg) {
          binanceErrorMessage = `Erro da Binance: ${orderResult.msg}`;
          if (orderResult.code) {
            binanceErrorMessage += ` (Código: ${orderResult.code})`;
            const errorCodeInt = parseInt(String(orderResult.code));
            if (errorCodeInt < 0 || 
                [-1111, -1013, -2010, -4003, -4014, -4164, -1102, -1121, -1104, -1105, -1106, -4081].includes(errorCodeInt)) { 
                 statusCode = 400; 
            }
          }
        } else if (orderResult && !isOrderSuccessfullyPlaced) { // No msg, but conditions for success not met
          binanceErrorMessage = "Falha ao colocar ordem: A Binance não retornou um ID de ordem válido ou retornou uma resposta que indica falha (sem mensagem de erro específica).";
          if(orderResult.orderId !== undefined) binanceErrorMessage += ` ID da Ordem Recebido: ${orderResult.orderId}.`;
          if(orderResult.code !== undefined) binanceErrorMessage += ` Código de Erro Recebido: ${orderResult.code}.`;
          console.warn("Unexpected orderResult structure or failed order (no error msg from Binance, but conditions not met):", orderResult);
        }


        console.error(`Falha ao colocar ordem. Binance response (msg: ${orderResult?.msg}, code: ${orderResult?.code}, orderId: ${orderResult?.orderId}). Full response:`, orderResult);
        return NextResponse.json({
          success: false,
          message: binanceErrorMessage,
          orderDetails: orderResult 
        }, { status: statusCode });
      }

    } catch (orderError: any) {
      console.error("Erro CRÍTICO ao colocar ordem na Binance (dentro do try/catch da chamada API):", orderError.body || orderError.message || orderError);
      let errorMsg = orderError.message || 'Erro desconhecido ao tentar colocar ordem.';
      let statusCode = 500;

      if (orderError.body) {
        try {
          const parsedBody = (typeof orderError.body === 'string') ? JSON.parse(orderError.body) : orderError.body;
          if (parsedBody && parsedBody.msg) {
            errorMsg = `Erro da Binance: ${parsedBody.msg}`;
            if (parsedBody.code) {
              errorMsg += ` (Código: ${parsedBody.code})`;
              const errorCodeInt = parseInt(String(parsedBody.code));
              if (errorCodeInt < 0 || [-1111, -1013, -2010, -4003, -4014, -4164, -1102, -1121, -1104, -1105, -1106, -4081].includes(errorCodeInt)) {
                  statusCode = 400;
              } else if ([ -1022, -2008, -2014, -2015].includes(errorCodeInt)) {
                  statusCode = 401; 
              }
            }
          }
        } catch (e) {
          console.error("Erro ao parsear corpo do erro da Binance:", e);
          errorMsg = orderError.body ? String(orderError.body) : orderError.message;
        }
      }

      return NextResponse.json({
        success: false,
        message: errorMsg
      }, { status: statusCode });
    }

  } catch (error: any) {
    console.error("Erro interno GERAL ao processar ordem (bloco try/catch externo):", error);
    return NextResponse.json({
      success: false,
      message: "Falha interna GERAL ao processar a ordem: " + (error.message || "Erro desconhecido.")
    }, { status: 500 });
  }
}

    