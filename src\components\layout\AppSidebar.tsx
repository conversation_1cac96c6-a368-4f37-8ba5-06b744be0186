"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";
import {
  Sidebar,
  SidebarHeader,
  SidebarContent,
  SidebarMenu,
  SidebarMenuItem,
  SidebarMenuButton,
  SidebarFooter,
} from "@/components/ui/sidebar";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { StatusIndicator } from "@/components/ui/status-indicator";
import { cn } from "@/lib/utils";
import { 
  LayoutDashboard, 
  Briefcase, 
  Newspaper, 
  GraduationCap, 
  Settings,
  Rocket,
  LogOut,
  TrendingUp,
  Activity,
  DollarSign,
  Zap,
  Target,
  Shield,
  HelpCircle,
  ExternalLink
} from "lucide-react";
import { Button } from "@/components/ui/button";

const mainNavItems = [
  { 
    href: "/", 
    label: "Dashboard", 
    icon: LayoutDashboard,
    description: "Portfolio overview and insights",
    badge: null
  },
  { 
    href: "/portfolio", 
    label: "Portfolio", 
    icon: Briefcase,
    description: "Manage your assets",
    badge: null
  },
  { 
    href: "/news", 
    label: "Market News", 
    icon: Newspaper,
    description: "Latest crypto news",
    badge: "New"
  },
];

const tradingNavItems = [
  { 
    href: "/trading", 
    label: "Trading Terminal", 
    icon: TrendingUp,
    description: "Advanced trading tools",
    badge: null
  },
  { 
    href: "/analytics", 
    label: "Analytics", 
    icon: Activity,
    description: "Performance metrics",
    badge: "Pro"
  },
  { 
    href: "/alerts", 
    label: "Price Alerts", 
    icon: Target,
    description: "Set price notifications",
    badge: null
  },
];

const learningNavItems = [
  { 
    href: "/onboarding", 
    label: "Getting Started", 
    icon: GraduationCap,
    description: "Learn the basics",
    badge: null
  },
  { 
    href: "/help", 
    label: "Help Center", 
    icon: HelpCircle,
    description: "Support and guides",
    badge: null
  },
];

const settingsNavItems = [
  { 
    href: "/settings", 
    label: "Settings", 
    icon: Settings,
    description: "Configure your account",
    badge: null
  },
];

interface NavSectionProps {
  title: string;
  items: typeof mainNavItems;
  pathname: string;
}

function NavSection({ title, items, pathname }: NavSectionProps) {
  return (
    <div className="space-y-2">
      <div className="px-2">
        <h4 className="text-xs font-semibold text-muted-foreground uppercase tracking-wider group-data-[collapsible=icon]:hidden">
          {title}
        </h4>
      </div>
      <div className="space-y-1">
        {items.map((item) => (
          <SidebarMenuItem key={item.href}>
            <Link href={item.href} passHref legacyBehavior>
              <SidebarMenuButton
                asChild
                isActive={pathname === item.href}
                className={cn(
                  "group relative w-full justify-start p-3 h-auto transition-all duration-200",
                  pathname === item.href && "bg-primary/10 text-primary hover:bg-primary/20 border-r-2 border-primary"
                )}
                tooltip={
                  <div className="space-y-1">
                    <div className="font-medium">{item.label}</div>
                    <div className="text-xs text-muted-foreground">{item.description}</div>
                  </div>
                }
              >
                <a className="flex items-center gap-3 w-full">
                  <div className="relative">
                    <item.icon className="h-5 w-5 transition-transform duration-200 group-hover:scale-110" />
                    {pathname === item.href && (
                      <div className="absolute -inset-1 bg-primary/20 rounded-full animate-pulse" />
                    )}
                  </div>
                  <div className="flex-1 group-data-[collapsible=icon]:hidden">
                    <div className="flex items-center justify-between">
                      <span className="font-medium">{item.label}</span>
                      {item.badge && (
                        <Badge 
                          variant="secondary" 
                          className="text-xs h-5 px-2 bg-primary/20 text-primary border-primary/30"
                        >
                          {item.badge}
                        </Badge>
                      )}
                    </div>
                    <div className="text-xs text-muted-foreground mt-0.5">
                      {item.description}
                    </div>
                  </div>
                </a>
              </SidebarMenuButton>
            </Link>
          </SidebarMenuItem>
        ))}
      </div>
    </div>
  );
}

export function AppSidebar() {
  const pathname = usePathname();

  return (
    <Sidebar 
      collapsible="icon" 
      variant="sidebar" 
      side="left" 
      className="border-r bg-sidebar-background"
      sheetTitle="CryptoPilot Menu"
    >
      {/* Header */}
      <SidebarHeader className="p-4 border-b">
        <Link href="/" passHref legacyBehavior>
          <a className="flex items-center gap-3 text-primary hover:text-primary/90 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-background rounded-md transition-colors group">
            <div className="relative">
              <div className="flex items-center justify-center h-10 w-10 group-data-[collapsible=icon]:h-8 group-data-[collapsible=icon]:w-8 bg-primary/10 rounded-lg border border-primary/20">
                <Rocket className="h-6 w-6 group-data-[collapsible=icon]:h-5 group-data-[collapsible=icon]:w-5 text-primary" />
              </div>
              <div className="absolute -top-1 -right-1 w-3 h-3 bg-green-500 rounded-full border-2 border-background pulse-glow group-data-[collapsible=icon]:hidden" />
            </div>
            <div className="group-data-[collapsible=icon]:hidden">
              <h1 className="text-2xl font-bold">CryptoPilot</h1>
              <p className="text-xs text-muted-foreground">v2.0 • Professional</p>
            </div>
          </a>
        </Link>
      </SidebarHeader>

      {/* Content */}
      <SidebarContent className="p-2">
        <ScrollArea className="h-full">
          <div className="space-y-6">
            <NavSection title="Main" items={mainNavItems} pathname={pathname} />
            
            <Separator className="mx-2" />
            
            <NavSection title="Trading" items={tradingNavItems} pathname={pathname} />
            
            <Separator className="mx-2" />
            
            <NavSection title="Learn" items={learningNavItems} pathname={pathname} />
          </div>
        </ScrollArea>
      </SidebarContent>

      {/* Footer */}
      <SidebarFooter className="p-2 border-t space-y-2">
        {/* Quick Stats */}
        <div className="px-2 py-3 bg-muted/30 rounded-lg group-data-[collapsible=icon]:hidden">
          <div className="space-y-2">
            <div className="flex items-center justify-between text-xs">
              <span className="text-muted-foreground">Portfolio</span>
              <StatusIndicator status="online" size="sm" />
            </div>
            <div className="flex items-center gap-2">
              <DollarSign className="h-3 w-3 text-green-400" />
              <span className="text-sm font-mono font-semibold">$12,847.92</span>
            </div>
            <div className="flex items-center gap-2">
              <TrendingUp className="h-3 w-3 text-green-400" />
              <span className="text-xs text-green-400 font-medium">+2.47%</span>
              <span className="text-xs text-muted-foreground">24h</span>
            </div>
          </div>
        </div>

        {/* Settings and Logout */}
        <SidebarMenu>
          {settingsNavItems.map((item) => (
            <SidebarMenuItem key={item.href}>
              <Link href={item.href} passHref legacyBehavior>
                <SidebarMenuButton
                  asChild
                  isActive={pathname === item.href}
                  className={cn(
                    "w-full justify-start transition-all duration-200",
                    pathname === item.href && "bg-primary/10 text-primary hover:bg-primary/20"
                  )}
                  tooltip={item.label}
                >
                  <a className="flex items-center gap-3">
                    <item.icon className="h-5 w-5" />
                    <span className="group-data-[collapsible=icon]:hidden">{item.label}</span>
                  </a>
                </SidebarMenuButton>
              </Link>
            </SidebarMenuItem>
          ))}
          
          <SidebarMenuItem>
            <SidebarMenuButton
              className="w-full justify-start text-red-400 hover:text-red-300 hover:bg-red-500/10 transition-all duration-200"
              tooltip="Logout"
            >
              <LogOut className="h-5 w-5" />
              <span className="group-data-[collapsible=icon]:hidden">Logout</span>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>

        {/* Version Info */}
        <div className="px-2 group-data-[collapsible=icon]:hidden">
          <div className="flex items-center justify-between text-xs text-muted-foreground">
            <span>CryptoPilot</span>
            <div className="flex items-center gap-1">
              <Shield className="h-3 w-3" />
              <span>Secure</span>
            </div>
          </div>
        </div>
      </SidebarFooter>
    </Sidebar>
  );
}
