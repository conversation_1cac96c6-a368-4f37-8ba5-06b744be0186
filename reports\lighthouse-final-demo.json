{"lighthouseVersion": "12.6.0", "requestedUrl": "http://localhost:9002/demo", "mainDocumentUrl": "http://localhost:9002/demo", "finalDisplayedUrl": "http://localhost:9002/demo", "finalUrl": "http://localhost:9002/demo", "fetchTime": "2025-06-02T00:13:39.900Z", "gatherMode": "navigation", "runWarnings": [], "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "environment": {"networkUserAgent": "Mozilla/5.0 (Linux; Android 11; moto g power (2022)) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "hostUserAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "benchmarkIndex": 3535, "credits": {}}, "audits": {"viewport": {"id": "viewport", "title": "Has a `<meta name=\"viewport\">` tag with `width` or `initial-scale`", "description": "A `<meta name=\"viewport\">` not only optimizes your app for mobile screen sizes, but also prevents [a 300 millisecond delay to user input](https://developer.chrome.com/blog/300ms-tap-delay-gone-away/). [Learn more about using the viewport meta tag](https://developer.chrome.com/docs/lighthouse/pwa/viewport/).", "score": 1, "scoreDisplayMode": "metricSavings", "warnings": [], "metricSavings": {"INP": 0}, "details": {"type": "debugdata", "viewportContent": "width=device-width, initial-scale=1"}, "guidanceLevel": 3}, "first-contentful-paint": {"id": "first-contentful-paint", "title": "First Contentful Paint", "description": "First Contentful Paint marks the time at which the first text or image is painted. [Learn more about the First Contentful Paint metric](https://developer.chrome.com/docs/lighthouse/performance/first-contentful-paint/).", "score": 1, "scoreDisplayMode": "numeric", "numericValue": 1062.222, "numericUnit": "millisecond", "displayValue": "1.1 s", "scoringOptions": {"p10": 1800, "median": 3000}}, "largest-contentful-paint": {"id": "largest-contentful-paint", "title": "Largest Contentful Paint", "description": "Largest Contentful Paint marks the time at which the largest text or image is painted. [Learn more about the Largest Contentful Paint metric](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)", "score": 0.18, "scoreDisplayMode": "numeric", "numericValue": 5585.222, "numericUnit": "millisecond", "displayValue": "5.6 s", "scoringOptions": {"p10": 2500, "median": 4000}}, "first-meaningful-paint": {"id": "first-meaningful-paint", "title": "First Meaningful Paint", "description": "First Meaningful Paint measures when the primary content of a page is visible. [Learn more about the First Meaningful Paint metric](https://developer.chrome.com/docs/lighthouse/performance/first-meaningful-paint/).", "score": null, "scoreDisplayMode": "notApplicable"}, "speed-index": {"id": "speed-index", "title": "Speed Index", "description": "Speed Index shows how quickly the contents of a page are visibly populated. [Learn more about the Speed Index metric](https://developer.chrome.com/docs/lighthouse/performance/speed-index/).", "score": 0.97, "scoreDisplayMode": "numeric", "numericValue": 2549.47733092971, "numericUnit": "millisecond", "displayValue": "2.5 s", "scoringOptions": {"p10": 3387, "median": 5800}}, "screenshot-thumbnails": {"id": "screenshot-thumbnails", "title": "Screenshot Thumbnails", "description": "This is what the load of your site looked like.", "score": 1, "scoreDisplayMode": "informative", "details": {"type": "filmstrip", "scale": 3643, "items": [{"timing": 455, "timestamp": 130362636791, "data": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAHyAPoDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAj/xAAUEAEAAAAAAAAAAAAAAAAAAAAA/8QAFAEBAAAAAAAAAAAAAAAAAAAAAP/EABQRAQAAAAAAAAAAAAAAAAAAAAD/2gAMAwEAAhEDEQA/AKpAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAB//9k="}, {"timing": 911, "timestamp": 130363092166, "data": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAHyAPoDASIAAhEBAxEB/8QAHAABAAIDAQEBAAAAAAAAAAAAAAECAwQFBgcI/8QARhAAAQMDAgIHBQUECAUFAQAAAQACAwQFERIhBjETMkFRYYGRBxQiUnEVkrHR4RcjgqEzQlViZZOk4hYkU8HSCDRDcvGy/8QAGQEBAQEBAQEAAAAAAAAAAAAAAAECAwQF/8QANREBAAIBAQUFBgQGAwAAAAAAAAERAgMEEiExUUFhcaHRExQVU+HwBVKBkRYiQlSSsXLB8f/aAAwDAQACEQMRAD8A/OSIgGTgc13cRF1ajhu+08Dpqiy3OKFrS9z30sjWho5kkjl4rlICI0ZcB3nCICIiAiIgIiy1dLPRzGGrhkglDWu0SNLXYcA5pwewggjwIQYkREBERAREQEREBEVoo3yyNjiY58jjhrWjJJ7gEFUWepo6qlc9tVTTQuYQ1wkjLS0ncA55cj6LAgIrwxSTzMihjfJK9waxjAS5xPIADmVsXS2V1pqzS3SjqKOpABMU8ZY7B5HB7EGCSCWJjHyRvYyQZYXNIDh4d6xr0d/4lkvlFSUj6WOHo3AlzTnJxjYY2HgvOtaXOAHMrGE5TF5RUvRtOGlhnWjlvY9apCLpUdukqn9HTQS1EoBcWxtLjgczgdi1pqcBpczs5hdKl5ra7esPquyuM3rD6rsrMq4q6nCkscHFFnmne2OGOshe97jgNaHgkk92Fy0VH1yW23Km4g4fralzWUVLRv8AeJHVLNLP3kx3+LuI9V8jREEs67fqFCA4IPciD3t1uXCcns5pqSjo2MvrQwOd0XxhwI1uL+1p3wM9o22XnuFLq23VmH/A2Q4MjTpcPAuBa7G3Y9o3yTgLhoucacREx1cMdnxxxnG5m5vi6/EtyjuVaHxDIaMGR27nn6n4sctnFx57kYx6n2SXLhO3T3Q8YU0UwfE33cywdKBjOoAYOCctwfDmF8/Rcto2bHX0Z0ZmYie2Jqf3enQn2ExOMcurfdNbxepZvdJX20yucynEuh2jJ0t1YPhnZer4oqaG+WCLia5TPku1U80baaF4YI3MLjqOQSWNjdTtA2zvvsvCovREVFIIiKo9HEaCZ9qkqJKZ8cNG5j45HEAygyFodjfGS3JHYujDHw0+Gb3h1Owvbqd0WvMbwxhwwl24LtYGx7d+S8Wi1GXczu973NRWWV1NDBIKNsbZjmKB0mlzOkGCTnO7R3+hVLXR2uvpJqltNRNDY8zh3S4jPRk/Bg4B1DfUccsdoXiUV3+5NzvbVTSNgiD21dNMScaYy4nt33A7v5haqIsNi9VwVTOmor0aevpqGsMDIoXyyiN79Tt2McSACQMEkjYkduF5VEHreIaq5UnClHZLtXMkfHUmaOlY6KQwt0n4nvaC7Li84aXbYOQMheSREHoeCRQ/aNW6vukdtf7pLHTyyxSPY6R46PB6NrnD4XvcCBzaO9dDjK2Udms1JbH3amrbtSVUmtkMM7NET2tIDukjbyc0kYz1zyxv45EFov6Vn/2H4rJSf03ksTTpcCOYOVAJBBHMIPdcJ1tHaaaasr5XFk7xE2CJhc8lha/US2RhaM6Rz3ye0ZHI4iiEN2qCKiOpbLpqBLG3SCJGh/L+qRqwR2EELitqhj4mnPgqTTl4wBgLdwzTE3rD6rsrjN6w+q7K5y042l3cU0u7irrcitlZLTCojh1RkFwGoanAHBIbnJGdsgYWqS2hpd3FNLu4rZFLUOkMbYJTICBpDDnflsrPoqpkbJHU8wY9hka7QcFoOCfplKLaml3cU0u7iukbRWjR+6adekDTI07uxgHB2PxDY7rXZR1L2OeynlLGs6QkMOA3ONX0z2pRbV0u7iml3cVdEVTS7uKaXdxV0QU0u7iml3cV0JbXWRMjcYdQeGkCNweRqbqbkAkjI33UUdrrqzSaWkmlBcGgtYcZPLdKS2hpd3FNLu4rejttXJI5jYXZbzJIA6wbsTsfiIC15IpIw0yMc0OGWkjmO8d6UWw6XdxTS7uKuiKppd3FNLu4rdoaCet6T3foj0bdTtcrGYHf8RCxmlqBjMEu7gwfAd3HkPqlJbW0u7iml3cVuTUFVC7TJA8O06iAMkDAdkgctiCsL4ZI2Me+N7WPGWuLSA76JRbDpd3FNLu4q6Iqml3cU0u7irogppd3FNLu4refbapkUcnRBwk06Qx7XO+IZbloORkbjIWOGkqJsmOF5ADjnGB8I1O38AM4Sktq6XdxTS7uK24aKpmgM0cL3Qgka8fDkDJGe/HYqupKhrmtdTzBzzpaCw5J54HqPVKLa2l3cU0u7irkEEgjBHYiKqxjtQ2PNdrQ7uK5DOu36rsqSOKutQ3foHULzSMmno8NicXHGNevcDmckjz5LkruW2426npqYSQ1EVWxxD56fAcWYdjBJ2cS4AnsDRhahmWxFxARRR08sUr3NjlbK5vwOOonSCd+rqdv/exjZRNxXPNO2WSBupr+kAa8gZD3uGe8fGQR24CzU/EFO2qknfJUhz545nhkYHSNY0t6M5eSQRjck9ucryx3JWpmY5SzERPY7TOI6sStc/S6JoYGx8gNLmOztzJ6MDJSpv0k9LLB0RayRpDiJDzOjl3D92NvE+GOKizvS1uwIiKKLsXu2Q0dDb5qZz3iaPU97jgF2o8gQMcvH8+OrPlfI1jXvc5rBpaCeqM5wPMlUdf/AIhqn4ZLk0/Qsh6NjtHVDQDkb5+Ht7yO1Z2cU1Tax1QYm5MjJNIcQAWuc70Oo/yWaK4WV9G1slKOmihLs9C1uqQABo2JJBOrOef8hX7VtLqeQe5NZP0hLX+7scOi7GEZG/8Ae5jHNb49WOHRFNxTUQhgFOx7v3Rdlx3MejTjuzoGe/P0XPu13kuUTGPjazDtZIOd9LW7DsGGjb9Mdymvllp6uOaGjdG6ORrwRTsJDBnLOtz5fFz2XkFJmeqxEdBERYaZ6SpdTdNpaD0sTojnsB7V228UzaJ2vp2OMxeXHUcgOcScHmDvgb42BxlaVkq6CljqRXU/Svfp0EsDwAM5GCRjO243GF0qO62Tp9VXQuLA8jSyCPePI2O/PGrfnvzW48WZ8GA8S1InikZA1vROjdguJJDQ0AE9udI9Vjvt4huNBSQwxPbJGTJK53LUWsbsMnb4fDnyXTdfrQ+Z7p6aWRsmkOxCxpLQYzpOHfF1HHJ7wPppz3a3e5VsdNAWSTxdHtTsbqOWHVnOWj4XbDbfP0s+KR4POIiLm2IiIO7S8SVUDI2GON8UbWta0sbtiN0faDk4dnfPotiXiyaWOZrqSLMuvJznGoPG2R/fPhty55y0d4s8dC2CenkOAxwaKeMgODQHEkn4snUd+/s7Et3ssrHtkppS1sJhiHQR/Ds/BznOclhznOx5rpc9WKjo51FxBUUw/oonHU5wDGiNvxN0n4WgDsWzBxXVRF2YY3h2QdXxYz0nLOR/8p7DyC86izvS1uwz19U6trZ6l7Q10ry8hvIZWBEWVSzrt+q7K4zOu36rsqSsONmP5n/dH5pmP5n/AHR+axIllMuY/mf90fmmY/mf90fmsSJZTLmP5n/dH5pmP5n/AHR+axIllMuY/mf90fmmY/mf90fmsSJZTLmP5n/dH5pmP5n/AHR+axIllMuY/mf90fmmY/mf90fmsSJZTLmP5n/dH5pmP5n/AHR+axIllMuY/mf90fmmY/mf90fmsSJZTLmP5n/dH5pmP5n/AHR+axIllMuY/mf90fmmY/mf90fmsSJZTLmP5n/dH5pmP5n/AHR+axIllMuY/mf90fmmY/mf90fmsSJZTLmP5n/dH5pmP5n/AHR+axIllMuY/mf90fmmY/mf90fmsSJZTLmP5n/dH5pmP5n/AHR+axIllMzDHqHxP5/KPzXZyzvd939VwW9YfVdlJkcVZaToPeove+kNPqHSdFjVp7cZ2ysSJE1NkxcU9nWWfh5tvoJqb7UMtwDm04e9mA4HA1bcskcuxc1/CVwjqHQyS0bCwOdI4zjEYGN3doznbvUSXqmNFw9EGS67e9zpdhggvB+HffYduFtR3u2SXy7VtRDJ/wAzvTyOhZKYj3ljjgr6eXu+cxdRy5cOy58+D5WHvWnHbPPnx/qqPLi0m8LXB1w91Bg09D0/T9J+66P5s9y6kfCsMcNB0mqeWaGeR/RzN0HQRpLTjlg//i25uLLZUM6GdlaY5aQ0srwxocMHLXAA43ycjs8VrU3Edro4qCGnZWOipoJ4iXtbkl+MHn9fp4rrhp7HhM8b/wDY7PC2MtTbM4jhU/Se3xpzIeErjNDC9j6XVNCJoozKA+QYzgDv/lusVFwzXVdPTyh9NEah2IY5ZQ17xnBIHcunBxLRsutiqXR1HR0NN0MoDRku0kfDvuN+3C61nfTV0Vlraks10eW621DGtY0HbpGu3B+mc96mls2zas1jPHx/48eXfP7OmevtGnF5Rw8PHhz7o/d5ew2aOq4ifbrg57Wx9IHmFwzloPIkeCzVFqttbbJ6uxSVZfTuY2SGoDS4hxwCCPFTQXilo+Lqq4S9I+me+XBjALiHZwcEjvU/atporfJR26nqpWVEjDUPqC0Esac6QAVywjQ3JjKueXjyjdr9XfKdbeuL7PDv8mrWcN1tLTvlLqeV0Za2WKGQOfEXctQ+uyyycL1kDgZnwPDHsbPHFIC+IOIxkf8A6upPxFazSV9NC2qbBUAdGyOCOIREEEDIOTn5j6KtTxFbZYnmVtVVy6ozE6eGMPiwcn42nLvMLrOjscXWXn4/T6dkjU2iecffD6/Xt0r/AMLyUMlZJSSRy0tO5upplDpWtIG7gBsr3nhSanqqoUD2SRwsEgifIDK5uBl2AOWc+iVfEFLNU36Rsc+K9jGxZaNsfNv+GVty8R2sXWqukEVYat8PRMjeGhmcAZJz4clqcNiynKpqJnryi8uXDpU01jOtFX98vq47eGq91KJR0IlMfSinMg6Us+bSopuHK2opo5WOpxJKwyRwOkAke0doC6v/ABFb3V8V1fHVfaDIOj6IBvRl2MZznON+WFNo4ittu9zfFDNGY49E0bIWfvHdrtZOryWcdDY96Ly4ePhx5c+fD/bpvatcnHHD1c6lp5x0OKgAxN6QBzyTjAB7VuS8NiltFyqameN89MWBogkDmgl2HB22chWmv9Myps0lNHM5tBqDukAGoE9mCexKm7Wxlsu1PRtrHSVr2vBla0BuHZxsfqsxp7JjGXG+E9vbu9n68P0bvOWC32+2Cwi4XJ1ZkzmENgLe7Odx9Uq+HZDWxMt8glpZYRUNlk+AMZ26u7CUFfbTYRb7iKsETmYOga09mMblb0PE9OyrexkEsVD7sKePTpc9oH9Yg7H6K4Y7LlhjGpMRwjlzvtvub425jeHK59R0UZheDEZmSNkBY9o54Pmso4Wr3Pa1klK/WzXEWyg9L3hvfhbw4kp2Pcwvq5ovdpImlzI24e7HJrcYG3eVrW6+09NNZXvZMRRMkbJgDfVnGN/FWNLYbiJmf374jv6zP6NQ0qKwVlXBHK0wx9KSImSSBrpSOekLlyMdFI5kjS17SQQewr0sV6oJRb5KxtS2ahcSwRgFrxnIzk7di8/X1Bq62eoc0NMry/A7MleTaNPQxwidKbn6eXHgs0wIiLxoIiICIiCW9YfVdlcZvWH1XZUkcVS1rnuDWAuc44AAySVC3LNUMpLxQ1ExIihnjkdgZOA4E/gqNeeCanfoqIpIn4zpe0tOPNUY1z3YY0uPPAGV7C2X61lrRW08bXM6QMD2mRgBew5+MPIJAcMeHZklZKe7WKOha2NrYpQ1+g9F8TdTJGkEhucfE3m53Lsxhb3Y6sb09Hi2tLiA0EknAAGclQQQcHYr0VnutFTxzx9E2njkkge5rh0uoMcS7BIyDg/y5rbberY1scfu8Bj1whxNKwnRh3S74zk/Dvz22UqOq3PR5JF6S+V9qqqOgioaeON7HM1EMDS1ukBwccDOXb7l3fkZwuhU3O1MrJQyShMoldombQtMYi1jDNOnBdgH4sduM9qbveb09HiwCeSu+GVk3RPje2XIGgtIdk+C9RHfaOJtTFSMhghMUfRh1M1xLmvDiCcEk47Stl1+tslb708Rf+4EpApmiQvFQHdJqA5dHtjPPs7U3Y6m9PR4xzXMIDmlpIBGRjY9qhes+07QKIR6Yi5sHRy6qcOMp6ENbocRluHgnO3Pt5LWtlzt1LbYGzwwTFg/eQOgGt7+lzq6THLR8OM8+zfKbsdTeno84jQXODWglxOAB2r10lzsTLdV0tPTsdhrmRvkhGuT4AGuzpJB16jzGxHPkqMu9odVVU81MzpG1J92DIGtb0Jc3cgDm1rTjty/PYm7HU3p6PKHY4PNF7H7UsElLSQS07AwgMlc2Ea2fA4OdnSM/EWkbnl2dujcrlaqm52+SnpWU9N0onqGtiHwklocwDtaA3I/+x2Tdjqb09HnFOl2nVg6c4zjbK9dXXe0GT/l4YCJWhsx93af6jwcfCMblhyAOWdlpX65Uk9BDDROiIZOZGRspxH0bNIAa44+Igg7nOe/fCTjHUjKejzqljHPOGNc49wGV6596t811lml6BzJamaYv92ZnBY0RgksOMHVnAO++/NZ33mzRzVDqQRRU8hdqj91GpxLmkEOx8LcAjSCBtyOchux1N6ejxJBBIIwRzBUta52dLScDJwOQXsaO52iephgfDThnw6HvhYNMnx5c4nGQMsOHHBwtS8z0EF7nDXQTM9zbEXwRtDHS6Rk4b8PPPJN3ts3uynmFLmuacOaWnAOCMbHkva016sj5ZTUwwMBkkaC2kYMRa8tA+EjOM9mT8zVLKi1T2epmY+lj6On6NzJI4zJJJ0LGtLcnWMOB6oI7e9Xdjqm9PR4lzXNc5rmkOacEEYIUxsfI9rI2ue9xwGtGSV7CTiKimfdC8QtdUuka13urcOj6Zj2ggDc6Q/fnkjfkRrVVwt0nEEc1I9kUb6aWIvbGGNa9zXtadmt2+Ju+Nu84ypux1Xeno83LTzwsY+aGSNjxlrnNIDh4d6xL19NeLZE/NXI+rgmMGafQdMRY0AuOdicjGADkE57kqL3a2zvMFPS/Ex+o+6scC/ogGEZaMfHvyGeZCbsdTeno8gi9XcLpaJqKdtPFCwviOqMUwDnTENw9rsfC0HOwI+hyvKKTFLE2lvWH1XZXGb1h9V2VmVcVbNrphW3OkpXOLBPMyIuAzjU4DP81rK9PNJTzxzQuLJY3B7HDsIOQVR3qbh37SbG61yvI1SMkbMwktLNO40AkjDx2bb571kbwhVyTxU8VRC6pLHPlj0SHoy2Ux82tORkc+X8ieb9vXHpNfTt6pZo6JmjGQ7q405yAc4zkBXHEV01l5qWucSXEuiYTku1Z3HzZI7iTjmt3ixWTWulEKM02C797Frc13Nrg5zHD1YfJaS2rjXTXCZstQW6mtDQGtDR2k7eJJP1JWqsT3NwIiICIiAiIgIiICIiAiIgIiICIiAiIgIiICIiAiIglvWH1XZXGb1h9V2VJHFRFLQXOAHMqiEWxSUs1ZUMp6GnlqZ3Z0sjYXOd9AN10bTw3d7tWVdJRUBNTSM6SeOV4iMbchu+sjfLgMc90uOSXF04yLdmtlfBNJFNb6iOSOUwva6JwLZBzafEdyg2+tEgjNBUCR2rDeidk6ett4dvcitNACeStgOGww4b+BUD+jP1H/dA0Hvb94JoPe37wW3arbUXOodFT6GtjYZZZZHaY4mDm5zuwbgeJIAySAujc+F66hsEN81Q1Fonm93iqYi4NfJhxIAc0O20ncgDuzvgOGWkfocqFLeTvp/3UICIiAiIgIiICIiAiIgIiICIiAiIgIiIJb1h9V2Vxm9YfVdlSRxVLCA4Z5dqhFR6Dgu/1PDF6FfS07akFhiewjrNODsew7BdeHjEy3y/V10sxqoLvA2nkgjldFpa17HNw7BP/wAY/mvEIs7mO9vVxc40cI1Pa1/NVX3PsMPtquraZzJLNEZDUvnzG7SAC8uA6pdkbDVq5Dl2rVpva5cWtp21dsnm6FjW9I2reyX4RByfpJALoMuHbrP1XyhFrg6cW5cql9ZcaysmjbE+olfKY2jABcScAdwytQf0Z+o/7qFIOxB5IPQ8IXG00TbnBfY6t9PVwNiApydjra7U5upuoDGdORkgLXu8lop7e2htDp6t75BNNWVELYjsCAxjQXED4iSS74jp2GnJ43w9x9U+HuPqgN5O+n/cKFORjAHNQgIiICIiAiIgIiICIiAiIgIiICIiAiIglvWH1XZXGb1h9V2VJHFRFaL+kB7t1RPRP7h6hOjd4eoWRFaS2Po3eHqE6N3h6hZESi2Po3eHqE6N3h6hZESi2Po3eHqE6N3h6hZESi2Po3eHqE6N3h6hZESi2Po3eHqE6N3h6hZESi2Po3eHqE6N3h6hZESi2Po3eHqE6N3h6hZESi2Po3eHqE6N3h6hZESi2Po3eHqE6N3h6hZESi2Po3eHqE6N3h6hZESi2Po3eHqE6N3h6hZESi2Po3eHqE6N3h6hZESi2Po3eHqFVzS04IwVmVJtw3zCUWo3rD6rsrjN6w+q7KzKuKrRdceaqrRdcef4KwMqIujw41jr7QiRjXt6VpLXcj9V0wx3sox6uWpnuYzl0c5F7Gz3mW5VzKeKkYSQXEOlkcCBzGkZJ+mFzKKigi45pqIsD6dtwZFoeMhzekAwQeey66mjGOMZ4zceFOOjr5Z5zp547s8+duCi/Rl64b4V4gv9x4dqXWWmr4q2SSkbZKQxTx08bXOkjl+EMc84aBz3yd15P9n3CzOFZOJ6ie+R2w0TKplIHxGoa4zGIguLQC07EHA7ea89vVT4+i/Q9Lw7wTar1d3Q2ecvpbFDWhtQ5lRE0yaBrDZGka/i7ct54AWvw37M+H6XjJ9Ow1tTLZa6np6xtfHG6nrBKwn4G42wd8Eu2wUsp8ARfb7pwHZJbTabtcRVdBJT0VMKe2xxRP6SaSQdI/4CCAAAMjU47ErjWPhqk4dqvaBL7tS3u4cPAMpIZ4xJGWmQtdM5h2cWN3IOQDlLKfKUX3Cp4EoOMrFaqujo2WriyvoPem0VMwRU8rWziMv6PHw5Y4OGnA2Jws1T7IeGDT/8neqx8lS2pdSPc9hx0J0nLAzLwXNcSQW6QRz5pZT4Si+y1fswsFbfbtw9w9VXZ98t/u7yKl0ZjkZI5geWhrQfhEgOfrsux+zfhq4cORVUVc9lqt7q49KTDFLUBkzY2l0wj6ucnJDsAgBLKfAkX3if2LWQ1QFNeK33VpfDJLIGtLZ39F7szGn+t0oye3SSMZXy32hWCl4Y4gZaaeWaSeGmhNX0padM7mBz2twOQyBvk7FLKeZREVQUgE8goV3Auc1jQTyAA7SgjS7uTS7uXraHhu2UVQJOIblC6mgb/wA7TUlS1lTE/wDqsYHNcHu7DjZpBDi3GTgt3Dtuuf8AxDNTXmnp6agDn0zagO11DOkDWnZu2QfrnGwGSA8zpd3Jpd3L7RX+zaipuIeH6eaw3aCmeH+/Na58jCNOYwJNLQZC7YhmW7gA5yrTezuxRm4ulpalsLTI6V7Ji02uNtK2Vhla7JD3uJaQ47YwN1LhafFdLu5Qcg4K+l8f8G2qi4ZZfeHGTto3SsZKyqk/eQZYC1paM/E4ODjk5Axgc8fNXbtBPPllVFVSXk3zV1SXqt80lYUb1h9V2VxUWVFaLrjz/BVVouuPP8EgZVlpp5KaojnhOmSNwc04zuFiRaia4wxMRMVLptu72kEUdCCOX7kLTfVzvrTVmRzakydL0jTpIdnORjkc9ywItZZ5ZRUyzjp44zcQ3Bc68XP7SFbVC4a+l966V3S6/m15znxytq48SXu5OqXV92r6g1LWsm6Soe4SNacta4Z3AO4HYVyUWW3Vk4ivctI2llvFxfTNhNO2F1U8sERxlgbnGn4W7cth3LLLxVxBLDRxSXq5OjonB1M01L/3JHIt32I7D2Liog7sPGHEkNY+riv92bVPj6J0vvchcWZJDSc5wCSfAklaFuvFytlx9/t9fVU1cSSZ4pXNec88kbnPb3rRRB1n8S3x93+1XXi4/aekt97FS8ShvcHZyB4KKTiO9Udsnt1Lda6GgnJMlPHO5rH555AON+3vXKRB0o79d47nJcY7pXsuEjdL6ptQ8SuGMYL85IwAOfIK9t4jvVsNP9nXavphTB4hEVQ5ojDjlwaAcAE8+9cpEHUdxDenOmc67XAummbUSn3l/wAcrd2vO+7h2HmFo1lXUV1VLU1s8tRUyu1SSyvL3vPeSdyVhRAREQFbUCBqGceKqiC2W/K71/RMt+V3r+iqiCwLQQQHZ+v6K2sfF1/i5/Fz/ksaILZb8rvX9FDnZxtgDkFCICpL1W+auqS9VvmkrDGiIsqK0XXHn+CqrRdceaQMq2rW2F9wp2VMRlie8MLQ7TzOOa1VLSWuBacEHII7Fpl62s4cpHVMphklgjY+TDNOsaI5NDiXkjDjzDcd2+62ouC4Ja+boqmU08VUYtEjGtc5jZGsd/Wz/W5huNuzkvHmsqjC+I1Exie7W9hedLnd5HafFZTdbiQQa+rIJyR0zt9sd/ct3j0YrLq61nstNcp6ym1vjlp6hrnOJ2FPqLXuPi34T6rddwrB7lJXtlcYTG97YTjUzML5GZIcTyaOYbns2XlBUTCSSQSyCSQEPdqOXA88ntyswuNc1kbG1lSGRjDGiV2GjltvtzKkTHRZier0tPwS+SSpD61rI4JHsLnMDdQb0e41OA36Qcz2dq8zdKQ0FxqaR0jZDBI6PWzk7BxkKRcq8OjcK2pDohhh6V2WDGMDfZari5zi5xJcTkk9qTMdhET2oTZEWWhERAREQEREBERAREQZKZjJKmJkr9EbnhrnfKCdyuncaSnbTVckNPLTmnqRAA+TVqBDuew+Iad8YG42HbyFmqKqoqWxtqJ5ZWxjSwPeXaR3DPLkFmYm2MsZmYmJemZw3BU0VC6ETxOkY1z5Dl5cTGXkNbgB24ONLjgdbBWCs4VfC7RDNNPIQ8tLaZwa/SHnSCd9XwbtI2z24Xm8nbflyTJ7+Szu5dXONPUieGXk9U7hJjH1Ebq15kh1ag2nLthI9gOxzj92STjbI55XPv8AZo7VR0rg6Z8sksrHOezQ1waGYLNzlpycO7e4LiZI5ISSd1YxyvjK44akZXOVx4CpNyb5q4VJuTfNbl3hjREWVFaLrjz/AAVVaLrjz/BIGVbVsoai53GmoaKMy1VTI2KJg7XOOAFqrt8ENrHcYWcWypp6Su96j6CaoJEbX6vh1YB2zty7Vpl9cn/9PtUOGyKe8Uc3Esc5D6cTYh06NXRj4dXSbg5OBg8u1eH4H9lPEXFVTKBTSUFJG2QmrqIz0Zex2ksGOZyCPIr9MPttTfKX3HiW1z266SXB7RX2SeSMB4ptqgP2IBb+7w7O+Avm3sGqblS+0XiuxVF4ra6hoYqhrGyzucxzhNgyaSSA45JJ8Ss3LVQ+a8F+zGXiThSXiGpvtss9vjqDT661xaCQBvq5czhaF74DdTcQWqz2G92viCquBLWfZ8oe2M5/rHO22T9AV9E4Kvd+t3sapIrdwLFe6JtW6T3iZzamMv1nc07fjyOWTt29y9xDbLHauOfZ5fZ7VS2O8XWOWOeiiYI2tkdDsdPYQ46e/wCIZSynzOo9hkxjqqO2cUWqv4jpI+lqLXHs5g7g7Oc7jm0cxyyvmfDHDlx4k4hp7LbYc10zy3EnwhmOsXdwAByvuvAnC3EFH/6irvcqulqobfFNVTyVUkZEUsb9WgBx2PWadvlPcp9lFDNSe2q43yoo3wWi9mrFsqXABkxc8PGn6sa4pZTx919ikjLfcHWDia23q6W1pdW0EA0vjxnIB1HJ2IwQ3l37LjcE+zB994c/4hvl8ouH7I6QxRVFSNRlcDjYZAxkEc87HZfRvYjw7e7B7TuJ7lfaWppLfTwTiaonaWxyEyBwIJ2cMNJyP+6w+0aln4r9h3CtRwtSz1lNT1BE0FPGXOacObktG/P/APoJZT5txP7Lr3ZOKbXZYHQXD7V3oamB37uZvad+WAQTzGDnJXp6r2GzGKqpLXxRarhxFSR9LUWuPZzB3B2ck7jm0cxyX1u0VFPYH+yuzX0iO7iCRoY/rRnodIB7tyG/ULx/s74Yv9B/6g75ca6kqoaCOSqmfVSMIikjeSWAOOx5g4/unuSynwvhThm5cUcRQWW1xZrJXEHpMtbGB1nOPYAvoV59i8kNtuMvD/EttvdfbGl1dQwDTJFjOQPiOTsdiByPbsvbex+hmo/a5d7xUUb6a2X1tSbXUvADZsyCQad+1oJ+gWp7BOHb3w1xrxTX8R09TS0NLSSsqJ54y1krtYdqBOzhhrjkd/ikyU+f8Fey5974cHEN9vlFw/ZHvMUNRUjUZXZxsMtGMgjOew7Lie0XgW4cDXKngrZoKqkq4+lpauA5ZMz/ALHcbeI3K+q+0KiqeKPYZwdPwvST1dPSyaJqemjL3McGlu7R3EEfxBaXt2abZ7M+ALNctrxDAHSMdu5jRG0EHu3IH8J7ksp8KRdS30FHNCx1ZcIaaWUno2uaXtaBzMhbktB3AABPeAMFdaJ9jruKLW232wtpMaJ4HyFolfl2CC95xkafhLue2e0yc6cMtWMb4TNPKovqVXZqRnG9TCLVQSUT+jdK2MZMILnDdheNGQNyMhowQN8LhUFLT26hnF0tlFK108UUD85e9shJ1hxcBgNacZxzGViNaJcMdsxyi4jp5vFIvpVwtNC2ue+ltlM2pNLK6nt8zQ0lzZGDUQHkO+EuI330nmsBsdNDcLlJQW6kq5C6L3emkk1RhvKbTh2TpdhvPYEnxSNeCNtwmLr7+/8At88RfRa61WaOjrDQ09NNbWxVTn1pky+KZr3CJgOeRAZgY3Difp868lvDOM+Tto60asTMQIoUhbdkrHNyb5q6pNyb5qSsMaIiiitF1x5qqtF1x5/gkDKpBIOQcEKEWmXvP2scYf8ADTbMLvUBol1+99I73gt046PXnq9vfntXl7Hf7tYaqWps9fPRzysMcj4nYLmk5IPmFy0Qeh4X4z4i4WZKywXaooo5TqfG3DmE8s6XAjPjhal94ivF/uLa+8XGpq6tmAySR+7ADnDcbNGd9lyUQevuHtJ4xuFqdbaziGtko3N0OZqAL29oc4DUR9Ssl745qq7hHhey0rqmF9mc6X3h0nxGQn4CzHVDRsF4xEot6y9e0Ti692s266X6rnonDDoyQ3WO5xABd55WnwvxnxFws2Vtgu1RRRynU+NmHMceWdLgRnxwvPohbpXi+XS9XQ3G619RVV22JpHkubjljux4LuXH2k8Y3G1OttbxBWy0b26HsLgC9vaHOA1EfUryKIO1LxTfZaS3Uz7rV9BbnB1IwSEdARsC3HJdC++0Liy+2z7Pu19q6ijIAdESGh+PmwAXcu3K8qiD0PC/GfEXCzJWWC7VFHHIdT42kOYT36XAjPjhc6+Xm5X64PrrxWzVlW8AGWV2TgcgO4eAXPRATdEQSSSSSSSeZKtLLJKWmWRzy1oYC45wAMAfQBURA3REQERQglSoCICpLyb5q6pLyb5qdiwxoiKKK0XXHmqq0XXHn+CQMqkKPNFplOPEJjxCjzTzQTjxCY8Qo80QTjxCY8Qo8080E48QmPEKPNPNBOPEJjxCjzTzQTjxCY8Qo8080E48QmPEKPNPNBOPEJjxCjzTzQTjxCY8Qo8080E48QmPEKPNEE48R/NMD5gqqQgtgfMFB27QVCICpLyb5q/mqS9VvmkrDGiIsqK0XXHn+CqrRdceaQMqvDG+aVkUTHPke4Na1oySTyAVFu2eqZRXCOaUHRpewlvNuppbqHiM5H0WoZWmtNXFTvmLYnMYCXBkrXENDi3VgHcZBGRsq2u1XG7TPitVBV1srG63MpoXSFozjJDQcDJC6sFbHQUMzPeqOoLYZIITFE7pCH5B3c3ZvxOPf2DvFeF73TWehvcVVRsrHVtPHFHFKHdGXNmjf8elzXYww8jzxnbKs8OSR3uHUwTUtRJBUwyQzxuLXxyNLXNI5gg7grPSW2urJIY6OiqZ5JtXRNiic4yad3aQBvjtxyX1S0e021uNHV8QU89RUNkD6ikipY+ifIJ2vbOHFwOpsbejAI5Y3wStKn9pum2QskqbkKuKOvjDWRs0O6ZhETs6hjQcbY5cu5Ztp80pqOqqnStpaaaZ0TC+QRxlxY0EAk45DJG/iqVEMtPPJDURPimjcWvje0tc0jmCDyK+uz+0Th59PVR+71xFTDJHEG0sY9yYWx4gZ8Y1M1MJztzzjJK53GfG9hvNnukFBS1MXvJPQ0r6eNscUnvBkNRrDidZYdBAHhkgBLHhX8N3xkFPO+y3JsNRp6GQ0sgbLqGW6TjfI3GOa5sMMsxcIYnyFrS8hrScNAySfAL6pa+PeH6Griq3x3Sd8tHQ0dRTugjEbBAGanNPSHWSWYAIbgOK2IvaPZoKehEBuME0dDJRl0FLGOgDoAwFoL8uOtodsWNPPTqJSx8pprfW1UEs9LR1M0MWTJJHE5zWYaXHJAwMNBP0BPYtXyX2Kl9p1oZNWB7LjE17GR00sdNE50IFI+JztJfj+kdrxnft3WlV8ccPSRVkjYq41wZO2KX3aMGokkpYoumf8fwHXGXEDV1s88pY+ZVFDV00fSVFLPEzWYtUkZaNYAJbkjmARt4ha/kvs1w9pdkrpauTVc4ayeWaWGvdSRPkotYiwGDpPi6kjc5Gz8+C4nFfHVoutmuVut9BNS088buhi6GMNbL72ZQ84O37slu3IkgbbpY+aeSIiqHknkiICeSIgeSIiAFKhSghERAVJuTfNXVJuTfNSVhjREUUVouuPP8ABVVouuPP8EgZURWYMu35LTKqLJ/CE/hCDGiyfwhP4Qgxosn8IT+EIMaLJ/CE/hCDGiyfwhP4Qgxosn8IT+EIMaLJ/CE/hCDGiyfwhP4Qgxosn8IUfwhBRAr7fKFOB3BBjRZPIJ/CEGNFfP8AdCOAwCBjwQUVJuTfNZFjm5N81FhjREUUVouuPP8ABVVouuPP8EgZVaPrH6Kqszn5LTK3mth1BVtpBVOppxSk4ExjOgn/AO3Ja3kvsNz43sE/CM0EJPTSUxhbSCM/AS3AGcYwPr2L0aGlhqRlv5VTjq6mWExu43b5HTU01TJ0dNFJNJjOmNpccfQLGWlpIOQRsQV7D2b3mntNXcm1VbFRtqacRh0zJSxxD2uwXQkSMO2QW92CF72e8cCCnira99JX09RJUvmhfStfXTuNQCx7pAAWfCHbEtyDyPZ5rd6fEgCQSM4HNR5r6zdeJOHWcOXq20FRbffKuiw6ogtnRRTObOXtYG6MtcI9tWAM4OcjUvk3kqiceKjzTt5Jt3IJ81HmnknkgnzUeaeSeSB5qfNRt3KfJBHmnmnkm3cgeajzU+ShBnjgLgCThZjQyguaWvDmjURoOw710uHq2CgvVuraiLp6eCdkr49via1wJG/0X0z9otqM1S6Rte98zGNlmDXB0rR0vwD97lg+Nu+SMgnC3UMW+RfZ8xa1wbJpPI6DgrE6lc0dY5IyMjGV9BtvGr7XT2ukpa64z0tNO+SUTtBy3AaxjGl5DQBq7eZ7eS8/xVX0Nyr4qm3RzxNMLWyRyY0scNtLME/CBgBKguXliCDg81LuqPqrykOkcRyVH9UfVYbY1SXk3zV1SXqt80lYY0RFlRWi6481VWi648/wSBlVmcz9FVWj6x+i0ytum6jZfQa/2W3Oj4Xp7o6ut7qqRomND02mQRObGWuBcACR0g1DOwI3O+A+f7puvcT+zK8Uk0jbhVW2kght8dxnqZJnPihje4ta0ljXFzyWnAaHA9hUzezO6Ur6ySvuFqo7dTRwyi4SyvMEzZv6Po9LC52cH+rtg5Synht03XrI+Abu7i+q4de6ljqaWN081Q+Q9AyENDul1YzpwR2Z3Gy24/ZrdJZad8FdbJLXNRyV4uYkkFO2Jhw/VlgeCCQNOnOSEsp4jdN17d/s2ucZknluFrjtLKNld9qOkk92Mb3aWgfBrLi7I06c5Wo/gC7ji+l4eidSy1NVE2ohqGSHoHQlurpdRAOnAPZnbkllPJ7puvcRezS51DqSWhuNqq7bURzSm4xSvFPC2H+k6QuYHNxkf1TnO2Vw+J+Gajh+O3zyVVJWUVwiM1NVUjnGOQB2lw+JrXAg8wQEHD3TdRsmyCd03UbJsgndN1GybIG6BQrcggsHOHIkJrf8x9VUqNkFi9/zH1TW87FxVFYK2CP6o+qFQ/qj6qCipLyb5q6pL1W+aSsMaIiyorRdcef4KqtF1x5/gkDKrR8z9FVWj5n6LTK2fBfXa72iWCb2Z/Y7aOumrHwugFHUv6WGnfpjAlje7LmgFri1oOxLgdsL5FjxXvYvZ258THPujQ4gEhsGoD6HVuvRobJq7TM+yi6++15Nq2/Q2OInXyq+XCZ/06g9oNorbYyzXSkrxaZrLSW6ofCG9Kyane57XsBOC3LuRws1x9odhvNvqOH7lRXKHh2OClhopICx1QzoAQC8E6TqDnZwdtua5H7Of8V/0/8AuT9nP+K/6f8A3r0/CNr/ACecerx/H9g+Z5Zejvy8eWCW51l8mirT9psls9RQNczMVCIWNY9px18gHc42I8VrU/tEstHQwcOU9HcHcLi3z0Usj9Aqi+V4eZAM6dnAYbnllcn9nP8Aiv8Ap/8Acn7Of8V/0/8AuU+EbX+Tzj1P4g2D5nll6OtP7QrFU2p/DE1Hcm8L+4xUrJW6PehJHIZOkIzpwXEjTnl2qzPaNYoeJaG7Q0FzPuNPHa4oXPZpfRCIscXEbiUlxIx8PYuP+zn/ABX/AE/+9P2c/wCK/wCn/wByvwja/wAnnHqfxBsHzPLL0deg9oVis9upuHrbR3Kbh2SCqhrZJ9Dal5nAGWAZaNIaMZ5+C8rxxxFb7pb7FaLJDVMtlohkZHJV6ellfI/U9xDdgOQA8F0/2c/4r/p/9yfs5/xX/T/7k+EbX+Tzj1T4/sHzPLL0fP8APgmfBfQP2c/4r/p/9yfs5/xX/T/70+E7X+Tzj1Pj/wCH/M8svR8/z4JnwX0D9nP+K/6f/cn7Of8AFf8AT/70+E7X+Tzj1Pj/AOH/ADPLL0fPifBMr6D+zj/Ff9P/ALl5C62v3C41FL03SdE8t1acZ8srjr7Fr7PEZamNX3w9Wy/iWzbXlOOjlcx3TH+4c0c1K2Pd/wC9/JPd/wC//JeWpe24a+fBRnwWx7t/f/knu39/+SVJcNfmrLMKbH9f+Sn3f+9/JKkuGv5KH9UfVWe3S4tJ5KruqPqoqipL1W+auFSbk3zSVhjREWVFaLrjz/BVVouuPNIGVWj6x+iqrM5n6LTKdlvw3m5QRNihuFXHG0Ya1szgAPAZWjuu5RUFviMMtdX07iwCSenGo5YRsGOafifvuMjGRvs7D2k4cYlz1Nyv5ov9Lan29df7Trf8935p9vXb+063/Pd+a61jprTV36sc6GL7MYNbGVFQWODdQ2bu3U7GQMkDtPLC3WW3hxtpikNQyWo1TGUCchw/cvc1gBwNnBozvl3eNknac44XLz5Z6WM1OHlDzn29dv7Trf8APd+afb11/tOt/wA935rr09qtkHFNJTOn96onvfkmSMAgZ07h3056c9nPK6dws9rjdd2xw0b3CRzKcR1fxhxY0gBpcA1oJOc6j/VHLKk7VlE1cpOpoRMRu8+6PB5X7eu39p1v+e780+3rr/adb/nu/Nerhs1lFFSioFNHVRxnpWSTYM03RyHRqEpAGtrQfhadxvuqT2qyBjxEKUudE50x96/9s/3drmtZ8XxAyFzd9XLHPdPe8uss+20Lrc8o+/vq8v8Ab12/tOt/z3fmn29dv7Trf8935r01DabKYKZtWaYENj/e+9gGWY6tcbviw1oIA1ADbBzuFpy2y3PlriwRMkjtzJzEyQvZDKXsDsEEkhrSXYycb9ye9ZdZajU0Zmtzyhxft66/2nW/57vzT7eu39p1v+e78105LXbG09dDT1cdRUsfEYpumDW6CCXgAgZIxzyeYHNdm40HD1FXs6GOiqKNrJ3OEkz9b2xgFpBbLjL84B27fh2SdqyjtknU0Ymtzyjpbyf29df7Trf8935p9vXX+063/Pd+a9FQ22z3CS3Op2Qs6SSEVED53B2kuw7TgnfBycnYNz2kDx0wa2Z7Y3amBxDT3jOxWsdozy4XLppxpZzMRjy7ob/29dv7Trf8935rSknmmkdJLLI97jlznOJJPiViUq5Z5Zc5dsdPHHjjFLa3fM71UdI75neqhN1ltPSO+Z3qge8nru9VXfKkILa3fM71TpHfM71VU3QQSCodjSPqpOUd1R9UFFSbk3zWRY5uTfNRYY0RFFFaLrjz/BVVouuPP8EgZVZnM/RVUtODnC0yt5ompvyn1/RNTfld6/ogeaKdTflPr+iam/KfX9EEKQS0ggkEbghNTflPr+ijU35T6/ogk7nJO6jzU6m/KfX9FGpvyn1/RAUgkZwSE1N+U+v6Jqb8p9f0QQiam/KfX9E1N+U+v6IAODkEgqFOpvyu9f0TUz5Xfe/RAapTU35Xev6Jqb8rvX9EEdvNQram/KfX9FGpnyu+9+iABurKA5vyu9f0TW35Xev6IBUH6qdTflPr+ijU35Xev6IIUu6o+qBzPld979FDnZwAMAIKqk3JvmrqkvJvmosMaIiiitF1x5/gqq0XXHn+CQMqAEnA5orR9byWmTQe8eqaT3j1U7Jt3FBGk949U0nvHqp2TZBGk949U0nvCnZNkEaT3j1TSe8K2yjZBGk949U0nvHqp2TZBGk949U0nvHqp2UbIGg94QMPeEVkFdB7wmk94Vtt1BwgjSe8eqaD3hFYckEaD3hRoPeFZQcII0nvCaT3j1TZSOaCNB7wjmlp3Vij+qPqgxqkvJvmrqkvVb5qLDGiIoorRdceaqrRdcef4JAyq0fM/RV81ZnPyWmVt19TuPsvgpOGZav36U18MJmeMDozgZLR2+efJfK/Nd6bi6+TWj7MkuDzR6QzTpaCW9xdjOPNd9HPSxjL2kX0ddPLCL34ts8E8O09+F0fWVL4I6KBs3wviZqzI1mNUjmtHWzufBdm0ezeruN7p6d1dTU9DMYXiYnpHCOWR7GbNy0uyw5wcbjfG68RT1tRTUtXTwTFkNUxrJmgD42hwcB95oO3cuzScacQUdDSUlNcnxwUpY6ECNmpuhxc0asZIBJIBOBk964OTq0fs4utdURMoqy3SxzRxzRy65GtcySYwtO7AR8TSdxy9Ftw+ye/VFDSVlPU2+SkqSzo5sytYWvJDXZdGM5I6oy7cbbrjR8fcSxwsiiuQjYxwc0Mp4m4w/WACG8g4kgchk7brXpOMb9SUlJTU9wLYqXHRfumFwAzhpcRlzfiPwkkb8lOK8GnxLZKrh67y26uLDPG1riWBzdnAEbODXA78iAVy91vXm61l5rjV3GcSzlrWAhjWBrWjDWhrQAAAMYAWj5qobpunmo80DdN0P1UY3QSO9SnYh580DdRkp5qEE9qsBkgKAE80HastJBU3CGCofLHAcl74mB7gAM5wSB5kgDn2L1tbwbRU/2wxlxqHzUcHvMGYGhkzNDX7HXkn4ju0OAAyTgheGoblNRTx1FNNLBUR7tkicWuH0IXRHFd3HS4u1xHSuD34nd8ThjBO/gPQdy3bFS9Na+ErZW2+31El1np3zzNp5WPgadL3MLm4AcTg4wNQGrmF5ziKzyWetbSzBzsxh2sj4XntLe8ZBGe3CwS8SXOeKGOW5Vz2QnMYdM46DgjI322JHmtSruU1V0ZqZ55zGwRs6V5dpaOTRk7DwVspoyN0vICo/qj6qzjqJJPNVd1Rv2rm2oqS9Vvmrqk3JvmkrDGiIsqK0XXHn+CqrRdceaQMqtHzP0VVaPreS0ynyU6TpDtJ0nbKbr9F3mxU9b7J44KeyUVGYaP3gieBrSHCOAunjqIyQ4nOcOILs6dtCTJT85+SeS/SV/4bst1ubGfZrLVbKG6UUNRDPaoKYVDJCG4jmYNZ7S7J3z2YXkeLrHU3Pha8Om4bhornS3o01vZQ24Qvlh0vL24Y0F7WhoOo5+qlrT435J5L79cLHQdJfrbLZKCDhel4fjqqO6NpGiR02lhEnT41Oc5znDTns5LZuXD9qku3E1ruNmoKHhehgpHUFwjpWxvJc6MZ6fGZNYc7OSeXYllPzx5J5L7jxjYJa22ca0o4ZpqV9vr4IbMaO3iOSZpeWloc0ZlzGNRznvW7SWGhY+22t9kohw5Lw26rqri6jaZGVGhxc8zkamua8Aac4HLCWU+A+SeS+8XuzUbI+IreLHQx8O0nDzKuhuLaNrXvn0Mc13T4y4uc5wxns5L4Pv3qxKIUhQrIB5KD9FO6boI8kG6hWHJBKjs5Im6CPJQp370agnkilRugjyUO6o27VO/ejuqPqgoFSbk3zWRY5uTfNJWGNERZUVouuPP8FVWi6480gZVaPmfoqqzOt5LTKdl05L9dZLLHaJLhVOtjHl7aYyHQCcdndsMDkDkjmVzfJfTKTj22MpomvpKmNzWgFkbWlo+m42Xq2bR0tWZ9pnu/pbxbZtGvoRE6Olv331TwNbebnX0kVLXXGtqaaHHRxTTuexmBgYBOBst2l4tvtPcKat+1ayaop2PjhdPM6Tow5pacZO2x/Be2/aBav8AoVv3G/8Akn7QLV/0K37jf/Jev3LZfnx+31eD4lt39rP+Uej53JeLlJbW2+S4VjqBpyKZ0zjGDnOzc45pPd7jUW+KgqLhWS0MWOjp3zOMbMcsNJwF9E/aBav+hW/cb/5J+0C1f9Ct+43/AMk9y2X58ft9T4lt39rP+UejwD+ILw8Uofdrg4Uv/t81L/3O2Pg3+HbbZY/tm5/Z76D7RrfcXuLn0/Tu6NxJySW5wd919D/aBav+hW/cb/5J+0C1f9Ct+43/AMk9y2X58ft9T4lt39rP+Uej51Jd7jJbWW6S4VjqBhy2mdM4xg+Dc4WiujW1sc9ZPK1rw2SRzwDjOCcrD7w3ucvmTERPCX2oymYuYarVPatn3hvc5PeG9zkqFuWrso2W37w3ucnvLe5yVBctUBStn3hvc5PeG9zkqC5ax5qNlte8N7nJ7w3uclQXLUVuQWz7w3ucnvDe5yVBctY42UbLa94b3OT3hvc5KguWopd1B9VZ51vJAwCof1R9VlVFjm5N81kWObk3zUWGNERRRWi648/wVVaLrjz/AASBlVo+sfoqqzOZ+i0yt5rLS00tVPHDAAZJDpaC4NBPdknCw5HcuuziCsihbDA2CKKNoETWxg9E7tewnJDz2u/JuJN9jGc5f0wxQ2O4S1tTRiFrKim2lbJKyPQchuMuIGckDHisf2TXhzw6kmboDyS9paPgBLtztsAdlloL3W0MlVNBLieoYGOlPWbhzXZHj8IW/HxZWCn6GaGCdhjEbukL8uGiRhyQ7mRK4/XCxM5uczrRyiJce3UFRcKyKlpW6pZc6cnSMAEk5PYACfJZquzXCkbqnp3tZ03QB+xDn4zhp7du0bLYob7JR3Oiq4oGgUgc1kYlkwAS44BLst6x5fU53zlqeIBWSVJq6KB0U0vT9G0ub+8DNIJOd+895z3qzOd8uBOWrvcI4KP4YurBI58MLWMGpz3VMQYNyMatWM5aRjOdlrzWO4xQQSvp3FszmNYGuDnZeMsBaDluobjIGexdePjSsiqnTxU0MeQwCOOSVsbQ0uLWhofjT8R+Hl/NYG8W1TWxkUtL0zHQvMnxZc6FumM41Y2HkVmJ1OjEZbR2xH3+rWHDF3MwjFMwkgYcJ49JJcWhodqwXZBGnOcjkuQWljnNcCHA4IPYV6OPi2drYom0NJ7tFIJ2Q/HgSB7nh2dWebiMcseq8/PK6eaSWQ5fI4vce8k5K1jOX9TppTqTM+0hjQ/VNlB+i27HmgG6hWCAnmigkdyB5qE8lLUEoibII8080yO5BuUEjkj+qPqpUP6o+qDGqS8m+auqS8m+adiwxoiLKitF1x5qqtF1x5/gkDKrR9bcqqLTLLofjqn0U6H/ACu9FhRBl0P+V3op0P8Ald6LCiDNof8AK70UaH/K70WJEGXQ/wCV3onRv+V3osSIMwY/5XeiaH/K70WJQgzaH/KfRRof8p9FiRBlEb/ld6KdD/ld6LCiDNof8p9FBY/5T6LEiDLof8rvRTod8rvRYQiDNof8rvRRof8AKfRYkQZdD/ld6KQx3yu9FhUoMuh/yu9FWTYAdvcsaICpNyb5q6pLyb5pKwxoiLKitF1x5qqtF1x5/gkDKpG5UItMraT4eoTSfD1CqiC2k+HqE0nw9QqogtpPh6hNJ8PUKqIJ0n+76hSGnw9QqqUE6T4eoTSfD1CqiC2k+HqE0n+76hV81KCdJ8PUJpPh6hVRBbSfD1CjSfD1ChAgtpPh6hNJ8PUKFCC2k+HqFGk/3fUKEQW0nw9Qmk+HqFCIJ0nw9QoIxzx6qEQFSbk3zV1Sbk3zUlWNERRRWi64+h/BESBlREWmRERAREQQvbXyipY+A7bUR00DJ36NUjYwHHbtPNEXTT5ZeDnnzh4oIURc3QREQApRFBHYoRFQUoiAtu0Ma+6UbHtDmumYCCMgjI2REjmsc2KtAbWTtaAAJHAAdm6whESUSo70RQQpRFQCpNyb5oiiwxoiKK//2Q=="}, {"timing": 1366, "timestamp": 130363547541, "data": "data:image/jpeg;base64,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"}, {"timing": 1822, "timestamp": 130364002916, "data": "data:image/jpeg;base64,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"}, {"timing": 2277, "timestamp": 130364458291, "data": "data:image/jpeg;base64,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"}, {"timing": 2732, "timestamp": 130364913666, "data": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAHyAPoDASIAAhEBAxEB/8QAHAABAAIDAQEBAAAAAAAAAAAAAAECAwQFBgcI/8QARhAAAQMDAgIHBQUECAUFAQAAAQACAwQFERIhBjETIkFRYYGRBxQyUnEVkrHR4RcjgqEzQlViZZOk4hYkU8HSCDRDcvGy/8QAGQEBAQEBAQEAAAAAAAAAAAAAAAECAwQF/8QANREBAAIBAQUFBgQGAwAAAAAAAAERAgMEEiExUUFhcaHRExQVU+HwBVKBkRYiQlSSsXLB8f/aAAwDAQACEQMRAD8A/OSIgGTgc13cRF1ajhu+08Dpqiy3OKFrS9z30sjWho5kkjl4rlICI0ZcB3nCICIiAiIgIiy1dLPRzGGrhkglDWu0SNLXYcA5pwewggjwIQYkXpqTh2Os4jraN/T01Myd0UbmsDsZfpb8RGfLJP8ANZY+GqWNkDqmaqcDG8ydHG0aXdG97QMnP9Q9mD2Fa3JZ34eURdq2WQV1E2oEr2gmoBAZnHRxB4z9c4XXqeE6WnfO81NY6GnlfA9opx0j3NcxuWjVu394N/DxSMZknKIeORe3fwlR++vL5aiOIVohMTWtJ6N0jmt0nUT/AFRzHf3b6kXC1NJQxVIrZjlpmcxsIJ0aXuAG/wAeGYwe3Pdu3JN+Hk0XY4goKeimxTMqmt0w4MkeB1omuOdzh2SThcdZmKaibERWijfLI2OJjnyOOGtaMknuAQVRZ6mjqqVz21VNNC5hDXCSMtLSdwDnlyPosCAivDFJPMyKGN8kr3BrGMBLnE8gAOZWxdLZXWmrNLdKOoo6kAExTxljsHkcHsQYJIJYmMfJG9jJBlhc0gOHh3rGvR3/AIlkvlFSUj6WOHo3AlzTnJxjYY2HgvOtaXOAHMrGE5TF5RUvRtOGlhnWjlvY9apCLpUdukqn9HTQS1EoBcWxtLjgczgdi1pqcBpczs5hdKl5rayIiii6nCkscHFFnmne2OGOshe97jgNaHgkk92Fy0QfXJbbcqbiDh+tqXNZRUtG/wB4kdUs0s/eTHfrdxHqvkaIglnxt+oUIDgg9yIPe3W5cJyezmmpKOjYy+tDA53RdcOBGtxf2tO+BntG2y89wpdW26sw/qNkODI06XDwLgWuxt2PaN8k4C4aLnGnERMdXDHZ8ccZxuZub4uvxLco7lWh8QyGjBkdu55+p62OWzi489yMY73szuHDVBNcTxRTxS6429AZIelAxnUAOwnbB8OYXikVywicd1c9DHPS9lcxHdzb3TUAvEkrqWV9v6VzmwNl0O0ZOkasHwzsvW8UVFDfLDFxLcpny3aqeaNtNC8MEbmFx1EEEljY3U7QNs777Lwilji17XAkEHII5rTtHCKdCsorlSSMbO2YPflgDX69wQC04JwckZB33Cs+2XRrZekjkb7uXFzXSAOaQ3U7DScnAOTjl2revN9hrqcwwRzQA9I95ZpZ0j3lhOsDmOp4ZPYOSqb1Smqq6owSiWaN8ZbhpBaYwwdbm0g5JxzG2y3UM3Ll01LWyw1D6eOYxQ6TLpzgZOBnxJK2nwXarudQJnSCsYDJKZZBGWgkZySQBkuG3itt/EQNsrqEwGRk7TiaSRxkc4va7U85w44aBy7B451m3jRVVs7IWEz07IQ2RrXgEGM5IIwfgPqnA49GKW23SN9PFJDOHTTGGJpPxSAjIG/PJC05XVEMropHyNfE85bqPVcDv5r0lFxYI6JsdXTGoqI3OljlyG6JDkAgAbANJ2HaG9y85XziqrqmoDS0SyOkAPZkkpNdhF9rE57nZ1Oc7JycnKqiLLQvVcFUzpqK9Gnr6ahrDAyKF8soje/U7djHEgAkDBJI2JHbheVRB63iGquVJwpR2S7VzJHx1JmjpWOikMLdJ6z3tBdlxecNLtsHIGQvJIiD0PBIoftGrdX3SO2v90ljp5ZYpHsdI8dHg9G1zh1XvcCBzaO9dDjK2Udms1JbH3amrbtSVUmtkMM7NET2tIDukjbyc0kYz8Z5Y38ciC0X9Kz/AOw/FZKT+m8liadLgRzByoBIII5hB7rhOto7TTTVlfK4sneImwRMLnksLX6iWyMLRnSOe+T2jI5HEUQhu1QRUR1LZdNQJY26QRI0P5f1SNWCOwghcVtUMdZpz4Kk05eMAYC3cM0woiLDSdLu4ppd3FXW5FbKyWmFRHDqjILgNQ1OAOCQ3OSM7ZAwrSW0NLu4ppd3FbIpah0hjbBKZAQNIYc78tlZ9FVMjZI6nmDHsMjXaDgtBwT9MpRbU0u7iml3cV0jaK0aP3TTr0gaZGnd2MA4Ox6w2O612UdS9jnsp5SxrOkJDDgNzjV9M9qUW1dLu4ppd3FXRFU0u7iml3cVdEFNLu4ppd3FdCW11kTI3GHUHhpAjcHkam6m5AJIyN91FHa66s0mlpJpQXBoLWHGTy3SktoaXdxTS7uK3o7bVySOY2F2W8ySAPiDdidj1iAteSKSMNMjHNDhlpI5jvHelFsOl3cU0u7iroiqaXdxTS7uK3aGgnrek936I9G3U7XKxmB39YhYzS1AxmCXdwYOod3HkPqlJbW0u7iml3cVuTUFVC7TJA8O06iAMkDAdkgctiCsL4ZI2Me+N7WPGWuLSA76JRbDpd3FNLu4q6Iqml3cU0u7irogppd3FNLu4refbapkUcnRBwk06Qx7XO6wy3LQcjI3GQscNJUTZMcLyAHHOMDqjU7fwAzhKS2rpd3FNLu4rbhoqmaAzRwvdCCRrx1cgZIz347FV1JUNc1rqeYOedLQWHJPPA9R6pRbW0u7iml3cVcggkEYI7ERVNLu4ppd3FXRAXWobv0DqF5pGTT0eGxOLjjGvXuBzOSR58lyV3LbcbdT01MJIaiKrY4h89PgOLMOxgk7OJcAT2BowrDMtiLiAiijp5YpXubHK2Vzeo46idIJ3+HU7f8AvYxsom4rnmnbLJA3U1/SANeQMh73DPeOuQR24CzU/EFO2qknfJUhz545nhkYHSNY0t6M5eSQRjck9ucryx3JWpmY5SzERPY7TOI6sStc/S6JoYGx8gNLmOztzJ6MDJSpv0k9LLB0RayRpDiJDzOjl3D92NvE+GOKizvS1uwIiKKLsXu2Q0dDb5qZz3iaPU97jgF2o8gQMcvH8+OrPlfI1jXvc5rBpaCfhGc4HmSqOv8A8Q1T8MlyafoWQ9Gx2j4Q0A5G+er295Has7OKaptY6oMTcmRkmkOIALXOd6HUf5LNFcLK+ja2SlHTRQl2eha3VIAA0bEkgnVnPP8AkK/atpdTyD3JrJ+kJa/3djh0XYwjI3/vcxjmt8erHDoim4pqIQwCnY937ouy47mPRpx3Z0DPfn6Ln3a7yXKJjHxtZh2skHO+lrdh2DDRt+mO5TXyy09XHNDRujdHI14Ip2Ehgzlnxc+XW57LyCkzPVYiOgiIsNM9JUupum0tB6WJ0Rz2A9q7beKZtE7X07HGYvLjqOQHOJODzB3wN8bA4ytKyVdBSx1IrqfpXv06CWB4AGcjBIxnbcbjC6VHdbJ0+qroXFgeRpZBHvHkbHfnjVvz35rceLM+DAeJakTxSMga3onRuwXEkhoaACe3Okeqx328Q3GgpIYYntkjJklc7lqLWN2GTt1fDnyXTdfrQ+Z7p6aWRsmkOxCxpLQYzpOHdb4HHJ7wPppz3a3e5VsdNAWSTxdHtTsbqOWHVnOWjqu2G2+fpZ8UjwecREXNsREQd2l4kqoGRsMcb4o2ta1pY3bEbo+0HJw7O+fRbEvFk0sczXUkWZdeTnONQeNsj++fDblzzlo7xZ46FsE9PIcBjg0U8ZAcGgOJJPWydR37+zsS3eyyse2SmlLWwmGIdBH1dn4Oc5zksOc52PNdLnqxUdHOouIKimH9FE46nOAY0Rt6zdJ6rQB2LZg4rqoi7MMbw7IOrrYz0nLOR/8AKew8gvOos70tbsM9fVOra2epe0NdK8vIbyGVgRFlRERBOY/mf90fmmY/mf8AdH5rEilrTLmP5n/dH5pmP5n/AHR+axIllMuY/mf90fmmY/mf90fmsSJZTLmP5n/dH5pmP5n/AHR+axIllMuY/mf90fmmY/mf90fmsSJZTLmP5n/dH5pmP5n/AHR+axIllMuY/mf90fmmY/mf90fmsSJZTLmP5n/dH5pmP5n/AHR+axIllMuY/mf90fmmY/mf90fmsSJZTLmP5n/dH5pmP5n/AHR+axIllMuY/mf90fmmY/mf90fmsSJZTLmP5n/dH5pmP5n/AHR+axIllMuY/mf90fmmY/mf90fmsSJZTLmP5n/dH5pmP5n/AHR+axIllMuY/mf90fmmY/mf90fmsSJZTLmP5n/dH5pmP5n/AHR+axIllCy0nQe9Re99IafUOk6LGrT24ztlYkSJqbJi4p7Oss/DzbfQTU32oZbgHNpw97MBwOBq25ZI5di5r+ErhHUOhklo2Fgc6RxnGIwMbu7RnO3eokvVMaLh6IMl1297nS7DBBeD1d99h24W1He7ZJfLtW1EMn/M708joWSmI95Y44K+nl7vnMXUcuXDsufPg+Vh71px2zz58f6qjy4tJvC1wdcPdQYNPQ9P0/Sfuuj+bPcupHwrDHDQdJqnlmhnkf0czdB0EaS045YP/wCLbm4stlQzoZ2VpjlpDSyvDGhwwctcADjfJyOzxWtTcR2ujioIadlY6KmgniJe1uSX4wef1+niuuGnseEzxv8A9js8LYy1NsziOFT9J7fGnMh4SuM0ML2PpdU0ImijMoD5BjOAO/8AlusVFwzXVdPTyh9NEah2IY5ZQ17xnBIHcunBxLRsutiqXR1HR0NN0MoDRku0kdXfcb9uF1rO+mrorLW1JZro8t1tqGNaxoO3SNduD9M571NLZtm1ZrGePj/x48u+f2dM9faNOLyjh4ePDn3R+7y9hs0dVxE+3XBz2tj6QPMLhnLQeRI8FmqLVba22T1dikqy+ncxskNQGlxDjgEEeKmgvFLR8XVVwl6R9M98uDGAXEOzg4JHep+1bTRW+Sjt1PVSsqJGGofUFoJY050gArlhGhuTGVc8vHlG7X6u+U629cX2eHf5NWs4braWnfKXU8roy1ssUMgc+Iu5ah9dllk4XrIHAzPgeGPY2eOKQF8QcRjI/wD1dSfiK1mkr6aFtU2CoA6NkcEcQiIIIGQcnPzH0VaniK2yxPMraqrl1RmJ08MYfFg5PXacu8wus6OxxdZefj9Pp2SNTaJ5x98Pr9e3Sv8AwvJQyVklJJHLS07m6mmUOla0gbuAGyveeFJqeqqhQPZJHCwSCJ8gMrm4GXYA5Zz6JV8QUs1TfpGxz4r2MbFlo2x82/4ZW3LxHaxdaq6QRVhq3w9EyN4aGZwBknPhyWpw2LKcqmomevKLy5cOlTTWM60Vf3y+rjt4ar3UolHQiUx9KKcyDpSz5tKim4craimjlY6nEkrDJHA6QCR7R2gLq/8AEVvdXxXV8dV9oMg6PogG9GXYxnOc435YU2jiK2273N8UM0Zjj0TRshZ+8d2u1k6vJZx0Nj3ovLh4+HHlz58P9um9q1ycccPVzqWnnHQ4qADE3pAHPJOMAHtW5Lw2KW0XKpqZ43z0xYGiCQOaCXYcHbZyFaa/0zKmzSU0czm0GoO6QAagT2YJ7EqbtbGWy7U9G2sdJWva8GVrQG4dnGx+qzGnsmMZcb4T29u72frw/Ru85YLfb7YLCLhcnVmTOYQ2At7s53H1Sr4dkNbEy3yCWllhFQ2WTqBjO3V3YSgr7abCLfcRVgiczB0DWnsxjcreh4np2Vb2MgliofdhTx6dLntA/rEHY/RXDHZcsMY1JiOEcud9t9zfG3Mbw5XPqOijMLwYjMyRsgLHtHPB81lHC1e57WskpX62a4i2UHpe8N78LeHElOx7mF9XNF7tJE0uZG3D3Y5NbjA27yta3X2npprK97JiKJkjZMAb6s4xv4qxpbDcRMz+/fEd/WZ/RqGlRWCsq4I5WmGPpSREySQNdKRz0hcuRjopHMkaWvaSCD2FelivVBKLfJWNqWzULiWCMAteM5GcnbsXn6+oNXWz1DmhpleX4HZkrybRp6GOETpTc/Ty48FmmBEReNBERAREQEREBS1rnuDWAuc44AAySVC3LNUMpLxQ1ExIihnjkdgZOA4E/gg154Jqd+ioikifjOl7S0481RjXPdhjS488AZXsLZfrWWtFbTxtczpAwPaZGAF7Dnrh5BIDhjw7MkrJT3axR0LWxtbFKGv0Hous3UyRpBIbnHWbzc7l2Ywt7sdWN6ejxbWlxAaCSTgADOSoIIODsV6Kz3Wip454+ibTxySQPc1w6XUGOJdgkZBwf5c1ttvVsa2OP3eAx64Q4mlYTow7pd8Zyervz22UqOq3PR5JF6S+V9qqqOgioaeON7HM1EMDS1ukBwccDOXb7l3fkZwuhU3O1MrJQyShMoldombQtMYi1jDNOnBdgHrY7cZ7U3e83p6PFgE8ld8MrJuifG9suQNBaQ7J8F6iO+0cTamKkZDBCYo+jDqZriXNeHEE4JJx2lbLr9bZK33p4i/9wJSBTNEheKgO6TUBy6PbGefZ2pux1N6ejxjmuYQHNLSQCMjGx7VC9Z9p2gUQj0xFzYOjl1U4cZT0Ia3Q4jLcPBOdufbyWtbLnbqW2wNnhgmLB+8gdANb39LnV0mOWjq4zz7N8pux1N6ejziNBc4NaCXE4AHavXSXOxMt1XS09Ox2GuZG+SEa5OoA12dJIOvUeY2I58lRl3tDqqqnmpmdI2pPuwZA1rehLm7kAc2tacduX57E3Y6m9PR5Q7HB5ovY/algkpaSCWnYGEBkrmwjWzqODnZ0jPWLSNzy7O3RuVytVTc7fJT0rKem6UT1DWxDqklocwDtaA3I/wDsdk3Y6m9PR5xTpdp1YOnOM42yvXV13tBk/wCXhgIlaGzH3dp/qPBx1RjcsOQByzstK/XKknoIYaJ0RDJzIyNlOI+jZpADXHHWIIO5znv3wk4x1Iyno86pYxzzhjXOPcBleuferfNdZZpegcyWpmmL/dmZwWNEYJLDjB1ZwDvvvzWd95s0c1Q6kEUVPIXao/dRqcS5pBDsdVuARpBA25HOQ3Y6m9PR4kggkEYI5gqWtc7OlpOBk4HIL2NHc7RPUwwPhpwzq6HvhYNMnXy5xOMgZYcOODhal5noIL3OGugmZ7m2IvgjaGOl0jJw3q888k3e2ze7KeYUua5pw5pacA4IxseS9rTXqyPllNTDAwGSRoLaRgxFry0DqkZxnsyfmapZUWqez1MzH0sfR0/RuZJHGZJJOhY1pbk6xhwPwgjt71d2Oqb09HiXNc1zmuaQ5pwQRghTGx8j2sja573HAa0ZJXsJOIqKZ90LxC11S6RrXe6tw6PpmPaCANzpD9+eSN+RGtVXC3ScQRzUj2RRvppYi9sYY1r3Ne1p2a3brN3xt3nGVN2Oq709Hm5aeeFjHzQyRseMtc5pAcPDvWJevprxbIn5q5H1cExgzT6DpiLGgFxzsTkYwAcgnPclRe7W2d5gp6XrMfqPurHAv6IBhGWjHX35DPMhN2OpvT0eQReruF0tE1FO2nihYXxHVGKYBzpiG4e12Oq0HOwI+hyvKKTFLE2IiKKLZtdMK250lK5xYJ5mRFwGcanAZ/mtZXp5pKeeOaFxZLG4PY4dhByCg71Nw79pNjda5XkapGSNmYSWlmncaASRh47Nt896yN4Qq5J4qeKohdUljnyx6JD0ZbKY+bWnIyOfL+RPN+3rj0mvp2/CWaOiZoxkO+HGnOQDnGcgK44iumsvNS1ziS4l0TCcl2rO4+bJHcScc1u8WKya10ohRmmwXfvYtbmu5tcHOY4erD5LSW1ca6a4TNlqC3U1oaA1oaO0nbxJJ+pK1Vie5uBERAREQEREBERAREQEREBERAREQEREBERAREQEREBERARFLQXOAHMoIRbFJSzVlQynoaeWpndnSyNhc530A3XRtPDd3u1ZV0lFQE1NIzpJ45XiIxtyG76yN8uAxz3S45JcXTjIt2a2V8E0kU1vqI5I5TC9ronAtkHNp8R3KDb60SCM0FQJHasN6J2Tp+Lbw7e5FaaAE8lbAcNhhw38CoH9GfqP+6BoPe37wTQe9v3gtu1W2oudQ6Kn0NbGwyyyyO0xxMHNzndg3A8SQBkkBdG58L11DYIb5qhqLRPN7vFUxFwa+TDiQA5odtpO5AHdnfAcMtI/Q5UKW8nfT/uoQEREBERAREQEREBERAREQEREBERAREQEREBSwgOGeXaoRB6Dgu/1PDF6FfS07akFhiewj4mnB2PYdguvDxiZb5fq66WY1UF3gbTyQRyui0ta9jm4dgn/AOMfzXiEWdzHe3q4ucaOEanta/mqr7n2GH21XVtM5klmiMhqXz5jdpABeXAfCXZGw1auQ5dq1ab2uXFradtXbJ5uhY1vSNq3sl6og5P0kgF0GXDt1n6r5Qi1wdOLcuVS+suNZWTRtifUSvlMbRgAuJOAO4ZWoP6M/Uf91CkHYg8kHoeELjaaJtzgvsdW+nq4GxAU5Ox1tdqc3U3UBjOnIyQFr3eS0U9vbQ2h09W98gmmrKiFsR2BAYxoLiB1iSS7rHTsNOTxur3H1Tq9x9UBvJ30/wC4UKcjGAOahAREQEREBERAREQEREBERAREQEREBERAREQERWi/pAe7dBPRP7h6hOjd4eoWRFaS2Po3eHqE6N3h6hZESi2Po3eHqE6N3h6hZESi2Po3eHqE6N3h6hZESi2Po3eHqE6N3h6hZESi2Po3eHqE6N3h6hZESi2Po3eHqE6N3h6hZESi2Po3eHqE6N3h6hZESi2Po3eHqE6N3h6hZESi2Po3eHqE6N3h6hZESi2Po3eHqE6N3h6hZESi2Po3eHqE6N3h6hZESi2Po3eHqE6N3h6hZESi2Po3eHqFVzS04IwVmVJtw3zCUWxoiKKK0XxjzVVaL4x5/gkDKiLo8ONY6+0IkY17elaS13I/VdMMd7KMerlqZ7mM5dHORexs95luVcynipGEkFxDpZHAgcxpGSfphcyiooIuOaaiLA+nbcGRaHjIc3pAMEHnsuupoxjjGeM3HhTjo6+Wec6eeO7PPnbgov0ZeuG+FeIL/ceHal1lpq+KtkkpG2SkMU8dPG1zpI5eqGOecNA575O68n+z7hZnCsnE9RPfI7YaJlUykD4jUNcZjEQXFoBadiDgdvNee3qp8fRfoel4d4JtV6u7obPOX0tihrQ2ocyoiaZNA1hsjSNfW7ct54AWvw37M+H6XjJ9Ow1tTLZa6np6xtfHG6nrBKwnqNxtg74JdtgpZT4Ai+33TgOyS2m03a4iq6CSnoqYU9tjiif0k0kg6R/UIIAAAyNTjsSuNY+GqTh2q9oEvu1Le7hw8AykhnjEkZaZC10zmHZxY3cg5AOUsp8pRfcKngSg4ysVqq6OjZauLK+g96bRUzBFTytbOIy/o8dXLHBw04GxOFmqfZDwwaf/AJO9Vj5KltS6ke57DjoTpOWBmXgua4kgt0gjnzSynwlF9lq/ZhYK2+3bh7h6quz75b/d3kVLozHIyRzA8tDWg9USA5+uy7H7N+Grhw5FVRVz2Wq3urj0pMMUtQGTNjaXTCP4c5OSHYBACWU+BIvvE/sWshqgKa8VvurS+GSWQNaWzv6L3ZmNP9bpRk9ukkYyvlvtCsFLwxxAy008s0k8NNCavpS06Z3MDntbgchkDfJ2KWU8yiIqgpAJ5BQruBc5rGgnkAB2lBGl3cml3cvW0PDdsoqgScQ3KF1NA3/naakqWsqYn/1WMDmuD3dhxs0ghxbjJwW7h23XP/iGamvNPT01AHPpm1AdrqGdIGtOzdsg/XONgMkB5nS7uTS7uX2iv9m1FTcQ8P081hu0FM8P9+a1z5GEacxgSaWgyF2xDMt3ABzlWm9ndijNxdLS1LYWmR0r2TFptcbaVsrDK12SHvcS0hx2xgbqXC0+K6Xdyg5BwV9L4/4NtVFwyy+8OMnbRulYyVlVJ+8gywFrS0Z6zg4OOTkDGBzx81du0E8+WVUVVJeTfNXVJfhb5pKwxoiLKitF8Y8/wVVaL4x5/gkDKstNPJTVEc8J0yRuDmnGdwsSLUTXGGJiJipdNt3e0gijoQRy/chab6ud9aasyObUmTpekadJDs5yMcjnuWBFrLPLKKmWcdPHGbiG4LnXi5/aQraoXDX0vvXSu6XX82vOc+OVtXHiS93J1S6vu1fUGpa1k3SVD3CRrTlrXDO4B3A7CuSiy26snEV7lpG0st4uL6ZsJp2wuqnlgiOMsDc409Vu3LYdyyy8VcQSw0cUl6uTo6JwdTNNS/8Ackci3fYjsPYuKiDuw8YcSQ1j6uK/3ZtU+PonS+9yFxZkkNJznAJJ8CSVoW68XK2XH3+319VTVxJJnilc15zzyRuc9vetFEHWfxLfH3f7VdeLj9p6S33sVLxKG9wdnIHgopOI71R2ye3Ut1roaCckyU8c7msfnnkA437e9cpEHSjv13juclxjuley4SN0vqm1DxK4YxgvzkjAA58gr23iO9Ww0/2ddq+mFMHiERVDmiMOOXBoBwATz71ykQdR3EN6c6ZzrtcC6aZtRKfeX9eVu7Xnfdw7DzC0ayrqK6qlqa2eWoqZXapJZXl73nvJO5KwogIiICtqBA1DOPFVRBbLfld6/omW/K71/RVRBYFoIIDs/X9FbWOt8fW59bn/ACWNEFst+V3r+ihzs42wByChEBUl+Fvmrqkvwt80lYY0RFlRWi+Mef4KqtF8Y80gZVtWtsL7hTsqYjLE94YWh2nmcc1qqWktcC04IOQR2LTL1tZw5SOqZTDJLBGx8mGadY0RyaHEvJGHHmG47t91tRcFwS183RVMpp4qoxaJGNa5zGyNY7+tn+tzDcbdnJePNZVGF8RqJjE92t7C86XO7yO0+Kym63Egg19WQTkjpnb7Y7+5bvHoxWXV1rPZaa5T1lNrfHLT1DXOcTsKfUWvcfFvVPqt13CsHuUle2VxhMb3thONTMwvkZkhxPJo5huezZeUFRMJJJBLIJJAQ92o5cDzye3KzC41zWRsbWVIZGMMaJXYaOW2+3MqRMdFmJ6vS0/BL5JKkPrWsjgkewucwN1BvR7jU4DfpBzPZ2rzN0pDQXGppHSNkMEjo9bOTsHGQpFyrw6NwrakOiGGHpXZYMYwN9lquLnOLnElxOST2pMx2ERPahNkRZaEREBERAREQEREBERBkpmMkqYmSv0RueGud8oJ3K6dxpKdtNVyQ08tOaepEAD5NWoEO57DrDTvjA3Gw7eQs1RVVFS2NtRPLK2MaWB7y7SO4Z5cgszE2xljMzExL0zOG4KmioXQieJ0jGufIcvLiYy8hrcAO3BxpccD4sFYKzhV8LtEM008hDy0tpnBr9IedIJ31dTdpG2e3C83k7b8uSZPfyWd3Lq5xp6kTwy8nqncJMY+ojdWvMkOrUG05dsJHsB2OcfuyScbZHPK59/s0dqo6VwdM+WSWVjnPZoa4NDMFm5y05OHdvcFxMkckJJO6sY5XxlccNSMrnK48BUm5N81cKk3Jvmty7wxoiLKitF8Y8/wVVaL4x5/gkDKtq2UNRc7jTUNFGZaqpkbFEwdrnHAC1V2+CG1juMLOLZU09JXe9R9BNUEiNr9XV1YB2zty7Vpl9cn/wDT7VDhsinvFHNxLHOQ+nE2IdOjV0Y6urpNwcnAweXavD8D+yniLiqplAppKCkjbITV1EZ6MvY7SWDHM5BHkV+mH22pvlL7jxLa57ddJLg9or7JPJGA8U21QH7EAt/d4dnfAXzb2DVNypfaLxXYqi8VtdQ0MVQ1jZZ3OY5wmwZNJJAcckk+JWblqofNeC/ZjLxJwpLxDU322We3x1Bp9da4tBIA31cuZwtC98BupuILVZ7De7XxBVXAlrPs+UPbGc/1jnbbJ+gK+icFXu/W72NUkVu4FivdE2rdJ7xM5tTGX6zuadvXyOWTt29y9xDbLHauOfZ5fZ7VS2O8XWOWOeiiYI2tkdDsdPYQ46e/rDKWU+Z1HsMmMdVR2zii1V/EdJH0tRa49nMHcHZzncc2jmOWV8z4Y4cuPEnENPZbbDmumeW4k6oZj4i7uAAOV914E4W4go//AFFXe5VdLVQ2+KaqnkqpIyIpY36tADjsfiadvlPcp9lFDNSe2q43yoo3wWi9mrFsqXABkxc8PGn6sa4pZTx919ikjLfcHWDia23q6W1pdW0EA0vjxnIB1HJ2IwQ3l37LjcE+zB994c/4hvl8ouH7I6QxRVFSNRlcDjYZAxkEc87HZfRvYjw7e7B7TuJ7lfaWppLfTwTiaonaWxyEyBwIJ2cMNJyP+6w+0aln4r9h3CtRwtSz1lNT1BE0FPGXOacObktG/P8A/oJZT5txP7Lr3ZOKbXZYHQXD7V3oamB37uZvad+WAQTzGDnJXp6r2GzGKqpLXxRarhxFSR9LUWuPZzB3B2ck7jm0cxyX1u0VFPYH+yuzX0iO7iCRoY/4oz0OkA925DfqF4/2d8MX+g/9Qd8uNdSVUNBHJVTPqpGERSRvJLAHHY8wcf3T3JZT4XwpwzcuKOIoLLa4s1kriD0mWtjA+Jzj2AL6FefYvJDbbjLw/wAS22919saXV1DANMkWM5A6xydjsQOR7dl7b2P0M1H7XLveKijfTWy+tqTa6l4AbNmQSDTv2tBP0C1PYJw7e+GuNeKa/iOnqaWhpaSVlRPPGWsldrDtQJ2cMNccjv8AFJkp8/4K9lz73w4OIb7fKLh+yPeYoaipGoyuzjYZaMZBGc9h2XE9ovAtw4GuVPBWzQVVJVx9LS1cByyZn/Y7jbxG5X1X2hUVTxR7DODp+F6Serp6WTRNT00Ze5jg0t3aO4gj+ILS9uzTbPZnwBZrlteIYA6Rjt3MaI2gg925A/hPcllPhSLqW+go5oWOrLhDTSyk9G1zS9rQOZkLcloO4AAJ7wBgrrRPsddxRa22+2FtJjRPA+QtEr8uwQXvOMjT1S7ntntMnOnDLVjG+EzTyqL6lV2akZxvUwi1UElE/o3StjGTCC5w3YXjRkDcjIaMEDfC4VBS09uoZxdLZRStdPFFA/OXvbISdYcXAYDWnGccxlYjWiXDHbMcouI6ebxSL6VcLTQtrnvpbZTNqTSyup7fM0NJc2Rg1EB5DuqXEb76TzWA2OmhuFykoLdSVchdF7vTSSaow3lNpw7J0uw3nsCT4pGvBG24TF19/f8A2+eIvotdarNHR1hoaemmtrYqpz60yZfFM17hEwHPIgMwMbhxP0+deS3hnGfJ20daNWJmIEUKQtuyVjm5N81dUm5N81JWGNERRRWi+MeaqrRfGPP8EgZVIJByDghQi0y95+1jjD/hptmF3qA0S6/e+kd7wW6cdHrz8Pb357V5ex3+7WGqlqbPXz0c8rDHI+J2C5pOSD5hctEHoeF+M+IuFmSssF2qKKOU6nxtw5hPLOlwIz44WpfeIrxf7i2vvFxqaurZgMkkfuwA5w3GzRnfZclEHr7h7SeMbhanW2s4hrZKNzdDmagC9vaHOA1EfUrJe+Oaqu4R4XstK6phfZnOl94dJ1jIT1CzHwho2C8YiUW9ZevaJxde7WbddL9Vz0Thh0ZIbrHc4gAu88rT4X4z4i4WbK2wXaooo5TqfGzDmOPLOlwIz44Xn0Qt0rxfLperobjda+oqq7bE0jyXNxyx3Y8F3Lj7SeMbjanW2t4grZaN7dD2FwBe3tDnAaiPqV5FEHal4pvstJbqZ91q+gtzg6kYJCOgI2BbjkuhffaFxZfbZ9n3a+1dRRkAOiJDQ/HzYALuXbleVRB6HhfjPiLhZkrLBdqijjkOp8bSHMJ79LgRnxwudfLzcr9cH114rZqyreADLK7JwOQHcPALnogJuiIJJJJJJJPMlWllklLTLI55a0MBcc4AGAPoAqIgboiICIoQSpUBEBUl5N81dUl5N81OxYY0RFFFaL4x5qqtF8Y8/wAEgZVIUeaLTKceITHiFHmnmgnHiEx4hR5ognHiEx4hR5p5oJx4hMeIUeaeaCceITHiFHmnmgnHiEx4hR5p5oJx4hMeIUeaeaCceITHiFHmnmgnHiEx4hR5p5oJx4hMeIUeaIJx4j+aYHzBVUhBbA+YKDt2gqEQFSXk3zV/NUl+FvmkrDGiIsqK0Xxjz/BVVovjHmkDKrwxvmlZFExz5HuDWtaMkk8gFRbtnqmUVwjmlB0aXsJbzbqaW6h4jOR9FqGVprTVxU75i2JzGAlwZK1xDQ4t1YB3GQRkbKtrtVxu0z4rVQVdbKxutzKaF0haM4yQ0HAyQurBWx0FDMz3qjqC2GSCExRO6Qh+Qd3N2b1nHv7B3ivC97prPQ3uKqo2Vjq2njijilDujLmzRv6+lzXYww8jzxnbKs8OSR3uHUwTUtRJBUwyQzxuLXxyNLXNI5gg7grPSW2urJIY6OiqZ5JtXRNiic4yad3aQBvjtxyX1S0e021uNHV8QU89RUNkD6ikipY+ifIJ2vbOHFwOpsbejAI5Y3wStKn9pum2QskqbkKuKOvjDWRs0O6ZhETs6hjQcbY5cu5Ztp80pqOqqnStpaaaZ0TC+QRxlxY0EAk45DJG/iqVEMtPPJDURPimjcWvje0tc0jmCDyK+uz+0Th59PVR+71xFTDJHEG0sY9yYWx4gZ1xqZqYTnbnnGSVzuM+N7DebPdIKClqYveSehpX08bY4pPeDIajWHE6yw6CAPDJACWPCv4bvjIKed9luTYajT0MhpZA2XUMt0nG+RuMc1zYYZZi4QxPkLWl5DWk4aBkk+AX1S18e8P0NXFVvjuk75aOho6indBGI2CAM1OaekOskswAQ3AcVsRe0ezQU9CIDcYJo6GSjLoKWMdAHQBgLQX5cdbQ7YsaeenUSlj5TTW+tqoJZ6WjqZoYsmSSOJzmsw0uOSBgYaCfoCexavkvsVL7TrQyasD2XGJr2MjppY6aJzoQKR8TnaS/H9I7XjO/butKr444ekirJGxVxrgydsUvu0YNRJJSxRdM/r9Q64y4gavizzylj5lUUNXTR9JUUs8TNZi1SRlo1gAluSOYBG3iFr+S+zXD2l2Sulq5NVzhrJ5ZpYa91JE+Si1iLAYOk63wSNzkbPz4LicV8dWi62a5W630E1LTzxu6GLoYw1svvZlDzg7fuyW7ciSBtulj5p5IiKoeSeSIgJ5IiB5IiIAUqFKCEREBUm5N81dUm5N81JWGNERRRWi+Mef4KqtF8Y8/wSBlRFZgy7fktMqosn8IT+EIMaLJ/CE/hCDGiyfwhP4Qgxosn8IT+EIMaLJ/CE/hCDGiyfwhP4Qgxosn8IT+EIMaLJ/CE/hCDGiyfwhR/CEFECvt8oU4HcEGNFk8gn8IQY0V8/3QjgMAgY8EFFSbk3zWRY5uTfNRYY0RFFFaL4x5/gqq0Xxjz/BIGVWj+I/RVVmc/JaZW81sOoKttIKp1NOKUnAmMZ0E/wD25LW8l9hufG9gn4RmghJ6aSmMLaQRnqEtwBnGMD69i9GhpYakZb+VU46uplhMbuN2+R01NNUydHTRSTSYzpjaXHH0CxlpaSDkEbEFew9m95p7TV3JtVWxUbamnEYdMyUscQ9rsF0JEjDtkFvdghe9nvHAgp4q2vfSV9PUSVL5oX0rX107jUAse6QAFnVDtiW5B5Hs81u9PiQBIJGcDmo819ZuvEnDrOHL1baCotvvlXRYdUQWzoopnNnL2sDdGWuEe2rAGcHORqXybyVROPFR5p28k27kE+ajzTyTyQT5qPNPJPJA81Pmo27lPkgjzTzTyTbuQPNR5qfJQgzxwFwBJwsxoZQXNLXhzRqI0HYd66XD1bBQXq3VtRF09PBOyV8e3Wa1wJG/0X0z9otqM1S6Rte98zGNlmDXB0rR0vUH73LB1275IyCcLdQxb5F9nzFrXBsmk8joOCsTqVzR8RyRkZGMr6DbeNX2untdJS11xnpaad8konaDluA1jGNLyGgDV28z28l5/iqvoblXxVNujniaYWtkjkxpY4baWYJ6oGAEqC5eWIIODzUu+EfVXlIdI4jkqP8AhH1WG2NUl5N81dUl+FvmkrDGiIsqK0XxjzVVaL4x5/gkDKrM5n6KqtH8R+i0ytum6jZfQa/2W3Oj4Xp7o6ut7qqRomND02mQRObGWuBcACR0g1DOwI3O+A+f7puvcT+zK8Uk0jbhVW2kght8dxnqZJnPihje4ta0ljXFzyWnAaHA9hUzezO6Ur6ySvuFqo7dTRwyi4SyvMEzZv6Po9LC52cH+rtg5Synht03XrI+Abu7i+q4de6ljqaWN081Q+Q9AyENDul1YzpwR2Z3Gy24/ZrdJZad8FdbJLXNRyV4uYkkFO2Jhw/VlgeCCQNOnOSEsp4jdN17d/s2ucZknluFrjtLKNld9qOkk92Mb3aWgdTWXF2Rp05ytR/AF3HF9Lw9E6llqaqJtRDUMkPQOhLdXS6iAdOAezO3JLKeT3Tde4i9mlzqHUktDcbVV22ojmlNxileKeFsP9J0hcwObjI/qnOdsrh8T8M1HD8dvnkqqSsorhEZqaqpHOMcgDtLh1mtcCDzBAQcPdN1GybIJ3TdRsmyCd03UbJsgboFCtyCCwc4ciQmt/zH1VSo2QWL3/MfVNbzsXFUVgrYI/4R9UKh/wAI+qgoqS8m+auqS/C3zSVhjREWVFaL4x5/gqq0Xxjz/BIGVWj5n6KqtHzP0WmVs+C+u13tEsE3sz+x20ddNWPhdAKOpf0sNO/TGBLG92XNALXFrQdiXA7YXyLHivexeztz4mOfdGhxAJDYNQH0OrdejQ2TV2mZ9lF199rybVt+hscROvlV8uEz/p1B7QbRW2xlmulJXi0zWWkt1Q+EN6Vk1O9z2vYCcFuXcjhZrj7Q7DebfUcP3KiuUPDscFLDRSQFjqhnQAgF4J0nUHOzg7bc1yP2c/4r/p/9yfs5/wAV/wBP/vXp+EbX+Tzj1eP4/sHzPLL0d+XjywS3Osvk0VaftNktnqKBrmZioRCxrHtOPjyAdzjYjxWtT+0Sy0dDBw5T0dwdwuLfPRSyP0CqL5Xh5kAzp2cBhueWVyf2c/4r/p/9yfs5/wAV/wBP/uU+EbX+Tzj1P4g2D5nll6OtP7QrFU2p/DE1Hcm8L+4xUrJW6PehJHIZOkIzpwXEjTnl2qzPaNYoeJaG7Q0FzPuNPHa4oXPZpfRCIscXEbiUlxIx1excf9nP+K/6f/en7Of8V/0/+5X4Rtf5POPU/iDYPmeWXo69B7QrFZ7dTcPW2juU3DskFVDWyT6G1LzOAMsAy0aQ0Yzz8F5XjjiK33S32K0WSGqZbLRDIyOSr09LK+R+p7iG7AcgB4Lp/s5/xX/T/wC5P2c/4r/p/wDcnwja/wAnnHqnx/YPmeWXo+f58Ez4L6B+zn/Ff9P/ALk/Zz/iv+n/AN6fCdr/ACecep8f/D/meWXo+f58Ez4L6B+zn/Ff9P8A7k/Zz/iv+n/3p8J2v8nnHqfH/wAP+Z5Zej58T4JlfQf2cf4r/p/9y8hdbX7hcail6bpOieW6tOM+WVx19i19niMtTGr74erZfxLZtrynHRyuY7pj/cOaOalbHu/97+Se7/3/AOS8tS9tw18+CjPgtj3b+/8AyT3b+/8AySpLhr81ZZhTY/r/AMlPu/8Ae/klSXDX8lD/AIR9VZ7dLi0nkqu+EfVRVFSX4W+auFSbk3zSVhjREWVFaL4x5/gqq0XxjzSBlVo/iP0VVZnM/RaZTst+G83KCJsUNwq442jDWtmcAB4DK0d13KKgt8Rhlrq+ncWAST041HLCNgxzT1n77jIxkb7Ow9pOHGJc9Tcr+aL/AEtqfb11/tOt/wA935p9vXb+063/AD3fmutY6a01d+rHOhi+zGDWxlRUFjg3UNm7t1OxkDJA7Tywt1lt4cbaYpDUMlqNUxlAnIcP3L3NYAcDZwaM75d3jZJ2nOOFy8+WeljNTh5Q859vXb+063/Pd+afb11/tOt/z3fmuvT2q2QcU0lM6f3qie9+SZIwCBnTuHfTnpz2c8rp3Cz2uN13bHDRvcJHMpxHV9cOLGkANLgGtBJznUf6o5ZUnasomrlJ1NCJiN3n3R4PK/b12/tOt/z3fmn29df7Trf8935r1cNmsooqUVApo6qOM9KySbBmm6OQ6NQlIA1taD1Wncb7qk9qsgY8RClLnROdMfev/bP93a5rWdbrAyFzd9XLHPdPe8uss+20Lrc8o+/vq8v9vXb+063/AD3fmn29dv7Trf8APd+a9NQ2mymCmbVmmBDY/wB772AZZjq1xu62GtBAGoAbYOdwtOW2W58tcWCJkkduZOYmSF7IZS9gdggkkNaS7GTjfuT3rLrLUamjM1ueUOL9vXX+063/AD3fmn29dv7Trf8APd+a6clrtjaeuhp6uOoqWPiMU3TBrdBBLwAQMkY55PMDmuzcaDh6ir2dDHRVFG1k7nCSZ+t7YwC0gtlxl+cA7dvV2SdqyjtknU0Ymtzyjpbyf29df7Trf8935p9vXX+063/Pd+a9FQ22z3CS3Op2Qs6SSEVED53B2kuw7TgnfBycnYNz2kDx0wa2Z7Y3amBxDT3jOxWsdozy4XLppxpZzMRjy7ob/wBvXb+063/Pd+a0pJ5ppHSSyyPe45c5ziST4lYlKuWeWXOXbHTxx44xS2t3zO9VHSO+Z3qoTdZbT0jvmd6oHvJ+N3qq75UhBbW75neqdI75neqqm6CCQVDsaR9VJyjvhH1QUVJuTfNZFjm5N81FhjREUUVovjHn+CqrRfGPP8EgZVZnM/RVUtODnC0yt5ompvyn1/RNTfld6/ogeaKdTflPr+iam/KfX9EEKQS0ggkEbghNTflPr+ijU35T6/ogk7nJO6jzU6m/KfX9FGpvyn1/RAUgkZwSE1N+U+v6Jqb8p9f0QQiam/KfX9E1N+U+v6IAODkEgqFOpvyu9f0TUz5Xfe/RAapTU35Xev6Jqb8rvX9EEdvNQram/KfX9FGpnyu+9+iABurKA5vyu9f0TW35Xev6IBUH6qdTflPr+ijU35Xev6IIUu+EfVA5nyu+9+ihzs4AGAEFVSbk3zV1SXk3zUWGNERRRWi+Mef4KqtF8Y8/wSBlQAk4HNFaP4vJaZNB7x6ppPePVTsm3cUEaT3j1TSe8eqnZNkEaT3j1TSe8Kdk2QRpPePVNJ7wrbKNkEaT3j1TSe8eqnZNkEaT3j1TSe8eqnZRsgaD3hAw94RWQV0HvCaT3hW23UHCCNJ7x6poPeEVhyQRoPeFGg94VlBwgjSe8JpPePVNlI5oI0HvCOaWndWKP+EfVBjVJeTfNXVJfhb5qLDGiIoorRfGPNVVovjHn+CQMqtHzP0VfNWZz8lplbdfU7j7L4KThmWr9+lNfDCZnjA6M4GS0dvnnyXyvzXem4uvk1o+zJLg80ekM06WglvcXYzjzXfRz0sYy9pF9HXTywi9+LbPBPDtPfhdH1lS+COigbN1XxM1ZkazGqRzWj4s7nwXZtHs3q7je6endXU1PQzGF4mJ6RwjlkexmzctLssOcHG43xuvEU9bUU1LV08ExZDVMayZoA67Q4OA+80HbuXZpONOIKOhpKSmuT44KUsdCBGzU3Q4uaNWMkAkkAnAye9cHJ1aP2cXWuqImUVZbpY5o45o5dcjWuZJMYWndgI6zSdxy9Ftw+ye/VFDSVlPU2+SkqSzo5sytYWvJDXZdGM5I+EZduNt1xo+PuJY4WRRXIRsY4OaGU8TcYfrABDeQcSQOQydt1r0nGN+pKSkpqe4FsVLjov3TC4AZw0uIy5vWPVJI35KcV4NPiWyVXD13lt1cWGeNrXEsDm7OAI2cGuB35EArl7revN1rLzXGruM4lnLWsBDGsDWtGGtDWgAAAYwAtHzVQ3TdPNR5oG6bofqoxugkd6lOxDz5oG6jJTzUIJ7VYDJAUAJ5oO1ZaSCpuEMFQ+WOA5L3xMD3AAZzgkDzJAHPsXra3g2ip/thjLjUPmo4PeYMwNDJmaGv2OvJPWO7Q4ADJOCF4ahuU1FPHUU00sFRHu2SJxa4fQhdEcV3cdLi7XEdK4Pfid3WcMYJ38B6DuW7YqXprXwlbK232+okus9O+eZtPKx8DTpe5hc3ADicHGBqA1cwvOcRWeSz1raWYOdmMO1kdV57S3vGQRntwsEvElznihjluVc9kJzGHTOOg4IyN9tiR5rUq7lNVdGameecxsEbOleXaWjk0ZOw8FbKaMjdLyAqP8AhH1VnHUSSeaq74Rv2rm2oqS/C3zV1Sbk3zSVhjREWVFaL4x5/gqq0XxjzSBlVo+Z+iqrR/F5LTKfJTpOkO0nSdspuv0XebFT1vsnjgp7JRUZho/eCJ4GtIcI4C6eOojJDic5w4guzp20JMlPzn5J5L9JX/huy3W5sZ9mstVsobpRQ1EM9qgphUMkIbiOZg1ntLsnfPZheR4usdTc+Frw6bhuGiudLejTW9lDbhC+WHS8vbhjQXtaGg6jn6qWtPjfknkvv1wsdB0l+tstkoIOF6Xh+Oqo7o2kaJHTaWESdPjU5znOcNOezktm5cP2qS7cTWu42agoeF6GCkdQXCOlbG8lzoxnp8Zk1hzs5J5diWU/PHknkvuPGNglrbZxrSjhmmpX2+vghsxo7eI5Jml5aWhzRmXMY1HOe9btJYaFj7ba32SiHDkvDbququLqNpkZUaHFzzORqa5rwBpzgcsJZT4D5J5L7xe7NRsj4it4sdDHw7ScPMq6G4to2te+fQxzXdPjLi5znDGezkvg+/erEohSFCsgHkoP0U7pugjyQbqFYckEqOzkiboI8lCnfvRqCeSKVG6CPJQ74Rt2qd+9HfCPqgoFSbk3zWRY5uTfNJWGNERZUVovjHn+CqrRfGPNIGVWj5n6Kqsz4vJaZTsunJfrrJZY7RJcKp1sY8vbTGQ6ATjs7thgcgckcyub5L6ZSce2xlNE19JUxua0Asja0tH03Gy9WzaOlqzPtM939LeLbNo19CInR0t+++qeBrbzc6+kipa641tTTQ46OKadz2MwMDAJwNlu0vFt9p7hTVv2rWTVFOx8cLp5nSdGHNLTjJ22P4L237QLV/0K37jf/JP2gWr/AKFb9xv/AJL1+5bL8+P2+rwfEtu/tZ/yj0fO5LxcpLa23yXCsdQNORTOmcYwc52bnHNJ7vcai3xUFRcKyWhix0dO+ZxjZjlhpOAvon7QLV/0K37jf/JP2gWr/oVv3G/+Se5bL8+P2+p8S27+1n/KPR4B/EF4eKUPu1wcKX/2+al/7nbHU36u22yx/bNz+z30H2jW+4vcXPp+nd0biTkktzg77r6H+0C1f9Ct+43/AMk/aBav+hW/cb/5J7lsvz4/b6nxLbv7Wf8AKPR86ku9xktrLdJcKx1Aw5bTOmcYwfBucLRXRra2Oesnla14bJI54BxnBOVh94b3OXzJiInhL7UZTMXMNVqntWz7w3ucnvDe5yVC3LV2UbLb94b3OT3lvc5KguWqApWz7w3ucnvDe5yVBctY81Gy2veG9zk94b3OSoLlqK3ILZ94b3OT3hvc5KguWscbKNlte8N7nJ7w3uclQXLUUu+AfVWedbyQMAqH/CPqsqosc3Jvmsixzcm+aiwxoiKKK0Xxjz/BVVovjHn+CQMqtH8R+iqrM5n6LTK3mstLTS1U8cMABkkOloLg0E92ScLDkdy67OIKyKFsMDYIoo2gRNbGD0Tu17CckPPa78m4k32MZzl/TDFDY7hLW1NGIWsqKbaVskrI9ByG4y4gZyQMeKx/ZNeHPDqSZugPJL2lo6gJdudtgDsstBe62hkqpoJcT1DAx0p+JuHNdkePVC34+LKwU/QzQwTsMYjd0hflw0SMOSHcyJXH64WJnNzmdaOURLj26gqLhWRUtK3VLLnTk6RgAknJ7AAT5LNV2a4UjdU9O9rOm6AP2Ic/GcNPbt2jZbFDfZKO50VXFA0CkDmsjEsmACXHAJdlvxHl9TnfOWp4gFZJUmrooHRTS9P0bS5v7wM0gk537z3nPerM53y4E5au9wjgo/hi6sEjnwwtYwanPdUxBg3Ixq1YzlpGM52WvNY7jFBBK+ncWzOY1ga4Odl4ywFoOW6huMgZ7F14+NKyKqdPFTQx5DAI45JWxtDS4taGh+NPWPV5fzWBvFtU1sZFLS9Mx0LzJ1sudC3TGcasbDyKzE6nRiMto7Yj7/VrDhi7mYRimYSQMOE8ekkuLQ0O1YLsgjTnORyXILSxzmuBDgcEHsK9HHxbO1sUTaGk92ikE7IevgSB7nh2dWebiMcseq8/PK6eaSWQ5fI4vce8k5K1jOX9TppTqTM+0hjQ/VNlB+i27HmgG6hWCAnmigkdyB5qE8lLUEoibII8080yO5BuUEjkj/hH1UqH/CPqgxqkvJvmrqkvJvmnYsMaIiyorRfGPNVVovjHn+CQMqtH8W5VUWmWXQ/Hwn0U6H/K70WFEGXQ/wCV3op0P+V3osKIM2h/yu9FGh/yu9FiRBl0P+V3onRv+V3osSIMwY/5XeiaH/K70WJQgzaH/KfRRof8p9FiRBlEb/ld6KdD/ld6LCiDNof8p9FBY/5T6LEiDLof8rvRTod8rvRYQiDNof8AK70UaH/KfRYkQZdD/ld6KQx3yu9FhUoMuh/yu9FWTYAdvcsaICpNyb5q6pLyb5pKwxoiLKitF8Y81VWi+Mef4JAyqRuVCLTK2k+HqE0nw9QqogtpPh6hNJ8PUKqILaT4eoTSfD1CqiCdJ/u+oUhp8PUKqlBOk+HqE0nw9QqogtpPh6hNJ/u+oVfNSgnSfD1CaT4eoVUQW0nw9Qo0nw9QoQILaT4eoTSfD1ChQgtpPh6hRpP931ChEFtJ8PUJpPh6hQiCdJ8PUKCMc8eqhEBUm5N81dUm5N81JVjREUUVovjH0P4IiQMqIi0yIiICIiCF7a+UVLHwHbaiOmgZO/RqkbGA47dp5oi6afLLwc8+cPFBCiLm6CIiAFKIoI7FCIqClEQFt2hjX3SjY9oc10zAQRkEZGyIkc1jmxVoDaydrQABI4ADs3WEIiSiVHeiKCFKIqAVJuTfNEUWGNERRX//2Q=="}, {"timing": 3188, "timestamp": 130365369041, "data": "data:image/jpeg;base64,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"}, {"timing": 3643, "timestamp": 130365824416, "data": "data:image/jpeg;base64,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*********************************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"}]}}, "final-screenshot": {"id": "final-screenshot", "title": "Final Screenshot", "description": "The last screenshot captured of the pageload.", "score": 1, "scoreDisplayMode": "informative", "details": {"type": "screenshot", "timing": 3644, "timestamp": 130365825381, "data": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAHyAPoDASIAAhEBAxEB/8QAHAABAAIDAQEBAAAAAAAAAAAAAAIDAQQGBQcI/8QASBAAAQMCAwQIBQEGAwYEBwAAAQACAwQRBRIhBhMxUSIyQVJhgZHRBxRxkqGxFRcjM2JjFkJUJGVypMHiCEPh8TRTVYKU0vD/xAAZAQEBAQEBAQAAAAAAAAAAAAAAAQIDBAX/xAA1EQEAAgECBAMECQMFAAAAAAAAARECAxIEITFRQWFxExSRoQUVFkJSgZLh8FNUsSIyctHx/9oADAMBAAIRAxEAPwD85IiAXNhxXdxEXq1GzeO08DpqjBcTiha0vc99LI1oaOJJI4eK8pARGi7gOZsiAiIgIiICIraulno5jDVwyQShrXZJGlrrOAc02PYQQR4EIKkXTUmzsdZtHW0b9/TUzJ3RRuawOtd+VvWIv5XJ/Ktj2apY2QOqZqpwMbzJu42jK7dve0C5v/kPZY9hWtks74coi9rDMEFdRNqBK9oJqAQGXtu4g8X+t7L16nZOlp3zvNTWOhp5XwPaKcbx7muY27Rm1b/EGvh4pGMyTlEOORdu/ZKj+deXy1EcQrRCYmtaTu3SOa3KcxP+UcRz5a6kWy1NJQxVIrZjdpmcxsIJyZXuAGvXsy1j235atkm+HJovY2goKeimtTMqmtyw2Mkdh0omuN9TZ1yTZeOszFNRNiIpRRvlkbHExz5HGzWtFyTyAQRRX1NHVUrntqqaaFzCGuEkZaWk6gG/DgfRUICKcMUk8zIoY3ySvcGsYwEucTwAA4lbGKYZXYTVmlxSjqKOpABMU8ZY6x4Gx7EFEkEsTGPkjexkguwuaQHDw5qtdHj+0smOUVJSPpY4d24Euab3NraC2g8FzrWlzgBxKxhOUxeUVL0cThpYZ1o5bse9Uwi9Kjw6Sqfu6aCWolALi2NpcbDibDsWtNTgNLmdnELpUvNbWREUUXqbKSxwbUYPNO9scMdZC973Gwa0PBJJ5WXlog+uS4biVNtBs/W1LmsoqWjf8xI6pZlZ/EmOvS5Eeq+RoiDLOu36hYQGxB5Ig73FcS2Tk+HNNSUdGxmOtDA5266YcCM7i/tadbC/aNNFz2ymKtw6ss/oNkNjI05XDwLgWutp2PaNbk2C8NFzjTiImO7hjw+OOM43M3N83r7S4lHiVaHxC4aLGR2rnn6npW4aOLjx1Itb3vhniGzVBNiJ2op4pc8bdwZId6Ba+YAdhOlj4cQuKRXLCJx2rnoY56XsrmI8ure31AMYkldSyvw/euc2BsuR2S5yjNY+F9F1u1FRQ45gMW0uJTPlxaqeaNtNC8MEbmFxzEEEljY3U7QNL666LhFlji17XAkEG4I4rTtHKKehWUWJUkjGztmD33YA1+fUEAtNibG5FwddQpPwzFGtl3kcjfly4ua6QBzSG5nWaTc2BubcO1b2M47DXU5hgjmgB3j3lmVm8e8sJzgcR0PC57BwUTjVKaqrqjBKJZo3xltmkFpjDB0uLSDcm3EaaLdQzcvLpqatkgqZII5jFDlMxGgbc2F/qStvEKbF/wBoxmrMrqqpkLGuEgdmeTYtuDYG51HZfVbn+ImnD6yikp3SxTg/xXyOMrnF7XFzzexNm2Bty8b1yYzTMlwkU8MhjoagzDOGtJbmaQ2zdCRl6x1N/BOXc59mpUYZikFNJNJHIaeIB7pGSB7Bd1gbgkdYdnatarirKN8cdTvYnFrZmAu7HC4d5he3hm1BhhqKevgE1JPZjoYmtiaGdLNYAWzXcDfm0Ly8dxL9q13zJZkJaGlt+HHh4JNVyIu+bQc9zr5nOdc3NzdRRFloXVbFUzpqLGjT19NQ1hgZFC+WURvfmdqxjiQASBYkkaEjtsuVRB1u0NViVJspR4Ji1cyR8dSZo6VjopDC3Kek97QXXcXmzS7SxuBcLkkRB0OxIof2jVur8Ujw1/yksdPLLFI9jpHjd2O7a5w6L3uBA4tHNehtlhlHg2DUmGPxamrcWpKqTOyGGdmSJ7WkB28jbwc0kWv1zwtrxyIJRfzWf8Q/VWUn87yVTTlcCOIN1gEggjiEHdbJ1tHhNNNWV8riyd4ibBEwueSwtfmJbIwtF8o463PaLjyNoohDi1QRUR1LZctQJY25QRI0P4f5SM1iOwgheK2qFuk038FCacvFgLBbuGaUoiLDTOV3IpldyKmtyLDKyWmFRHDmjILgMwzOANiQ29yL6XAsrSW0MruRTK7kVsilqHSGNsEpkBAyhhvrw0Un0VUyNkjqeYMewyNdkNi0GxP0ulFtTK7kUyu5FekcIrRk/hNOfKBlkadXWsDY6HpDQ6rXZR1L2OeynlLGs3hIYbBt7ZvpftSi2rldyKZXcipoioZXcimV3IqaIIZXcimV3Ir0JcLrImRuMOYPDSBG4PIzNzNuASRca6rFHhddWZTS0k0oLg0FrDa54apSW0MruRTK7kVvR4bVySOY2F128SSAOsG6E6HpEBa8kUkYaZGOaHC7SRxHMc0otTldyKZXcipoioZXcimV3IrdoaCet3ny+6O7bmdnlYyw59IhVmlqBa8EurgwdA6uPAfVKS2tldyKZXcityagqoXZZIHh2XMQBcgWDrkDhoQVS+GSNjHvje1jxdri0gO+iUWpyu5FMruRU0RUMruRTK7kVNEEMruRTK7kVvPw2qZFHJug4SZcoY9rndIXbdoNxcai4VcNJUTXMcLyAHG9rDojM7XwAvZKS2rldyKZXcituGiqZoDNHC90IJGe3RuBci/O3YoupKhrmtdTzBzzlaCw3J42HqPVKLa2V3IpldyKmQQSCLEdiIqGV3IpldyKmiAvWocX3DqF5pGTT0dmxOLja2fPqBxNyR58F5K9zDcRw6npqYSQ1EVWxxD56ewcWWdaxJ0cS4AnsDRZWGZbEW0BFFHTyxSvc2OVsrm9BxzE5QTr1cztf6rW0WJtq55p2yyQNzNfvAGvIFw97hfmOmQR22Cup9oKdtVJO+SpDnzxzPDIwN41jS3dm7ySCLakntvdcsdSVqZmOksxET4PaZtHViVrn5XRNDA2PgBlcx19OJO7AuUqceknpZYN0WskaQ4iQ8Tk4ch/DGnifC3ios7pa2wIiKKL2MbwyGjocPmpnPeJo8z3uNgXZjwBAtw8ffx1J8r5Gsa97nNYMrQT1Re9h5kqj1/8Q1T7Mluafcsh3bHZOqGgG41v0e3mR2q9m1NU2sdUGJtzIyTKHEAFrnO9DmP4V0WIYK+ja2SlG+ihLr7lrc0gADRoSSCc178fwI/tXCXU8g+SayfeEtf8uxw3XYwi41/q4i3Fb592OXZim2pqIQwCnY938Iuu46mPJltyvkF+d/ovPxbF5MSiYx8bWWdnJBvrla3Qdgs0af8Apb3KbHMFp6uOaGjdG6ORrwRTsJDBe7Otx4dLjouQUmZ7rER2ERFhpfSVLqbfZWg72J0Rv2A9q9tu1M2Sdr6djjMXlxzG4DnEmx4g62GttAbXWlglXQUsdSK6n3r35chLA8AC9xYkWvpqNRZelR4rgm/zVdC4sDyMrII9Y7jQ68bZteOvFbj1Zn0UHaWpE8UjIGt3To3WLiSQ0NABPbfKPVV47jEOI0FJDDE9skZMkrncMxaxugudOj4ceC9N2PYQ+Z7p6aWRsmUOtCxpLQYzlNndLqONzzA+mnPi2HfJVsdNAWSTxbvSnY3MbsOa97tHRdoNNb/Sz6pHo5xERc2xERB7tLtJVQMjYY43xRta1rSxulo3R9oNzZ19b+i2JdrJpY5mupIry57m97Zg8aXH9Z8NOHG9tHjGDx0LYJ6eQ2DHBop4yA4NAcSSelc5jrz7OxLi+Cyse2SmlLWwmGIbiPo6Psb3ve5Yb3voeK6XPdio7POotoKimH8qJxzOcAxojb0m5T0WgDsWzBtXVRF14Y3h1wc3StfecL3H/mnsPALnUWd0tbYX19U6trZ6l7Q10ry8hvAXVCIsqIiIM3j7z/tHul4+8/7R7qpFLWlt4+8/7R7pePvP+0e6qRLKW3j7z/tHul4+8/7R7qpEspbePvP+0e6Xj7z/ALR7qpEspbePvP8AtHul4+8/7R7qpEspbePvP+0e6Xj7z/tHuqkSylt4+8/7R7pePvP+0e6qRLKW3j7z/tHul4+8/wC0e6qRLKW3j7z/ALR7pePvP+0e6qRLKW3j7z/tHul4+8/7R7qpEspbePvP+0e6Xj7z/tHuqkSylt4+8/7R7pePvP8AtHuqkSylt4+8/wC0e6Xj7z/tHuqkSylt4+8/7R7pePvP+0e6qRLKW3j7z/tHul4+8/7R7qpEspbePvP+0e6Xj7z/ALR7qpEsoVtJuPmovm94afMN5urZsvba+l1UiRNTZMXFOzrMH2ebh9BNTftQy4gHNpw97LBwNhm04XI4di81+yWIR1DoZJaNhYHOkcZxaMC2ru0XvpzWJMapjRbPRBkufD3udLoLEF4PR110HbZbUeN4ZJjmLVtRDJ/tOtPI6FkpiPMscbFfTy93zmLqOnTl4XPz5PlYe9aceM9evP71R8ubSbstiDsQ+VBgy7nf7/efwt33r8l6keysMcNBvM08s0M8j93M3IchGUtNuFj/AOy25trMMqGbmdlaY5aQ0srwxocLG7XAA21ubjs8VrU20eF0cVBDTsrHRU0E8RL2tuS+1jx+v08V1w0+Dwmed/8AseHpbGWpxmcRyqf2nx9aeZDsliM0ML2Ppc00ImijMoD5Ba9gOf41VVFszXVdPTyh9NEah1oY5ZQ17xexIHJenBtLRsxXAql0dRu6Gm3MoDRcuykdHXUa9tl62Dvpq6LBa2pLM9HdudtQxrWNB03jXag/S9+amlw3Das1jPP1/wCPPp5z8HTPX4jTi8o5enry6+UfFy+A4NHVbRPw7EHPa2PeB5hcL3aDwJHgrqjCsNrcMnq8Ckqy+ncxskNQGlxDjYEEeKzQYxS0e11ViEu8fTPfLYxgFxDr2NiRzWf2rhNFh8lHh1PVSsqJGGofUFoJY03ygArlhGhsmMq65evSNtfm75Trbri/D08/k1azZutpad8pdTyujLWyxQyBz4i7hmH10Vsmy9ZA4GZ8Dwx7GzxxSAviDiLXH/uvUn2iws0lfTQtqmwVAG7ZHBHEIiCCBcG5v3j6KNTtFhssTzK2qq5c0ZidPDGHxWNz02m7vMLrOjwcXWXz9f2/bwkanET1j+cv3/fx0sf2XkoZKySkkjlpadzczTKHStaQNXADRTxnZSanqqoUD2SRwsEgifIDK5thd1gOF7+iVe0FLNU49I2Oe1exjYrtGlu9r+l1ty7R4WMVqsUgirDVvh3TI3hoZewFyb+HBanDgspyqaiZ79IvLpy7VNNYzrRV/wA6fu8duzVe6lEo3IlMe9FOZBvSzvZVim2craimjlY6nEkrDJHA6QCR7R2gL1f8RYe6vixV8dV+0GQbvdAN3Zda173vbXhZZwjaLDcO+TfFDNGY48k0bIWfxHdrs5ObyWcdDg90Xly9fTn069eX+XTdq10eONnq51LTzjc2qADE3eAOeSbWAPatyXZsUuEYlU1M8b56YsDRBIHNBLrODtL3ClNj9MypwaSmjmc2gzB28AGYE9liexKnFsMZhmLU9G2sdJWva8GVrQG2de2h+qzGnwmMZc75T4+O3w/Pl+Td5yow/D8MGAjEMSdWXM5hDYC3le+o+qVezshrYmYfIJaWWEVDZZOgGM7c3KyUFfhpwEYfiIqwROZg6BrT2WtqVvQ7T07Kt7GQSxUPywp48uVz2gf5iDoforhjwuWGMakxHKOnW/G/JvnbzG7OVz6jdRmF4MRmZI2QFj2jjY+atGy1e57WskpX52Z4i2UHe8w3nZbw2kp2Pcwvq5ovlpImlzI22e63BrbWGnMrWw7HaemmwV72TEUTJGyWA1zXtbXxVjS4G4iZn4+cR595n8moaVFgFZVwRytMMe9JETJJA10pHHKF5cjHRSOZI0te0kEHsK6WLGqCUYfJWNqWzULiWCMAteL3F7nTsXP19QautnqHNDTK8vsOy5Xk4jT0McInSm5/b5c+SzShEReNBERAREQEREBZa1z3BrAXOcbAAXJKwtzBqhlJjFDUTEiKGeOR1hc2DgT+iDXngmp35KiKSJ9r5XtLTbzUGNc91mNLjxsBddhhmPYWWtFbTxtczeBge0yMAL2G/TDyCQHC3h2XJVlPi2BR0LWxtbFKGvyHddJuZkjSCQ29uk3i53DstZb2x3Y3T2cW1pcQGgkk2AAvcrBBBsdCuiwfFaKnjnj3TaeOSSB7muG9zBjiXWJFwbH8cVttxrDGtjj+XgMeeEOJpWE5LO3utr3PR146aKVHdbns5JF0mOV+FVVHQRUNPHG9jmZiGBpa3KA4ONhe7tdS7ncXsvQqcTwplZKGSUJlErskzaFpjEWcWZly2LrA9K3ba/am3zN09nFgE8FN8MrJt0+N7ZbgZC0h1z4LqI8do4m1MVIyGCExR7sOpmuJc14cQTYkm3aVsux7DZK35p4i/wDiBKQKZokLxUB28zAcN3pa/Hs7U2x3N09nGOa5hAc0tJAIuLaHtWF1n7TwgUQjyxFzYN3Lmpw4ynchrcjiLts8E3049vBa2GYnh1LhsDZ4YJiwfxIHQDO9+9vm3luGTo2vx7Nbptjubp7OcRoLnBrQS4mwA7V10mJ4EzDqulp6djrNcyN8kIzydABrr5SQc+Y8RoRx4KDMXwh1VVTzUzN42pPywZA1rdyXN1IA4ta027bvv2Jtjubp7OUOhseKLsf2pgElLSQS07AwgMlc2EZ2dBwc6+UX6RaRqeHZ26OJYlhVTieHyU9Kynpt6J6hrYh0SS0OYB2tAbcf8R0TbHc3T2c4s5XZc1jlva9tLrrq7F8IMn+zwwEStDZj8u0/5Hg26ItqWG4A4X0Wlj2JUk9BDDROiIZOZGRspxHu2ZQA1xt0iCDqb3562ScY7kZT2c6ssY55sxrnHkBddc/GsPmxWWaXcOZLUzTF/wAsy9ixojBJYbWOa9gdddeKvfjODRzVDqQRRU8hdmj+VGZxLmkEOt0W2BGUEDTgb3DbHc3T2cSQQSCLEcQVlrXOvlaTYXNhwC7GjxPCJ6mGB8NOGdHI98LBlk6d3OJtcC7DZxsbLUxmeggxucNdBMz5NsRfBG0MdLlFzZvR434Jt8bN3hTmFlzXNNnNLTYGxFtDwXa02NYI+WU1MMDAZJGgtpGC0We7QOiRe1+y57zVllRhU+D1MzH0se7p925kkcZkkk3LGtLbnOLOB6oI7eau2O6bp7OJc1zXOa5pDmmxBFiFmNj5HtZG1z3uNg1ouSuwk2iopn4oXiFrql0jWu+VbZ0e+Y9oIA1OUP143I14Ea1ViGHSbQRzUj2RRvppYi9sYY1r3Ne1p0a3TpN1tpzNrqbY7runs5uWnnhYx80MkbHi7XOaQHDw5qpdfTYxhkT71cj6uCYwXp8hyxFjQC430JuLWANwTfklRjeFtneYKel6TH5j8qxwL90Awi7Rbp68BfiQm2O5uns5BF1eIYphE1FO2nihYXxHNGKYBzpiG2e11ui0G+gI+huuUUmKWJsREUUWzhdMK3E6Slc4sE8zIi4C9szgL/laynTzSU88c0LiyWNwexw7CDcFB71Ns7+0mxuwuV5GaRkjZmElpZl1GQEkWeOzTW/NWN2Qq5J4qeKohdUljnyx5JDuy2Ux8WtNxcceH4J839vYjvM+/b1SzJumZLXDurbLe4Bva9wFMbRYpnLzUtc4kuJdEwm5dmvqO9cjkSbcVu8WKya2KUQozTWLv4sWdzXcWuDnMcPVh8lpLaxGumxCZstQW5mtDQGtDR2k6eJJP1JWqsT5NwIiICIiAiIgIiICIiAiIgIiICIiAiIgIiICIiAiIgIiICIstBc4AcSgwi2KSlmrKhlPQ08tTO6+VkbC5zvoBqvRwnZvF8WrKukoqAmppGbyeOV4iMbbhuucjW7gLcdUuOiXF08ZFuzYZXwTSRTYfURyRymF7XROBbIOLT4jksHD60SCM0FQJHZrN3TrnL1tPDt5IrTQAngpWDhoLOGvgVgfyz9R/wBUDIebfuCZDzb9wW3hWG1GJ1DoqfI1sbDLLLI7LHEwcXOd2DUDxJAFyQF6OJ7L11DgEOOZoajCJ5vl4qmIuDXyWcSAHNDtMp1IA5X1sHhlpH/obrCy3g76f9VhAREQEREBERAREQEREBERAREQEREBERAREQFlhAcL8O1YRB0GxeP1OzGNCvpadtSCwxPYR1mmx0PYdAvXh2xMuOY9XYpgxqoMXgbTyQRyuiyta9jm2dYn/wAsflcQizsx3bq5ucaOEanta/1VV+T7DD8asVbTOZJg0RkNS+e8bsoALy4Dql1xoM2bgOHatWm+LmItbTtq8Mnm3LGt3jat7JeiIOD8pIBdBdw7c5+q+UItcnTm3MSqX1mI1lZNG2J9RK+UxtFgC4k2A5C61B/LP1H/AFWFkHQg8EHQ7IYjhNE3E4Mdjq309XA2ICnJ0Odrszm5m5gLXy3FyAtfF5MIp8PbQ4Q6ere+QTTVlRC2I6AgMY0FxA6RJJd0jl0GW58bo8j6p0eR9UBvB30/6hYWbi1gOKwgIiICIiAiIgIiICIiAiIgIiICIiAiIgIiICAXNhxRSi/mBBMQ6avYDyN/+gTcf3Y/z7KSLVM2juP7sf59k3H92P8APspIhaO4/ux/n2Tcf3Y/z7KSIWjuP7sf59k3H92P8+ykiFo7j+7H+fZNx/dj/PspIhaO4/ux/n2Tcf3Y/wA+ykiFo7j+7H+fZNx/dj/PspIhaO4/ux/n2Tcf3Y/z7KSIWjuP7sf59k3H92P8+ykiFo7j+7H+fZNx/dj/AD7KSIWjuP7sf59k3H92P8+ykiFo7j+7H+fZNx/dj/PspIhaO4/ux/n2Tcf3Y/z7KSIWjuP7sf59lW5pabFXKE3BvmpMLEq0RFFFKLrjz/RRUouuPNIFqIvT2aNsfoDYG0oOq6YY7sox7uWpnswnLs0TBMBcwyAD+kqpdVg+OVFXXxxyGOJo6RL55Te3YA6SxPgVq0VLFDt5S0rWAwNxFjAx3SGXeAWPPRdNTSiMN+M3Dlp62WWc6ecVNW59F+lcYg2U2j2oxDZSvnirK0V0ktPFSYaykfTsia8mASgXkLrAcDwvxsuZrtiNmsN2WqtosRwOvp5W0MdScHkq3MfC4zmLVxbmyuFjqL6FcLemnxBF+mjSYBg+JYnXwbKQ00cez0VXDUHLun33YdlDoy3MMwu/U66jVa2BfDrBMO2tjDMKrKdmGV1NDDV1b95FiokYc1mlobpx6NxbillPzci/QuIbC4fUUWG1lbg9biddHSUVM/Dqb+A+ESSSh0rwxt9MtteRuVzezOAw4Ni/xBp9noYcYxzCgI8MZJC2dxZvcsr2xkEOe0WHDj9Usp8eRfoCr2Mptr8OwmixShjwzburoDUOayMU4ysnDbyRCwDnRkngD0Fmo+G+xs+GPqsObWSUVSKosrIzPKaYwksGjI3MIJbmOdzTZ2nBLKfn5F9xqNgNmsW2qxrZXBaKopMUoBTSxVElWXiaNxj3vRIFsrX34nh5L2YNjNmMc2bpamhhdPQUbq51JStLy6oaKhrA5z4o3yuaB0tAeI4BLKfnVF+hH/CzZGaoLoJayGlhkfRz74vZIypm3Xy4yua02BldqQLhtz2r5N8S8GodntpRhFA1wfSU0LKpxfmzVBYHSEchc2t4JZTlERFUFJrS7gNOZNlFTfcuA8BZA3Z5s+8e6bs82fePdddhuG7P4ZIKnFa+lxJtMxwqqFhe0ySEHI2GRpGYdjn8G6kB4y5tShw3Aq92PzvxQUMVOHPoIZGkulG8AaDxucp4ce3gCllOc3Z5s+8e6bs82fePdfW6jANlP8RYDFuMK+UZvDiHyuLRvjfdv8NgLpg5zg7i4ZAb6CwutiXZ3Y2M1bnDDJIyZXVGWsdG+iaKZro2sbvX5nGUuadZBcWFlLgp8c3Z/p+4e6iRY2IsV9C27wrZl2Aw4nsw+hpnOc0y0bq0yTsu0WaG3IsM1ybnXTSxB+fG5YL87KiKhL1W+amoS9VvmkrCtERZUUouuPP9FFSi648/0SBarIJZKedk0Ly2Rjg5rh2EKvyRaiaZmL5S9QY7XDg6m/8Axov/ANV58s8stQ6eSQmZzs5fwOa97qvyTyW8tTLLllNsY6WGHPGIhZvpd/vt6/fXzZ8xzX535q2orquofI+oqp5XyAB5fIXF4HC9zqtbyRYbXmsqjEIzUzGMNyBuc2DeVuWg08FJ9fWPjgjfWVDo4NYmmRxEf/CL6eS1vJPJBuR4nXxzGWOuqmSluQvbK4OLeV78NSqKepnppxPTzyxTA3Ekby1w8wqvJPJBsOrat9Ual1VO6pPGUyEvPne6R1tXFTSU8VVOynkN3xNkIa76i9itfyTyQXCqqBOZhUSiYixkDzmPZxUqeuq6Z0bqaqnhdHfIY5C0svxtY6XWv5J5ILzV1JLyaiYl7g9xznpOHafHxVUj3yyOfK9z3uNy5xuSfqo+SeSB5p5p5J5ICkHaWcLqPknkgl0eR9U6PI+qj5J5IJdHkfVOjyPqo+SeSCV28j6rDjc8uQWPJPJAUJeq3zUlGXqt80WFaIiyopRdceaipRdcef6JAtW3hW7OI07Z4WTxueGuY8uAIJt/lIP5Woi0y7St2fonyVEzI5KeCOWQXiuYmtZLkyOc4k53DXjbUaarci2OpX4jN/DrGQR1JDWyjouja9rSLjXUEm5I058R8/vpa+iX0tcre6OzG2e7p8GwmjrquvpJ2mGSlnErnlxGWna4iQWPaAWnnoVvt2YppKJ9ayKcZo3ubA4EjpQvkYWkcbWA4m/bbguJWSb2uTpwUjKOyzE93bR7GQh87pqioEbHyCNojJdMwbuxbYEnrk3AOjfMcjiNO2kr6injlMrIpHMDywszAG18p1H0Wvc3vc3WFJmJ6QsRMdZERFFEREBERAREQEREBERBbTNjdUxCdxbCXgPI4ht9fwvWxWi3NHPJLSMpHRztiisXDfNIcS7pE3As3UadIcwvEUnOc4AOc4hosLngFmYuWMsZmYmJdTDhGH1dJR9JtM4taXvLnF0h3RcQ22YO1FtAC0mxDiVVXbPUsD3x09Y6ocA8tlYWZHkB9mAXvm6Avx6w4aX5lFnZPdz9lnE3GTrP8OYaZqiIYmA6IuF7ggjePYDoDoAwE8B0hqOK87HsPpKCkphTPL5TLK17y4HO1oZlcAODTdxH/VeIsKxjMTzlcdPOMrnK2QoTcG+amoTcG+a3LvCtERZUUouuPP8ARRUouuPP9EgWrdwXDajGMXo8Nomh1TVzNhjBNhmcbC/gtJevsi2Z+*********************************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"}}, "total-blocking-time": {"id": "total-blocking-time", "title": "Total Blocking Time", "description": "Sum of all time periods between FCP and Time to Interactive, when task length exceeded 50ms, expressed in milliseconds. [Learn more about the Total Blocking Time metric](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-total-blocking-time/).", "score": 0.34, "scoreDisplayMode": "numeric", "numericValue": 837, "numericUnit": "millisecond", "displayValue": "840 ms", "scoringOptions": {"p10": 200, "median": 600}}, "max-potential-fid": {"id": "max-potential-fid", "title": "Max Potential First Input Delay", "description": "The maximum potential First Input Delay that your users could experience is the duration of the longest task. [Learn more about the Maximum Potential First Input Delay metric](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-max-potential-fid/).", "score": 0.21, "scoreDisplayMode": "numeric", "numericValue": 371, "numericUnit": "millisecond", "displayValue": "370 ms"}, "cumulative-layout-shift": {"id": "cumulative-layout-shift", "title": "Cumulative Layout Shift", "description": "Cumulative Layout Shift measures the movement of visible elements within the viewport. [Learn more about the Cumulative Layout Shift metric](https://web.dev/articles/cls).", "score": 1, "scoreDisplayMode": "numeric", "numericValue": 0, "numericUnit": "unitless", "displayValue": "0", "scoringOptions": {"p10": 0.1, "median": 0.25}, "details": {"type": "debugdata", "items": [{"cumulativeLayoutShiftMainFrame": 0, "newEngineResult": {"cumulativeLayoutShift": 0, "cumulativeLayoutShiftMainFrame": 0}, "newEngineResultDiffered": false}]}}, "server-response-time": {"id": "server-response-time", "title": "Initial server response time was short", "description": "Keep the server response time for the main document short because all other requests depend on it. [Learn more about the Time to First Byte metric](https://developer.chrome.com/docs/lighthouse/performance/time-to-first-byte/).", "score": 1, "scoreDisplayMode": "metricSavings", "numericValue": 187.697, "numericUnit": "millisecond", "displayValue": "Root document took 190 ms", "metricSavings": {"FCP": 100, "LCP": 100}, "details": {"type": "opportunity", "headings": [{"key": "url", "valueType": "url", "label": "URL"}, {"key": "responseTime", "valueType": "timespanMs", "label": "Time Spent"}], "items": [{"url": "http://localhost:9002/demo", "responseTime": 187.697}], "overallSavingsMs": 87.697}, "guidanceLevel": 1}, "interactive": {"id": "interactive", "title": "Time to Interactive", "description": "Time to Interactive is the amount of time it takes for the page to become fully interactive. [Learn more about the Time to Interactive metric](https://developer.chrome.com/docs/lighthouse/performance/interactive/).", "score": 0.55, "scoreDisplayMode": "numeric", "numericValue": 6813.************, "numericUnit": "millisecond", "displayValue": "6.8 s"}, "user-timings": {"id": "user-timings", "title": "User Timing marks and measures", "description": "Consider instrumenting your app with the User Timing API to measure your app's real-world performance during key user experiences. [Learn more about User Timing marks](https://developer.chrome.com/docs/lighthouse/performance/user-timings/).", "score": null, "scoreDisplayMode": "notApplicable", "details": {"type": "table", "headings": [], "items": []}, "guidanceLevel": 2}, "critical-request-chains": {"id": "critical-request-chains", "title": "Avoid chaining critical requests", "description": "The Critical Request Chains below show you what resources are loaded with a high priority. Consider reducing the length of chains, reducing the download size of resources, or deferring the download of unnecessary resources to improve page load. [Learn how to avoid chaining critical requests](https://developer.chrome.com/docs/lighthouse/performance/critical-request-chains/).", "score": null, "scoreDisplayMode": "notApplicable", "displayValue": "", "details": {"type": "criticalrequestchain", "chains": {"9FA7086E4070BA1265A8FD4FFA2A4FC6": {"request": {"url": "http://localhost:9002/demo", "startTime": 130362.182948, "endTime": 130362.392155, "responseReceivedTime": 130362.371257, "transferSize": 20233}}}, "longestChain": {"duration": 209.20700000226498, "length": 1, "transferSize": 20233}}, "guidanceLevel": 1}, "redirects": {"id": "redirects", "title": "Avoid multiple page redirects", "description": "Redirects introduce additional delays before the page can be loaded. [Learn how to avoid page redirects](https://developer.chrome.com/docs/lighthouse/performance/redirects/).", "score": 1, "scoreDisplayMode": "metricSavings", "numericValue": 0, "numericUnit": "millisecond", "displayValue": "", "metricSavings": {"LCP": 0, "FCP": 0}, "details": {"type": "opportunity", "headings": [], "items": [], "overallSavingsMs": 0}, "guidanceLevel": 2}, "mainthread-work-breakdown": {"id": "mainthread-work-breakdown", "title": "Minimize main-thread work", "description": "Consider reducing the time spent parsing, compiling and executing JS. You may find delivering smaller JS payloads helps with this. [Learn how to minimize main-thread work](https://developer.chrome.com/docs/lighthouse/performance/mainthread-work-breakdown/)", "score": 0, "scoreDisplayMode": "metricSavings", "numericValue": 3670.************, "numericUnit": "millisecond", "displayValue": "3.7 s", "metricSavings": {"TBT": 850}, "details": {"type": "table", "headings": [{"key": "groupLabel", "valueType": "text", "label": "Category"}, {"key": "duration", "valueType": "ms", "granularity": 1, "label": "Time Spent"}], "items": [{"group": "scriptEvaluation", "groupLabel": "Script Evaluation", "duration": 2762.************}, {"group": "scriptParseCompile", "groupLabel": "Script Parsing & Compilation", "duration": 388.5679999999999}, {"group": "other", "groupLabel": "Other", "duration": 226.21999999999966}, {"group": "styleLayout", "groupLabel": "Style & Layout", "duration": 200.756}, {"group": "paintCompositeRender", "groupLabel": "Rendering", "duration": 52.39599999999962}, {"group": "garbageCollection", "groupLabel": "Garbage Collection", "duration": 20.868}, {"group": "parseHTML", "groupLabel": "Parse HTML & CSS", "duration": 19.499999999999993}], "sortedBy": ["duration"]}, "guidanceLevel": 1}, "bootup-time": {"id": "bootup-time", "title": "Reduce JavaScript execution time", "description": "Consider reducing the time spent parsing, compiling, and executing JS. You may find delivering smaller JS payloads helps with this. [Learn how to reduce Javascript execution time](https://developer.chrome.com/docs/lighthouse/performance/bootup-time/).", "score": 0, "scoreDisplayMode": "metricSavings", "numericValue": 3054.************, "numericUnit": "millisecond", "displayValue": "3.1 s", "metricSavings": {"TBT": 900}, "details": {"type": "table", "headings": [{"key": "url", "valueType": "url", "label": "URL"}, {"key": "total", "granularity": 1, "valueType": "ms", "label": "Total CPU Time"}, {"key": "scripting", "granularity": 1, "valueType": "ms", "label": "Script Evaluation"}, {"key": "scriptParseCompile", "granularity": 1, "valueType": "ms", "label": "<PERSON><PERSON><PERSON> Parse"}], "items": [{"url": "http://localhost:9002/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "total": 2728.2439999999638, "scripting": 2549.7479999999637, "scriptParseCompile": 102.64800000000001}, {"url": "http://localhost:9002/demo", "total": 299.104, "scripting": 4.696, "scriptParseCompile": 6.675999999999997}, {"url": "http://localhost:9002/_next/static/chunks/node_modules_next_dist_client_43e3ffb8._.js", "total": 282.29200000000037, "scripting": 166.19200000000035, "scriptParseCompile": 100.488}, {"url": "Unattributable", "total": 131.40400000000017, "scripting": 6.803999999999999, "scriptParseCompile": 0}, {"url": "http://localhost:9002/_next/static/chunks/node_modules_4cec1fd0._.js", "total": 117.488, "scripting": 16.687999999999988, "scriptParseCompile": 100.352}], "summary": {"wastedMs": 3054.************}, "sortedBy": ["total"]}, "guidanceLevel": 1}, "uses-rel-preconnect": {"id": "uses-rel-preconnect", "title": "Preconnect to required origins", "description": "Consider adding `preconnect` or `dns-prefetch` resource hints to establish early connections to important third-party origins. [Learn how to preconnect to required origins](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/).", "score": 1, "scoreDisplayMode": "metricSavings", "warnings": ["A `<link rel=preconnect>` was found for \"https://fonts.googleapis.com\" but was not used by the browser. Only use `preconnect` for important origins that the page will certainly request.", "A `<link rel=preconnect>` was found for \"https://fonts.gstatic.com\" but was not used by the browser. Only use `preconnect` for important origins that the page will certainly request."], "metricSavings": {"LCP": 0, "FCP": 0}, "guidanceLevel": 3}, "font-display": {"id": "font-display", "title": "All text remains visible during webfont loads", "description": "Leverage the `font-display` CSS feature to ensure text is user-visible while webfonts are loading. [Learn more about `font-display`](https://developer.chrome.com/docs/lighthouse/performance/font-display/).", "score": 1, "scoreDisplayMode": "metricSavings", "warnings": [], "details": {"type": "table", "headings": [], "items": []}, "guidanceLevel": 3}, "diagnostics": {"id": "diagnostics", "title": "Diagnostics", "description": "Collection of useful page vitals.", "score": 1, "scoreDisplayMode": "informative", "details": {"type": "debugdata", "items": [{"numRequests": 32, "numScripts": 21, "numStylesheets": 1, "numFonts": 3, "numTasks": 915, "numTasksOver10ms": 21, "numTasksOver25ms": 7, "numTasksOver50ms": 2, "numTasksOver100ms": 1, "numTasksOver500ms": 0, "rtt": 0.141, "throughput": 83634466.0011628, "maxRtt": 0.141, "maxServerLatency": 6.110999999999999, "totalByteWeight": 775563, "totalTaskTime": 917.7349999999964, "mainDocumentTransferSize": 20233}]}}, "network-requests": {"id": "network-requests", "title": "Network Requests", "description": "Lists the network requests that were made during page load.", "score": 1, "scoreDisplayMode": "informative", "details": {"type": "table", "headings": [{"key": "url", "valueType": "url", "label": "URL"}, {"key": "protocol", "valueType": "text", "label": "Protocol"}, {"key": "networkRequestTime", "valueType": "ms", "granularity": 1, "label": "Network Request Time"}, {"key": "networkEndTime", "valueType": "ms", "granularity": 1, "label": "Network End Time"}, {"key": "transferSize", "valueType": "bytes", "displayUnit": "kb", "granularity": 1, "label": "Transfer Size"}, {"key": "resourceSize", "valueType": "bytes", "displayUnit": "kb", "granularity": 1, "label": "Resource Size"}, {"key": "statusCode", "valueType": "text", "label": "Status Code"}, {"key": "mimeType", "valueType": "text", "label": "MIME Type"}, {"key": "resourceType", "valueType": "text", "label": "Resource Type"}], "items": [{"url": "http://localhost:9002/demo", "sessionTargetType": "page", "protocol": "http/1.1", "rendererStartTime": 0, "networkRequestTime": 0.5979999899864197, "networkEndTime": 209.8049999922514, "finished": true, "transferSize": 20233, "resourceSize": 110714, "statusCode": 200, "mimeType": "text/html", "resourceType": "Document", "priority": "VeryHigh", "experimentalFromMainFrame": true, "entity": "localhost"}, {"url": "http://localhost:9002/_next/static/media/gyByhwUxId8gMEwcGFWNOITd-s.p.da1ebef7.woff2", "sessionTargetType": "page", "protocol": "http/1.1", "rendererStartTime": 191.7109999805689, "networkRequestTime": 192.1819999963045, "networkEndTime": 201.39000000059605, "finished": true, "transferSize": 28755, "resourceSize": 28356, "statusCode": 200, "mimeType": "font/woff2", "resourceType": "Font", "priority": "High", "isLinkPreload": true, "experimentalFromMainFrame": true, "entity": "localhost"}, {"url": "http://localhost:9002/_next/static/media/or3nQ6H_1_WfwkMZI_qYFrcdmhHkjko-s.p.be19f591.woff2", "sessionTargetType": "page", "protocol": "http/1.1", "rendererStartTime": 191.89299999177456, "networkRequestTime": 192.337999984622, "networkEndTime": 203.04799999296665, "finished": true, "transferSize": 31687, "resourceSize": 31288, "statusCode": 200, "mimeType": "font/woff2", "resourceType": "Font", "priority": "High", "isLinkPreload": true, "experimentalFromMainFrame": true, "entity": "localhost"}, {"url": "http://localhost:9002/_next/static/chunks/%5Broot%20of%20the%20server%5D__f83bf54e._.css", "sessionTargetType": "page", "protocol": "http/1.1", "rendererStartTime": 191.96399998664856, "networkRequestTime": 192.42700000107288, "networkEndTime": 209.55099998414516, "finished": true, "transferSize": 14800, "resourceSize": 90053, "statusCode": 200, "mimeType": "text/css", "resourceType": "Stylesheet", "priority": "VeryHigh", "isLinkPreload": true, "experimentalFromMainFrame": true, "entity": "localhost"}, {"url": "http://localhost:9002/_next/static/chunks/%5Bturbopack%5D_browser_dev_hmr-client_hmr-client_ts_49a6ea35._.js", "sessionTargetType": "page", "protocol": "http/1.1", "rendererStartTime": 194.97100000083447, "networkRequestTime": 213.01500000059605, "networkEndTime": 219.23899999260902, "finished": true, "transferSize": 1189, "resourceSize": 743, "statusCode": 200, "mimeType": "application/javascript", "resourceType": "<PERSON><PERSON><PERSON>", "priority": "Low", "isLinkPreload": true, "experimentalFromMainFrame": true, "entity": "localhost"}, {"url": "http://localhost:9002/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "sessionTargetType": "page", "protocol": "http/1.1", "rendererStartTime": 195.0409999936819, "networkRequestTime": 213.2119999974966, "networkEndTime": 249.86399999260902, "finished": true, "transferSize": 197173, "resourceSize": 1104803, "statusCode": 200, "mimeType": "application/javascript", "resourceType": "<PERSON><PERSON><PERSON>", "priority": "Low", "experimentalFromMainFrame": true, "entity": "localhost"}, {"url": "http://localhost:9002/_next/static/chunks/node_modules_next_dist_client_43e3ffb8._.js", "sessionTargetType": "page", "protocol": "http/1.1", "rendererStartTime": 195.08099998533726, "networkRequestTime": 213.32899998128414, "networkEndTime": 247.63799999654293, "finished": true, "transferSize": 164553, "resourceSize": 961764, "statusCode": 200, "mimeType": "application/javascript", "resourceType": "<PERSON><PERSON><PERSON>", "priority": "Low", "experimentalFromMainFrame": true, "entity": "localhost"}, {"url": "http://localhost:9002/_next/static/chunks/node_modules_next_dist_3bfaed20._.js", "sessionTargetType": "page", "protocol": "http/1.1", "rendererStartTime": 195.11800000071526, "networkRequestTime": 213.44399999082088, "networkEndTime": 224.95599998533726, "finished": true, "transferSize": 19176, "resourceSize": 99721, "statusCode": 200, "mimeType": "application/javascript", "resourceType": "<PERSON><PERSON><PERSON>", "priority": "Low", "experimentalFromMainFrame": true, "entity": "localhost"}, {"url": "http://localhost:9002/_next/static/chunks/node_modules_%40swc_helpers_cjs_00636ac3._.js", "sessionTargetType": "page", "protocol": "http/1.1", "rendererStartTime": 195.16099998354912, "networkRequestTime": 213.69799998402596, "networkEndTime": 221.29799999296665, "finished": true, "transferSize": 1468, "resourceSize": 3449, "statusCode": 200, "mimeType": "application/javascript", "resourceType": "<PERSON><PERSON><PERSON>", "priority": "Low", "experimentalFromMainFrame": true, "entity": "localhost"}, {"url": "http://localhost:9002/_next/static/chunks/_e69f0d32._.js", "sessionTargetType": "page", "protocol": "http/1.1", "rendererStartTime": 195.2019999921322, "networkRequestTime": 219.35599999129772, "networkEndTime": 225.3999999910593, "finished": true, "transferSize": 1059, "resourceSize": 613, "statusCode": 200, "mimeType": "application/javascript", "resourceType": "<PERSON><PERSON><PERSON>", "priority": "Low", "experimentalFromMainFrame": true, "entity": "localhost"}, {"url": "http://localhost:9002/_next/static/chunks/_be317ff2._.js", "sessionTargetType": "page", "protocol": "http/1.1", "rendererStartTime": 195.2429999858141, "networkRequestTime": 221.4929999858141, "networkEndTime": 231.86599999666214, "finished": true, "transferSize": 16403, "resourceSize": 66161, "statusCode": 200, "mimeType": "application/javascript", "resourceType": "<PERSON><PERSON><PERSON>", "priority": "Low", "experimentalFromMainFrame": true, "entity": "localhost"}, {"url": "http://localhost:9002/_next/static/chunks/node_modules_next_dist_1a6ee436._.js", "sessionTargetType": "page", "protocol": "http/1.1", "rendererStartTime": 195.28499998152256, "networkRequestTime": 225.54999999701977, "networkEndTime": 236.15499998629093, "finished": true, "transferSize": 18924, "resourceSize": 117533, "statusCode": 200, "mimeType": "application/javascript", "resourceType": "<PERSON><PERSON><PERSON>", "priority": "Low", "experimentalFromMainFrame": true, "entity": "localhost"}, {"url": "http://localhost:9002/_next/static/chunks/src_app_favicon_ico_mjs_79b6a596._.js", "sessionTargetType": "page", "protocol": "http/1.1", "rendererStartTime": 195.32199999690056, "networkRequestTime": 225.66499999165535, "networkEndTime": 234.68299999833107, "finished": true, "transferSize": 826, "resourceSize": 380, "statusCode": 200, "mimeType": "application/javascript", "resourceType": "<PERSON><PERSON><PERSON>", "priority": "Low", "experimentalFromMainFrame": true, "entity": "localhost"}, {"url": "http://localhost:9002/_next/static/chunks/[turbopack]_browser_dev_hmr-client_hmr-client_ts_154e108b._.js", "sessionTargetType": "page", "protocol": "http/1.1", "rendererStartTime": 195.3599999845028, "networkRequestTime": 232.09999999403954, "networkEndTime": 240.0949999988079, "finished": true, "transferSize": 1189, "resourceSize": 743, "statusCode": 200, "mimeType": "application/javascript", "resourceType": "<PERSON><PERSON><PERSON>", "priority": "Low", "experimentalFromMainFrame": true, "entity": "localhost"}, {"url": "http://localhost:9002/_next/static/chunks/src_7b7cfb18._.js", "sessionTargetType": "page", "protocol": "http/1.1", "rendererStartTime": 195.3979999870062, "networkRequestTime": 234.83299998939037, "networkEndTime": 243.74999998509884, "finished": true, "transferSize": 26131, "resourceSize": 304966, "statusCode": 200, "mimeType": "application/javascript", "resourceType": "<PERSON><PERSON><PERSON>", "priority": "Low", "experimentalFromMainFrame": true, "entity": "localhost"}, {"url": "http://localhost:9002/_next/static/chunks/node_modules_4cec1fd0._.js", "sessionTargetType": "page", "protocol": "http/1.1", "rendererStartTime": 195.43699999153614, "networkRequestTime": 236.47799998521805, "networkEndTime": 262.7809999883175, "finished": true, "transferSize": 120943, "resourceSize": 911639, "statusCode": 200, "mimeType": "application/javascript", "resourceType": "<PERSON><PERSON><PERSON>", "priority": "Low", "experimentalFromMainFrame": true, "entity": "localhost"}, {"url": "http://localhost:9002/_next/static/chunks/src_app_layout_tsx_f0e4c1a2._.js", "sessionTargetType": "page", "protocol": "http/1.1", "rendererStartTime": 195.47399999201298, "networkRequestTime": 240.3599999845028, "networkEndTime": 244.52599999308586, "finished": true, "transferSize": 987, "resourceSize": 541, "statusCode": 200, "mimeType": "application/javascript", "resourceType": "<PERSON><PERSON><PERSON>", "priority": "Low", "experimentalFromMainFrame": true, "entity": "localhost"}, {"url": "http://localhost:9002/_next/static/chunks/src_39ae3a7d._.js", "sessionTargetType": "page", "protocol": "http/1.1", "rendererStartTime": 195.516999989748, "networkRequestTime": 243.92599998414516, "networkEndTime": 252.75499999523163, "finished": true, "transferSize": 11315, "resourceSize": 183791, "statusCode": 200, "mimeType": "application/javascript", "resourceType": "<PERSON><PERSON><PERSON>", "priority": "Low", "experimentalFromMainFrame": true, "entity": "localhost"}, {"url": "http://localhost:9002/_next/static/chunks/node_modules_c955a5f0._.js", "sessionTargetType": "page", "protocol": "http/1.1", "rendererStartTime": 195.55399999022484, "networkRequestTime": 244.9159999936819, "networkEndTime": 251.79899998009205, "finished": true, "transferSize": 4902, "resourceSize": 33884, "statusCode": 200, "mimeType": "application/javascript", "resourceType": "<PERSON><PERSON><PERSON>", "priority": "Low", "experimentalFromMainFrame": true, "entity": "localhost"}, {"url": "http://localhost:9002/_next/static/chunks/src_app_demo_page_tsx_4a1ef595._.js", "sessionTargetType": "page", "protocol": "http/1.1", "rendererStartTime": 195.59399999678135, "networkRequestTime": 247.88999998569489, "networkEndTime": 252.23799999058247, "finished": true, "transferSize": 851, "resourceSize": 405, "statusCode": 200, "mimeType": "application/javascript", "resourceType": "<PERSON><PERSON><PERSON>", "priority": "Low", "experimentalFromMainFrame": true, "entity": "localhost"}, {"url": "http://localhost:9002/_next/static/chunks/%5Bturbopack%5D_browser_dev_hmr-client_hmr-client_ts_154e108b._.js", "sessionTargetType": "page", "protocol": "http/1.1", "rendererStartTime": 195.63199999928474, "networkRequestTime": 250.18999998271465, "networkEndTime": 256.2469999939203, "finished": true, "transferSize": 1189, "resourceSize": 743, "statusCode": 200, "mimeType": "application/javascript", "resourceType": "<PERSON><PERSON><PERSON>", "priority": "Low", "experimentalFromMainFrame": true, "entity": "localhost"}, {"url": "http://localhost:9002/favicon.ico", "sessionTargetType": "page", "protocol": "http/1.1", "rendererStartTime": 195.7109999805689, "networkRequestTime": 252.12699998915195, "networkEndTime": 1515.1359999924898, "finished": true, "transferSize": 2724, "resourceSize": 2724, "statusCode": 500, "mimeType": "text/html", "resourceType": "Image", "priority": "Low", "isLinkPreload": true, "experimentalFromMainFrame": true, "entity": "localhost"}, {"url": "http://localhost:9002/_next/static/chunks/vendor-7620e74be0c75f43.js", "sessionTargetType": "page", "protocol": "http/1.1", "rendererStartTime": 195.7529999911785, "networkRequestTime": 196.5469999909401, "networkEndTime": 1193.8499999940395, "finished": true, "transferSize": 549, "resourceSize": 0, "statusCode": 404, "mimeType": "text/html", "resourceType": "<PERSON><PERSON><PERSON>", "priority": "High", "isLinkPreload": true, "experimentalFromMainFrame": true, "entity": "localhost"}, {"url": "http://localhost:9002/_next/static/chunks/pages/_app.js", "sessionTargetType": "page", "protocol": "http/1.1", "rendererStartTime": 195.80899998545647, "networkRequestTime": 197.37399998307228, "networkEndTime": 204.52599999308586, "finished": true, "transferSize": 922, "resourceSize": 476, "statusCode": 200, "mimeType": "application/javascript", "resourceType": "<PERSON><PERSON><PERSON>", "priority": "High", "isLinkPreload": true, "experimentalFromMainFrame": true, "entity": "localhost"}, {"url": "http://localhost:9002/favicon.ico?favicon.56766c03.ico", "sessionTargetType": "page", "protocol": "http/1.1", "rendererStartTime": 437.8269999921322, "networkRequestTime": 438.14099998772144, "networkEndTime": 1511.6649999916553, "finished": true, "transferSize": 1317, "resourceSize": 2749, "statusCode": 500, "mimeType": "text/html", "resourceType": "Other", "priority": "High", "experimentalFromMainFrame": true, "entity": "localhost"}, {"url": "http://localhost:9002/api/binance/test-connection", "sessionTargetType": "page", "protocol": "http/1.1", "rendererStartTime": 614.1039999872446, "networkRequestTime": 615.1529999822378, "networkEndTime": 717.9409999847412, "finished": true, "transferSize": 614, "resourceSize": 179, "statusCode": 200, "mimeType": "application/json", "resourceType": "<PERSON>tch", "priority": "High", "experimentalFromMainFrame": true, "entity": "localhost"}, {"url": "http://localhost:9002/_next/static/chunks/%5Bturbopack%5D_browser_dev_hmr-client_hmr-client_ts_61dcf9ba._.js", "sessionTargetType": "page", "protocol": "http/1.1", "rendererStartTime": 626.652999997139, "networkRequestTime": 627.363999992609, "networkEndTime": 638.0740000009537, "finished": true, "transferSize": 4311, "resourceSize": 15499, "statusCode": 200, "mimeType": "application/javascript", "resourceType": "<PERSON><PERSON><PERSON>", "priority": "Low", "experimentalFromMainFrame": true, "entity": "localhost"}, {"url": "http://localhost:9002/_next/static/chunks/%5Bturbopack%5D_browser_dev_hmr-client_hmr-client_ts_5160d576._.js", "sessionTargetType": "page", "protocol": "http/1.1", "rendererStartTime": 627.4109999835491, "networkRequestTime": 628.1009999960661, "networkEndTime": 637.2609999924898, "finished": true, "transferSize": 902, "resourceSize": 456, "statusCode": 200, "mimeType": "application/javascript", "resourceType": "<PERSON><PERSON><PERSON>", "priority": "Low", "experimentalFromMainFrame": true, "entity": "localhost"}, {"url": "http://localhost:9002/__nextjs_original-stack-frames", "sessionTargetType": "page", "protocol": "http/1.1", "rendererStartTime": 776.0039999932051, "networkRequestTime": 776.8949999958277, "networkEndTime": 833.7749999910593, "finished": true, "transferSize": 1050, "resourceSize": 4466, "statusCode": 200, "mimeType": "application/json", "resourceType": "<PERSON>tch", "priority": "High", "experimentalFromMainFrame": true, "entity": "localhost"}, {"url": "http://localhost:9002/__nextjs_original-stack-frames", "sessionTargetType": "page", "protocol": "http/1.1", "rendererStartTime": 780.6149999946356, "networkRequestTime": 781.6989999860525, "networkEndTime": 835.3999999910593, "finished": true, "transferSize": 1050, "resourceSize": 4466, "statusCode": 200, "mimeType": "application/json", "resourceType": "<PERSON>tch", "priority": "High", "experimentalFromMainFrame": true, "entity": "localhost"}, {"url": "http://localhost:9002/placeholder-avatar.jpg", "sessionTargetType": "page", "protocol": "http/1.1", "rendererStartTime": 784.6189999878407, "networkRequestTime": 785.1399999856949, "networkEndTime": 1175.2249999940395, "finished": true, "transferSize": 49793, "resourceSize": 49793, "statusCode": 404, "mimeType": "text/html", "resourceType": "Image", "priority": "Low", "experimentalFromMainFrame": true, "entity": "localhost"}, {"url": "http://localhost:9002/__nextjs_font/geist-latin.woff2", "sessionTargetType": "page", "protocol": "http/1.1", "rendererStartTime": 835.880999982357, "networkRequestTime": 839.2359999865294, "networkEndTime": 844.2479999810457, "finished": true, "transferSize": 28578, "resourceSize": 28356, "statusCode": 200, "mimeType": "font/woff2", "resourceType": "Font", "priority": "VeryHigh", "experimentalFromMainFrame": true, "entity": "localhost"}], "debugData": {"type": "debugdata", "networkStartTimeTs": 130362182350.00002}}}, "network-rtt": {"id": "network-rtt", "title": "Network Round Trip Times", "description": "Network round trip times (RTT) have a large impact on performance. If the RTT to an origin is high, it's an indication that servers closer to the user could improve performance. [Learn more about the Round Trip Time](https://hpbn.co/primer-on-latency-and-bandwidth/).", "score": 1, "scoreDisplayMode": "informative", "numericValue": 0.141, "numericUnit": "millisecond", "displayValue": "0 ms", "details": {"type": "table", "headings": [{"key": "origin", "valueType": "text", "label": "URL"}, {"key": "rtt", "valueType": "ms", "granularity": 1, "label": "Time Spent"}], "items": [{"origin": "http://localhost:9002", "rtt": 0.141}], "sortedBy": ["rtt"]}}, "network-server-latency": {"id": "network-server-latency", "title": "Server Backend Latencies", "description": "Server latencies can impact web performance. If the server latency of an origin is high, it's an indication the server is overloaded or has poor backend performance. [Learn more about server response time](https://hpbn.co/primer-on-web-performance/#analyzing-the-resource-waterfall).", "score": 1, "scoreDisplayMode": "informative", "numericValue": 6.110999999999999, "numericUnit": "millisecond", "displayValue": "10 ms", "details": {"type": "table", "headings": [{"key": "origin", "valueType": "text", "label": "URL"}, {"key": "serverResponseTime", "valueType": "ms", "granularity": 1, "label": "Time Spent"}], "items": [{"origin": "http://localhost:9002", "serverResponseTime": 6.110999999999999}], "sortedBy": ["serverResponseTime"]}}, "main-thread-tasks": {"id": "main-thread-tasks", "title": "Tasks", "description": "Lists the toplevel main thread tasks that executed during page load.", "score": 1, "scoreDisplayMode": "informative", "details": {"type": "table", "headings": [{"key": "startTime", "valueType": "ms", "granularity": 1, "label": "Start Time"}, {"key": "duration", "valueType": "ms", "granularity": 1, "label": "End Time"}], "items": [{"duration": 5.848, "startTime": 191.38}, {"duration": 6.895, "startTime": 218.488}, {"duration": 30.613, "startTime": 225.864}, {"duration": 7.798, "startTime": 273.066}, {"duration": 26.03, "startTime": 286.084}, {"duration": 63.123, "startTime": 312.196}, {"duration": 29.309, "startTime": 380.821}, {"duration": 9.159, "startTime": 410.156}, {"duration": 5.174, "startTime": 419.938}, {"duration": 8.255, "startTime": 425.151}, {"duration": 5.111, "startTime": 433.54}, {"duration": 5.095, "startTime": 438.956}, {"duration": 5.137, "startTime": 444.069}, {"duration": 5.138, "startTime": 449.21}, {"duration": 5.051, "startTime": 454.351}, {"duration": 5.008, "startTime": 459.408}, {"duration": 5.163, "startTime": 464.425}, {"duration": 5.259, "startTime": 474.568}, {"duration": 9.483, "startTime": 479.833}, {"duration": 5.16, "startTime": 489.324}, {"duration": 5.319, "startTime": 494.49}, {"duration": 5.057, "startTime": 499.815}, {"duration": 5.229, "startTime": 504.878}, {"duration": 5.64, "startTime": 510.113}, {"duration": 5.205, "startTime": 515.766}, {"duration": 25.583, "startTime": 521.008}, {"duration": 12.989, "startTime": 547.059}, {"duration": 5.195, "startTime": 560.389}, {"duration": 5.059, "startTime": 565.883}, {"duration": 5.477, "startTime": 570.959}, {"duration": 5.186, "startTime": 576.451}, {"duration": 5.064, "startTime": 581.642}, {"duration": 5.236, "startTime": 591.664}, {"duration": 5.239, "startTime": 596.908}, {"duration": 185.333, "startTime": 602.152}, {"duration": 42.036, "startTime": 792.265}, {"duration": 12.738, "startTime": 844.621}, {"duration": 6.442, "startTime": 859.706}, {"duration": 19.356, "startTime": 866.155}, {"duration": 7.744, "startTime": 887.977}, {"duration": 11.914, "startTime": 945.508}, {"duration": 10.577, "startTime": 1076.18}, {"duration": 11.555, "startTime": 1164.131}, {"duration": 14.139, "startTime": 1176.597}, {"duration": 13.149, "startTime": 1194.976}, {"duration": 5.251, "startTime": 1208.879}, {"duration": 8.541, "startTime": 1254.789}, {"duration": 11.553, "startTime": 1504.345}, {"duration": 12.293, "startTime": 1516.218}, {"duration": 11.29, "startTime": 1529.896}, {"duration": 8.203, "startTime": 1541.71}, {"duration": 17.416, "startTime": 1628.192}, {"duration": 16.896, "startTime": 2627.125}, {"duration": 16.937, "startTime": 3624.542}]}}, "metrics": {"id": "metrics", "title": "Metrics", "description": "Collects all available metrics.", "score": 1, "scoreDisplayMode": "informative", "numericValue": 6814, "numericUnit": "millisecond", "details": {"type": "debugdata", "items": [{"firstContentfulPaint": 1062, "largestContentfulPaint": 5585, "interactive": 6814, "speedIndex": 2549, "totalBlockingTime": 837, "maxPotentialFID": 371, "cumulativeLayoutShift": 0, "cumulativeLayoutShiftMainFrame": 0, "timeToFirstByte": 456, "observedTimeOrigin": 0, "observedTimeOriginTs": 130362181416, "observedNavigationStart": 0, "observedNavigationStartTs": 130362181416, "observedFirstPaint": 530, "observedFirstPaintTs": 130362711562, "observedFirstContentfulPaint": 530, "observedFirstContentfulPaintTs": 130362711562, "observedFirstContentfulPaintAllFrames": 530, "observedFirstContentfulPaintAllFramesTs": 130362711562, "observedLargestContentfulPaint": 530, "observedLargestContentfulPaintTs": 130362711562, "observedLargestContentfulPaintAllFrames": 530, "observedLargestContentfulPaintAllFramesTs": 130362711562, "observedTraceEnd": 3874, "observedTraceEndTs": 130366055375, "observedLoad": 419, "observedLoadTs": 130362600893, "observedDomContentLoaded": 219, "observedDomContentLoadedTs": 130362400159, "observedCumulativeLayoutShift": 0, "observedCumulativeLayoutShiftMainFrame": 0, "observedFirstVisualChange": 531, "observedFirstVisualChangeTs": 130362712416, "observedLastVisualChange": 3643, "observedLastVisualChangeTs": 130365824416, "observedSpeedIndex": 640, "observedSpeedIndexTs": 130362821681}, {"lcpInvalidated": false}]}}, "resource-summary": {"id": "resource-summary", "title": "Resources Summary", "description": "Aggregates all network requests and groups them by type", "score": 1, "scoreDisplayMode": "informative", "details": {"type": "table", "headings": [{"key": "label", "valueType": "text", "label": "Resource Type"}, {"key": "requestCount", "valueType": "numeric", "label": "Requests"}, {"key": "transferSize", "valueType": "bytes", "label": "Transfer Size"}], "items": [{"resourceType": "total", "label": "Total", "requestCount": 32, "transferSize": 775563}, {"resourceType": "script", "label": "<PERSON><PERSON><PERSON>", "requestCount": 21, "transferSize": 594962}, {"resourceType": "font", "label": "Font", "requestCount": 3, "transferSize": 89020}, {"resourceType": "image", "label": "Image", "requestCount": 2, "transferSize": 52517}, {"resourceType": "document", "label": "Document", "requestCount": 1, "transferSize": 20233}, {"resourceType": "stylesheet", "label": "Stylesheet", "requestCount": 1, "transferSize": 14800}, {"resourceType": "other", "label": "Other", "requestCount": 4, "transferSize": 4031}, {"resourceType": "media", "label": "Media", "requestCount": 0, "transferSize": 0}, {"resourceType": "third-party", "label": "Third-party", "requestCount": 0, "transferSize": 0}]}}, "third-party-summary": {"id": "third-party-summary", "title": "Minimize third-party usage", "description": "Third-party code can significantly impact load performance. Limit the number of redundant third-party providers and try to load third-party code after your page has primarily finished loading. [Learn how to minimize third-party impact](https://developers.google.com/web/fundamentals/performance/optimizing-content-efficiency/loading-third-party-javascript/).", "score": null, "scoreDisplayMode": "notApplicable", "metricSavings": {"TBT": 0}, "guidanceLevel": 1}, "third-party-facades": {"id": "third-party-facades", "title": "Lazy load third-party resources with facades", "description": "Some third-party embeds can be lazy loaded. Consider replacing them with a facade until they are required. [Learn how to defer third-parties with a facade](https://developer.chrome.com/docs/lighthouse/performance/third-party-facades/).", "score": null, "scoreDisplayMode": "notApplicable", "metricSavings": {"TBT": 0}, "guidanceLevel": 3}, "largest-contentful-paint-element": {"id": "largest-contentful-paint-element", "title": "Largest Contentful Paint element", "description": "This is the largest contentful element painted within the viewport. [Learn more about the Largest Contentful Paint element](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)", "score": 0, "scoreDisplayMode": "metricSavings", "displayValue": "5,590 ms", "metricSavings": {"LCP": 3100}, "details": {"type": "list", "items": [{"type": "table", "headings": [{"key": "node", "valueType": "node", "label": "Element"}], "items": [{"node": {"type": "node", "lhId": "page-0-P", "path": "1,HTML,1,BODY,1,DIV,0,DIV,0,DIV,1,MAIN,0,DIV,0,DIV,0,DIV,1,P", "selector": "div.space-y-4 > div.flex > div.space-y-1 > p.text-muted-foreground", "boundingRect": {"top": 166, "bottom": 206, "left": 24, "right": 388, "width": 364, "height": 40}, "snippet": "<p class=\"text-muted-foreground text-sm sm:text-base\">", "nodeLabel": "Real-time portfolio overview and trading insights (Demo Data)"}}]}, {"type": "table", "headings": [{"key": "phase", "valueType": "text", "label": "Phase"}, {"key": "percent", "valueType": "text", "label": "% of LCP"}, {"key": "timing", "valueType": "ms", "label": "Timing"}], "items": [{"phase": "TTFB", "timing": 456.111, "percent": "8%"}, {"phase": "<PERSON>ad <PERSON>", "timing": 0, "percent": "0%"}, {"phase": "Load Time", "timing": 0, "percent": "0%"}, {"phase": "Render Delay", "timing": 5129.111, "percent": "92%"}]}]}, "guidanceLevel": 1}, "lcp-lazy-loaded": {"id": "lcp-lazy-loaded", "title": "Largest Contentful Paint image was not lazily loaded", "description": "Above-the-fold images that are lazily loaded render later in the page lifecycle, which can delay the largest contentful paint. [Learn more about optimal lazy loading](https://web.dev/articles/lcp-lazy-loading).", "score": null, "scoreDisplayMode": "notApplicable", "metricSavings": {"LCP": 0}, "guidanceLevel": 3}, "layout-shifts": {"id": "layout-shifts", "title": "Avoid large layout shifts", "description": "These are the largest layout shifts observed on the page. Each table item represents a single layout shift, and shows the element that shifted the most. Below each item are possible root causes that led to the layout shift. Some of these layout shifts may not be included in the CLS metric value due to [windowing](https://web.dev/articles/cls#what_is_cls). [Learn how to improve CLS](https://web.dev/articles/optimize-cls)", "score": null, "scoreDisplayMode": "notApplicable", "metricSavings": {"CLS": 0}, "details": {"type": "table", "headings": [], "items": []}, "guidanceLevel": 2}, "long-tasks": {"id": "long-tasks", "title": "Avoid long main-thread tasks", "description": "Lists the longest tasks on the main thread, useful for identifying worst contributors to input delay. [Learn how to avoid long main-thread tasks](https://web.dev/articles/optimize-long-tasks)", "score": 1, "scoreDisplayMode": "informative", "displayValue": "15 long tasks found", "metricSavings": {"TBT": 850}, "details": {"type": "table", "headings": [{"key": "url", "valueType": "url", "label": "URL"}, {"key": "startTime", "valueType": "ms", "granularity": 1, "label": "Start Time"}, {"key": "duration", "valueType": "ms", "granularity": 1, "label": "Duration"}], "items": [{"url": "http://localhost:9002/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "duration": 371, "startTime": 5895.333}, {"url": "http://localhost:9002/_next/static/chunks/node_modules_next_dist_client_43e3ffb8._.js", "duration": 252, "startTime": 5372.333}, {"url": "http://localhost:9002/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "duration": 168, "startTime": 6266.333}, {"url": "http://localhost:9002/_next/static/chunks/node_modules_4cec1fd0._.js", "duration": 117, "startTime": 5624.333}, {"url": "http://localhost:9002/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "duration": 104, "startTime": 5268.333}, {"url": "http://localhost:9002/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "duration": 102, "startTime": 5741.333}, {"url": "http://localhost:9002/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "duration": 77, "startTime": 6485.333}, {"url": "http://localhost:9002/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "duration": 70, "startTime": 6960.333}, {"url": "http://localhost:9002/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "duration": 68, "startTime": 7030.333}, {"url": "http://localhost:9002/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "duration": 68, "startTime": 7098.333}, {"url": "http://localhost:9002/demo", "duration": 61, "startTime": 759.111}, {"url": "http://localhost:9002/_next/static/chunks/node_modules_next_dist_client_43e3ffb8._.js", "duration": 57, "startTime": 6698.333}, {"url": "http://localhost:9002/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "duration": 53, "startTime": 6755.333}, {"url": "http://localhost:9002/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "duration": 52, "startTime": 5843.333}, {"url": "http://localhost:9002/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "duration": 51, "startTime": 6434.333}], "sortedBy": ["duration"], "skipSumming": ["startTime"], "debugData": {"type": "debugdata", "urls": ["http://localhost:9002/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "http://localhost:9002/_next/static/chunks/node_modules_next_dist_client_43e3ffb8._.js", "http://localhost:9002/_next/static/chunks/node_modules_4cec1fd0._.js", "http://localhost:9002/demo"], "tasks": [{"urlIndex": 0, "startTime": 5895.3, "duration": 371, "other": 371}, {"urlIndex": 1, "startTime": 5372.3, "duration": 252, "other": 252, "scriptEvaluation": 0}, {"urlIndex": 0, "startTime": 6266.3, "duration": 168, "other": 168}, {"urlIndex": 2, "startTime": 5624.3, "duration": 117, "other": 117, "scriptEvaluation": 0}, {"urlIndex": 0, "startTime": 5268.3, "duration": 104, "other": 104, "scriptEvaluation": 0}, {"urlIndex": 0, "startTime": 5741.3, "duration": 102, "other": 102}, {"urlIndex": 0, "startTime": 6485.3, "duration": 77, "other": 77}, {"urlIndex": 0, "startTime": 6960.3, "duration": 70, "other": 70}, {"urlIndex": 0, "startTime": 7030.3, "duration": 68, "other": 68}, {"urlIndex": 0, "startTime": 7098.3, "duration": 68, "other": 68}, {"urlIndex": 3, "startTime": 759.1, "duration": 61, "other": 61, "paintCompositeRender": 0, "scriptEvaluation": 0, "styleLayout": 0}, {"urlIndex": 1, "startTime": 6698.3, "duration": 57, "other": 57}, {"urlIndex": 0, "startTime": 6755.3, "duration": 53, "other": 53}, {"urlIndex": 0, "startTime": 5843.3, "duration": 52, "other": 52}, {"urlIndex": 0, "startTime": 6434.3, "duration": 51, "other": 51}]}}, "guidanceLevel": 1}, "non-composited-animations": {"id": "non-composited-animations", "title": "Avoid non-composited animations", "description": "Animations which are not composited can be janky and increase CLS. [Learn how to avoid non-composited animations](https://developer.chrome.com/docs/lighthouse/performance/non-composited-animations/)", "score": 1, "scoreDisplayMode": "informative", "displayValue": "2 animated elements found", "metricSavings": {"CLS": 0}, "details": {"type": "table", "headings": [{"key": "node", "valueType": "node", "subItemsHeading": {"key": "failureReason", "valueType": "text"}, "label": "Element"}, {"key": null, "valueType": "text", "subItemsHeading": {"key": "animation", "valueType": "text"}, "label": "Name"}], "items": [{"node": {"type": "node", "lhId": "page-1-path", "path": "1,HTML,1,BODY,16,NEXTJS-PORTAL,a,#document-fragment,5,DIV,0,DIV,0,DIV,1,DIV,0,DIV,0,BUTTON,0,svg,0,g,1,path", "selector": "button > svg > g > path.paused", "boundingRect": {"top": 2167, "bottom": 2178, "left": 42, "right": 42, "width": 0, "height": 12}, "snippet": "<path class=\"paused\" d=\"M11.825 1.5 V13.1\" stroke-width=\"1.86\" stroke=\"url(#next_logo_paint1_linear_1357_10853)\" stroke-dasharray=\"11.6\" stroke-dashoffset=\"11.6\">", "nodeLabel": "button > svg > g > path.paused"}, "subItems": {"type": "subitems", "items": [{"failureReason": "Unsupported CSS Property: stroke-dashoffset", "animation": "draw1"}]}}, {"node": {"type": "node", "lhId": "page-2-path", "path": "1,HTML,1,BODY,16,NEXTJS-PORTAL,a,#document-fragment,5,DIV,0,DIV,0,DIV,1,DIV,0,DIV,0,BUTTON,0,svg,0,g,0,path", "selector": "button > svg > g > path.paused", "boundingRect": {"top": 2166, "bottom": 2180, "left": 33, "right": 44, "width": 11, "height": 14}, "snippet": "<path class=\"paused\" d=\"M13.3 15.2 L2.34 1 V12.6\" fill=\"none\" stroke=\"url(#next_logo_paint0_linear_1357_10853)\" stroke-width=\"1.86\" mask=\"url(#next_logo_mask0)\" stroke-dasharray=\"29.6\" stroke-dashoffset=\"29.6\">", "nodeLabel": "button > svg > g > path.paused"}, "subItems": {"type": "subitems", "items": [{"failureReason": "Unsupported CSS Property: stroke-dashoffset", "animation": "draw0"}]}}]}, "guidanceLevel": 2}, "unsized-images": {"id": "unsized-images", "title": "Image elements have explicit `width` and `height`", "description": "Set an explicit width and height on image elements to reduce layout shifts and improve CLS. [Learn how to set image dimensions](https://web.dev/articles/optimize-cls#images_without_dimensions)", "score": 1, "scoreDisplayMode": "metricSavings", "metricSavings": {"CLS": 0}, "details": {"type": "table", "headings": [], "items": []}, "guidanceLevel": 4}, "prioritize-lcp-image": {"id": "prioritize-lcp-image", "title": "Preload Largest Contentful Paint image", "description": "If the LCP element is dynamically added to the page, you should preload the image in order to improve LCP. [Learn more about preloading LCP elements](https://web.dev/articles/optimize-lcp#optimize_when_the_resource_is_discovered).", "score": null, "scoreDisplayMode": "notApplicable", "metricSavings": {"LCP": 0}, "guidanceLevel": 4}, "script-treemap-data": {"id": "script-treemap-data", "title": "Script Treemap Data", "description": "Used for treemap app", "score": 1, "scoreDisplayMode": "informative", "details": {"type": "treemap-data", "nodes": [{"name": "http://localhost:9002/demo", "resourceBytes": 49203, "encodedBytes": 8635, "unusedBytes": 35, "children": [{"name": "(inline) document.queryS…", "resourceBytes": 130, "unusedBytes": 35}, {"name": "(inline) document.addEve…", "resourceBytes": 151, "unusedBytes": 0}, {"name": "(inline) (self.__next_f=…", "resourceBytes": 43, "unusedBytes": 0}, {"name": "(inline) self.__next_f.p…", "resourceBytes": 2161, "unusedBytes": 0}, {"name": "(inline) self.__next_f.p…", "resourceBytes": 2158, "unusedBytes": 0}, {"name": "(inline) self.__next_f.p…", "resourceBytes": 2356, "unusedBytes": 0}, {"name": "(inline) self.__next_f.p…", "resourceBytes": 803, "unusedBytes": 0}, {"name": "(inline) self.__next_f.p…", "resourceBytes": 5667, "unusedBytes": 0}, {"name": "(inline) self.__next_f.p…", "resourceBytes": 1337, "unusedBytes": 0}, {"name": "(inline) self.__next_f.p…", "resourceBytes": 30399, "unusedBytes": 0}, {"name": "(inline) self.__next_f.p…", "resourceBytes": 661, "unusedBytes": 0}, {"name": "(inline) self.__next_f.p…", "resourceBytes": 2477, "unusedBytes": 0}, {"name": "(inline) self.__next_f.p…", "resourceBytes": 164, "unusedBytes": 0}, {"name": "(inline) self.__next_f.p…", "resourceBytes": 696, "unusedBytes": 0}]}, {"name": "http://localhost:9002/_next/static/chunks/%5Bturbopack%5D_browser_dev_hmr-client_hmr-client_ts_49a6ea35._.js", "resourceBytes": 743, "encodedBytes": 743, "unusedBytes": 0}, {"name": "http://localhost:9002/_next/static/chunks/node_modules_%40swc_helpers_cjs_00636ac3._.js", "resourceBytes": 3449, "encodedBytes": 991, "unusedBytes": 0}, {"name": "http://localhost:9002/_next/static/chunks/_e69f0d32._.js", "resourceBytes": 613, "encodedBytes": 613, "unusedBytes": 0}, {"name": "http://localhost:9002/_next/static/chunks/node_modules_next_dist_3bfaed20._.js", "resourceBytes": 99721, "encodedBytes": 18697, "unusedBytes": 63927}, {"name": "http://localhost:9002/_next/static/chunks/_be317ff2._.js", "resourceBytes": 66161, "encodedBytes": 15924, "unusedBytes": 32045}, {"name": "http://localhost:9002/_next/static/chunks/src_app_favicon_ico_mjs_79b6a596._.js", "resourceBytes": 380, "encodedBytes": 380, "unusedBytes": 0}, {"name": "http://localhost:9002/_next/static/chunks/node_modules_next_dist_1a6ee436._.js", "resourceBytes": 117515, "encodedBytes": 18445, "unusedBytes": 73435}, {"name": "http://localhost:9002/_next/static/chunks/[turbopack]_browser_dev_hmr-client_hmr-client_ts_154e108b._.js", "resourceBytes": 743, "encodedBytes": 743, "unusedBytes": 502}, {"name": "http://localhost:9002/_next/static/chunks/src_app_layout_tsx_f0e4c1a2._.js", "resourceBytes": 541, "encodedBytes": 541, "unusedBytes": 0}, {"name": "http://localhost:9002/_next/static/chunks/src_7b7cfb18._.js", "resourceBytes": 304964, "encodedBytes": 25652, "unusedBytes": 50792}, {"name": "http://localhost:9002/_next/static/chunks/node_modules_c955a5f0._.js", "resourceBytes": 33884, "encodedBytes": 4424, "unusedBytes": 1809}, {"name": "http://localhost:9002/_next/static/chunks/src_app_demo_page_tsx_4a1ef595._.js", "resourceBytes": 405, "encodedBytes": 405, "unusedBytes": 0}, {"name": "http://localhost:9002/_next/static/chunks/src_39ae3a7d._.js", "resourceBytes": 183782, "encodedBytes": 10836, "unusedBytes": 612}, {"name": "http://localhost:9002/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "resourceBytes": 1104803, "encodedBytes": 196693, "unusedBytes": 334947}, {"name": "http://localhost:9002/_next/static/chunks/node_modules_next_dist_client_43e3ffb8._.js", "resourceBytes": 961748, "encodedBytes": 164074, "unusedBytes": 372054}, {"name": "http://localhost:9002/_next/static/chunks/%5Bturbopack%5D_browser_dev_hmr-client_hmr-client_ts_154e108b._.js", "resourceBytes": 743, "encodedBytes": 743, "unusedBytes": 502}, {"name": "http://localhost:9002/_next/static/chunks/node_modules_4cec1fd0._.js", "resourceBytes": 911631, "encodedBytes": 120464, "unusedBytes": 440398}, {"name": "http://localhost:9002/_next/static/chunks/%5Bturbopack%5D_browser_dev_hmr-client_hmr-client_ts_5160d576._.js", "resourceBytes": 456, "encodedBytes": 456, "unusedBytes": 0}, {"name": "http://localhost:9002/_next/static/chunks/%5Bturbopack%5D_browser_dev_hmr-client_hmr-client_ts_61dcf9ba._.js", "resourceBytes": 15499, "encodedBytes": 3833, "unusedBytes": 10928}]}}, "uses-long-cache-ttl": {"id": "uses-long-cache-ttl", "title": "Uses efficient cache policy on static assets", "description": "A long cache lifetime can speed up repeat visits to your page. [Learn more about efficient cache policies](https://developer.chrome.com/docs/lighthouse/performance/uses-long-cache-ttl/).", "score": 1, "scoreDisplayMode": "metricSavings", "numericValue": 0, "numericUnit": "byte", "displayValue": "0 resources found", "details": {"type": "table", "headings": [], "items": []}, "guidanceLevel": 3}, "total-byte-weight": {"id": "total-byte-weight", "title": "Avoids enormous network payloads", "description": "Large network payloads cost users real money and are highly correlated with long load times. [Learn how to reduce payload sizes](https://developer.chrome.com/docs/lighthouse/performance/total-byte-weight/).", "score": 1, "scoreDisplayMode": "metricSavings", "numericValue": 775563, "numericUnit": "byte", "displayValue": "Total size was 757 KiB", "details": {"type": "table", "headings": [{"key": "url", "valueType": "url", "label": "URL"}, {"key": "totalBytes", "valueType": "bytes", "label": "Transfer Size"}], "items": [{"url": "http://localhost:9002/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "totalBytes": 197173}, {"url": "http://localhost:9002/_next/static/chunks/node_modules_next_dist_client_43e3ffb8._.js", "totalBytes": 164553}, {"url": "http://localhost:9002/_next/static/chunks/node_modules_4cec1fd0._.js", "totalBytes": 120943}, {"url": "http://localhost:9002/placeholder-avatar.jpg", "totalBytes": 49793}, {"url": "http://localhost:9002/_next/static/media/or3nQ6H_1_WfwkMZI_qYFrcdmhHkjko-s.p.be19f591.woff2", "totalBytes": 31687}, {"url": "http://localhost:9002/_next/static/media/gyByhwUxId8gMEwcGFWNOITd-s.p.da1ebef7.woff2", "totalBytes": 28755}, {"url": "http://localhost:9002/__nextjs_font/geist-latin.woff2", "totalBytes": 28578}, {"url": "http://localhost:9002/_next/static/chunks/src_7b7cfb18._.js", "totalBytes": 26131}, {"url": "http://localhost:9002/demo", "totalBytes": 20233}, {"url": "http://localhost:9002/_next/static/chunks/node_modules_next_dist_3bfaed20._.js", "totalBytes": 19176}], "sortedBy": ["totalBytes"]}, "guidanceLevel": 1}, "offscreen-images": {"id": "offscreen-images", "title": "Defer offscreen images", "description": "Consider lazy-loading offscreen and hidden images after all critical resources have finished loading to lower time to interactive. [Learn how to defer offscreen images](https://developer.chrome.com/docs/lighthouse/performance/offscreen-images/).", "score": 1, "scoreDisplayMode": "metricSavings", "numericValue": 0, "numericUnit": "millisecond", "displayValue": "", "warnings": [], "metricSavings": {"FCP": 0, "LCP": 0}, "details": {"type": "opportunity", "headings": [], "items": [], "overallSavingsMs": 0, "overallSavingsBytes": 0, "sortedBy": ["wastedBytes"], "debugData": {"type": "debugdata", "metricSavings": {"FCP": 0, "LCP": 0}}}, "guidanceLevel": 2}, "render-blocking-resources": {"id": "render-blocking-resources", "title": "Eliminate render-blocking resources", "description": "Resources are blocking the first paint of your page. Consider delivering critical JS/CSS inline and deferring all non-critical JS/styles. [Learn how to eliminate render-blocking resources](https://developer.chrome.com/docs/lighthouse/performance/render-blocking-resources/).", "score": 1, "scoreDisplayMode": "metricSavings", "numericValue": 0, "numericUnit": "millisecond", "metricSavings": {"FCP": 0, "LCP": 0}, "details": {"type": "opportunity", "headings": [], "items": [], "overallSavingsMs": 0}, "guidanceLevel": 2}, "unminified-css": {"id": "unminified-css", "title": "Minify CSS", "description": "Minifying CSS files can reduce network payload sizes. [Learn how to minify CSS](https://developer.chrome.com/docs/lighthouse/performance/unminified-css/).", "score": 1, "scoreDisplayMode": "metricSavings", "numericValue": 0, "numericUnit": "millisecond", "displayValue": "", "metricSavings": {"FCP": 0, "LCP": 0}, "details": {"type": "opportunity", "headings": [], "items": [], "overallSavingsMs": 0, "overallSavingsBytes": 0, "sortedBy": ["wastedBytes"], "debugData": {"type": "debugdata", "metricSavings": {"FCP": 0, "LCP": 0}}}, "guidanceLevel": 3}, "unminified-javascript": {"id": "unminified-javascript", "title": "Minify JavaScript", "description": "Minifying JavaScript files can reduce payload sizes and script parse time. [Learn how to minify JavaScript](https://developer.chrome.com/docs/lighthouse/performance/unminified-javascript/).", "score": 0, "scoreDisplayMode": "metricSavings", "numericValue": 1050, "numericUnit": "millisecond", "displayValue": "Est savings of 173 KiB", "warnings": [], "metricSavings": {"FCP": 0, "LCP": 1050}, "details": {"type": "opportunity", "headings": [{"key": "url", "valueType": "url", "label": "URL"}, {"key": "totalBytes", "valueType": "bytes", "label": "Transfer Size"}, {"key": "wastedBytes", "valueType": "bytes", "label": "Est Savings"}], "items": [{"url": "http://localhost:9002/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "totalBytes": 196693, "wastedBytes": 58308, "wastedPercent": 29.64392746942215}, {"url": "http://localhost:9002/_next/static/chunks/node_modules_next_dist_client_43e3ffb8._.js", "totalBytes": 164074, "wastedBytes": 52558, "wastedPercent": 32.0333392947009}, {"url": "http://localhost:9002/_next/static/chunks/node_modules_4cec1fd0._.js", "totalBytes": 120464, "wastedBytes": 34634, "wastedPercent": 28.75033867869785}, {"url": "http://localhost:9002/_next/static/chunks/_be317ff2._.js", "totalBytes": 15924, "wastedBytes": 7846, "wastedPercent": 49.26920693459893}, {"url": "http://localhost:9002/_next/static/chunks/node_modules_next_dist_1a6ee436._.js", "totalBytes": 18445, "wastedBytes": 7094, "wastedPercent": 38.461473003446365}, {"url": "http://localhost:9002/_next/static/chunks/src_7b7cfb18._.js", "totalBytes": 25652, "wastedBytes": 6464, "wastedPercent": 25.199039886675145}, {"url": "http://localhost:9002/_next/static/chunks/node_modules_next_dist_3bfaed20._.js", "totalBytes": 18697, "wastedBytes": 5781, "wastedPercent": 30.920267546454603}, {"url": "http://localhost:9002/_next/static/chunks/src_39ae3a7d._.js", "totalBytes": 10836, "wastedBytes": 4406, "wastedPercent": 40.656865198985756}], "overallSavingsMs": 1050, "overallSavingsBytes": 177091, "sortedBy": ["wastedBytes"], "debugData": {"type": "debugdata", "metricSavings": {"FCP": 0, "LCP": 1050}}}, "guidanceLevel": 3}, "unused-css-rules": {"id": "unused-css-rules", "title": "Reduce unused CSS", "description": "Reduce unused rules from stylesheets and defer CSS not used for above-the-fold content to decrease bytes consumed by network activity. [Learn how to reduce unused CSS](https://developer.chrome.com/docs/lighthouse/performance/unused-css-rules/).", "score": 0, "scoreDisplayMode": "metricSavings", "numericValue": 150, "numericUnit": "millisecond", "displayValue": "Est savings of 11 KiB", "metricSavings": {"FCP": 0, "LCP": 150}, "details": {"type": "opportunity", "headings": [{"key": "url", "valueType": "url", "label": "URL"}, {"key": "totalBytes", "valueType": "bytes", "label": "Transfer Size"}, {"key": "wastedBytes", "valueType": "bytes", "label": "Est Savings"}], "items": [{"url": "http://localhost:9002/_next/static/chunks/%5Broot%20of%20the%20server%5D__f83bf54e._.css", "wastedBytes": 11422, "wastedPercent": 79.68085460784205, "totalBytes": 14335}], "overallSavingsMs": 150, "overallSavingsBytes": 11422, "sortedBy": ["wastedBytes"], "debugData": {"type": "debugdata", "metricSavings": {"FCP": 0, "LCP": 150}}}, "guidanceLevel": 1}, "unused-javascript": {"id": "unused-javascript", "title": "Reduce unused JavaScript", "description": "Reduce unused JavaScript and defer loading scripts until they are required to decrease bytes consumed by network activity. [Learn how to reduce unused JavaScript](https://developer.chrome.com/docs/lighthouse/performance/unused-javascript/).", "score": 0, "scoreDisplayMode": "metricSavings", "numericValue": 900, "numericUnit": "millisecond", "displayValue": "Est savings of 177 KiB", "metricSavings": {"FCP": 0, "LCP": 900}, "details": {"type": "opportunity", "headings": [{"key": "url", "valueType": "url", "subItemsHeading": {"key": "source", "valueType": "code"}, "label": "URL"}, {"key": "totalBytes", "valueType": "bytes", "subItemsHeading": {"key": "sourceBytes"}, "label": "Transfer Size"}, {"key": "wastedBytes", "valueType": "bytes", "subItemsHeading": {"key": "sourceWastedBytes"}, "label": "Est Savings"}], "items": [{"url": "http://localhost:9002/_next/static/chunks/node_modules_next_dist_client_43e3ffb8._.js", "totalBytes": 164071, "wastedBytes": 63471, "wastedPercent": 38.685185724326956}, {"url": "http://localhost:9002/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "totalBytes": 196693, "wastedBytes": 59632, "wastedPercent": 30.317350695101297}, {"url": "http://localhost:9002/_next/static/chunks/node_modules_4cec1fd0._.js", "totalBytes": 120463, "wastedBytes": 58194, "wastedPercent": 48.30880038085585}], "overallSavingsMs": 900, "overallSavingsBytes": 181297, "sortedBy": ["wastedBytes"], "debugData": {"type": "debugdata", "metricSavings": {"FCP": 0, "LCP": 900}}}, "guidanceLevel": 1}, "modern-image-formats": {"id": "modern-image-formats", "title": "Serve images in next-gen formats", "description": "Image formats like WebP and AVIF often provide better compression than PNG or JPEG, which means faster downloads and less data consumption. [Learn more about modern image formats](https://developer.chrome.com/docs/lighthouse/performance/uses-webp-images/).", "score": 1, "scoreDisplayMode": "metricSavings", "numericValue": 0, "numericUnit": "millisecond", "displayValue": "", "warnings": [], "metricSavings": {"FCP": 0, "LCP": 0}, "details": {"type": "opportunity", "headings": [], "items": [], "overallSavingsMs": 0, "overallSavingsBytes": 0, "sortedBy": ["wastedBytes"], "debugData": {"type": "debugdata", "metricSavings": {"FCP": 0, "LCP": 0}}}, "guidanceLevel": 3}, "uses-optimized-images": {"id": "uses-optimized-images", "title": "Efficiently encode images", "description": "Optimized images load faster and consume less cellular data. [Learn how to efficiently encode images](https://developer.chrome.com/docs/lighthouse/performance/uses-optimized-images/).", "score": 1, "scoreDisplayMode": "metricSavings", "numericValue": 0, "numericUnit": "millisecond", "displayValue": "", "warnings": [], "metricSavings": {"FCP": 0, "LCP": 0}, "details": {"type": "opportunity", "headings": [], "items": [], "overallSavingsMs": 0, "overallSavingsBytes": 0, "sortedBy": ["wastedBytes"], "debugData": {"type": "debugdata", "metricSavings": {"FCP": 0, "LCP": 0}}}, "guidanceLevel": 2}, "uses-text-compression": {"id": "uses-text-compression", "title": "Enable text compression", "description": "Text-based resources should be served with compression (gzip, deflate or brotli) to minimize total network bytes. [Learn more about text compression](https://developer.chrome.com/docs/lighthouse/performance/uses-text-compression/).", "score": 1, "scoreDisplayMode": "metricSavings", "numericValue": 0, "numericUnit": "millisecond", "displayValue": "", "metricSavings": {"FCP": 0, "LCP": 0}, "details": {"type": "opportunity", "headings": [], "items": [], "overallSavingsMs": 0, "overallSavingsBytes": 0, "sortedBy": ["wastedBytes"], "debugData": {"type": "debugdata", "metricSavings": {"FCP": 0, "LCP": 0}}}, "guidanceLevel": 3}, "uses-responsive-images": {"id": "uses-responsive-images", "title": "Properly size images", "description": "Serve images that are appropriately-sized to save cellular data and improve load time. [Learn how to size images](https://developer.chrome.com/docs/lighthouse/performance/uses-responsive-images/).", "score": 1, "scoreDisplayMode": "metricSavings", "numericValue": 0, "numericUnit": "millisecond", "displayValue": "", "metricSavings": {"FCP": 0, "LCP": 0}, "details": {"type": "opportunity", "headings": [], "items": [], "overallSavingsMs": 0, "overallSavingsBytes": 0, "sortedBy": ["wastedBytes"], "debugData": {"type": "debugdata", "metricSavings": {"FCP": 0, "LCP": 0}}}, "guidanceLevel": 2}, "efficient-animated-content": {"id": "efficient-animated-content", "title": "Use video formats for animated content", "description": "Large GIFs are inefficient for delivering animated content. Consider using MPEG4/WebM videos for animations and PNG/WebP for static images instead of GIF to save network bytes. [Learn more about efficient video formats](https://developer.chrome.com/docs/lighthouse/performance/efficient-animated-content/)", "score": 1, "scoreDisplayMode": "metricSavings", "numericValue": 0, "numericUnit": "millisecond", "displayValue": "", "metricSavings": {"FCP": 0, "LCP": 0}, "details": {"type": "opportunity", "headings": [], "items": [], "overallSavingsMs": 0, "overallSavingsBytes": 0, "sortedBy": ["wastedBytes"], "debugData": {"type": "debugdata", "metricSavings": {"FCP": 0, "LCP": 0}}}, "guidanceLevel": 3}, "duplicated-javascript": {"id": "duplicated-javascript", "title": "Remove duplicate modules in JavaScript bundles", "description": "Remove large, duplicate JavaScript modules from bundles to reduce unnecessary bytes consumed by network activity. ", "score": 1, "scoreDisplayMode": "metricSavings", "numericValue": 0, "numericUnit": "millisecond", "displayValue": "", "metricSavings": {"FCP": 0, "LCP": 0}, "details": {"type": "opportunity", "headings": [], "items": [], "overallSavingsMs": 0, "overallSavingsBytes": 0, "sortedBy": ["wastedBytes"], "debugData": {"type": "debugdata", "metricSavings": {"FCP": 0, "LCP": 0}}}, "guidanceLevel": 2}, "legacy-javascript": {"id": "legacy-javascript", "title": "Avoid serving legacy JavaScript to modern browsers", "description": "Polyfills and transforms enable legacy browsers to use new JavaScript features. However, many aren't necessary for modern browsers. Consider modifying your JavaScript build process to not transpile [Baseline](https://web.dev/baseline) features, unless you know you must support legacy browsers. [Learn why most sites can deploy ES6+ code without transpiling](https://philipwalton.com/articles/the-state-of-es5-on-the-web/)", "score": 0, "scoreDisplayMode": "metricSavings", "numericValue": 150, "numericUnit": "millisecond", "displayValue": "Est savings of 8 KiB", "warnings": [], "metricSavings": {"FCP": 0, "LCP": 150}, "details": {"type": "opportunity", "headings": [{"key": "url", "valueType": "url", "subItemsHeading": {"key": "location", "valueType": "source-location"}, "label": "URL"}, {"key": null, "valueType": "code", "subItemsHeading": {"key": "signal"}, "label": ""}, {"key": "wastedBytes", "valueType": "bytes", "label": "Est Savings"}], "items": [{"url": "http://localhost:9002/_next/static/chunks/node_modules_next_dist_3bfaed20._.js", "wastedBytes": 8209, "subItems": {"type": "subitems", "items": [{"signal": "Array.prototype.at", "location": {"type": "source-location", "url": "http://localhost:9002/_next/static/chunks/node_modules_next_dist_3bfaed20._.js", "urlProvider": "network", "line": 40, "column": 27}}, {"signal": "Array.prototype.flat", "location": {"type": "source-location", "url": "http://localhost:9002/_next/static/chunks/node_modules_next_dist_3bfaed20._.js", "urlProvider": "network", "line": 20, "column": 29}}, {"signal": "Array.prototype.flatMap", "location": {"type": "source-location", "url": "http://localhost:9002/_next/static/chunks/node_modules_next_dist_3bfaed20._.js", "urlProvider": "network", "line": 22, "column": 3}}, {"signal": "Object.fromEntries", "location": {"type": "source-location", "url": "http://localhost:9002/_next/static/chunks/node_modules_next_dist_3bfaed20._.js", "urlProvider": "network", "line": 36, "column": 27}}, {"signal": "Object.hasOwn", "location": {"type": "source-location", "url": "http://localhost:9002/_next/static/chunks/node_modules_next_dist_3bfaed20._.js", "urlProvider": "network", "line": 43, "column": 22}}, {"signal": "String.prototype.trimEnd", "location": {"type": "source-location", "url": "http://localhost:9002/_next/static/chunks/node_modules_next_dist_3bfaed20._.js", "urlProvider": "network", "line": 14, "column": 127}}, {"signal": "String.prototype.trimStart", "location": {"type": "source-location", "url": "http://localhost:9002/_next/static/chunks/node_modules_next_dist_3bfaed20._.js", "urlProvider": "network", "line": 14, "column": 36}}]}, "totalBytes": 0}, {"url": "http://localhost:9002/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "wastedBytes": 181, "subItems": {"type": "subitems", "items": [{"signal": "@babel/plugin-transform-classes", "location": {"type": "source-location", "url": "http://localhost:9002/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "urlProvider": "network", "line": 18238, "column": 41}}]}, "totalBytes": 0}], "overallSavingsMs": 150, "overallSavingsBytes": 8390, "sortedBy": ["wastedBytes"], "debugData": {"type": "debugdata", "metricSavings": {"FCP": 0, "LCP": 150}}}, "guidanceLevel": 2}, "dom-size": {"id": "dom-size", "title": "Avoids an excessive DOM size", "description": "A large DOM will increase memory usage, cause longer [style calculations](https://developers.google.com/web/fundamentals/performance/rendering/reduce-the-scope-and-complexity-of-style-calculations), and produce costly [layout reflows](https://developers.google.com/speed/articles/reflow). [Learn how to avoid an excessive DOM size](https://developer.chrome.com/docs/lighthouse/performance/dom-size/).", "score": 1, "scoreDisplayMode": "metricSavings", "numericValue": 385, "numericUnit": "element", "displayValue": "385 elements", "metricSavings": {"TBT": 0}, "details": {"type": "table", "headings": [{"key": "statistic", "valueType": "text", "label": "Statistic"}, {"key": "node", "valueType": "node", "label": "Element"}, {"key": "value", "valueType": "numeric", "label": "Value"}], "items": [{"statistic": "Total DOM Elements", "value": {"type": "numeric", "granularity": 1, "value": 385}}, {"node": {"type": "node", "lhId": "1-0-SPAN", "path": "1,HTML,1,BODY,1,DIV,0,DIV,0,DIV,1,MAIN,0,DIV,2,DIV,1,DIV,0,DIV,1,DIV,0,DIV,0,DIV,0,DIV,0,DIV,0,SPAN", "selector": "div.flex > div.flex > div.w-8 > span.text-sm", "boundingRect": {"top": 1213, "bottom": 1233, "left": 76, "right": 86, "width": 10, "height": 20}, "snippet": "<span class=\"text-sm font-bold text-primary\">", "nodeLabel": "B"}, "statistic": "Maximum DOM Depth", "value": {"type": "numeric", "granularity": 1, "value": 15}}, {"node": {"type": "node", "lhId": "1-1-BODY", "path": "1,<PERSON><PERSON><PERSON>,1,<PERSON><PERSON><PERSON>", "selector": "body.geist_7261fffb-module__FDFYPa__variable", "boundingRect": {"top": 0, "bottom": 2210, "left": 0, "right": 412, "width": 412, "height": 2210}, "snippet": "<body class=\"geist_7261fffb-module__FDFYPa__variable geist_mono_b5dd9970-module__erib8q…\">", "nodeLabel": "body.geist_7261fffb-module__FDFYPa__variable"}, "statistic": "Maximum Child Elements", "value": {"type": "numeric", "granularity": 1, "value": 20}}]}, "guidanceLevel": 1}, "no-document-write": {"id": "no-document-write", "title": "Avoids `document.write()`", "description": "For users on slow connections, external scripts dynamically injected via `document.write()` can delay page load by tens of seconds. [Learn how to avoid document.write()](https://developer.chrome.com/docs/lighthouse/best-practices/no-document-write/).", "score": 1, "scoreDisplayMode": "metricSavings", "details": {"type": "table", "headings": [], "items": []}, "guidanceLevel": 2}, "uses-http2": {"id": "uses-http2", "title": "Use HTTP/2", "description": "HTTP/2 offers many benefits over HTTP/1.1, including binary headers and multiplexing. [Learn more about HTTP/2](https://developer.chrome.com/docs/lighthouse/best-practices/uses-http2/).", "score": 1, "scoreDisplayMode": "metricSavings", "numericValue": 0, "numericUnit": "millisecond", "metricSavings": {"LCP": 0, "FCP": 0}, "details": {"type": "opportunity", "headings": [], "items": [], "overallSavingsMs": 0}, "guidanceLevel": 3}, "uses-passive-event-listeners": {"id": "uses-passive-event-listeners", "title": "Uses passive listeners to improve scrolling performance", "description": "Consider marking your touch and wheel event listeners as `passive` to improve your page's scroll performance. [Learn more about adopting passive event listeners](https://developer.chrome.com/docs/lighthouse/best-practices/uses-passive-event-listeners/).", "score": 1, "scoreDisplayMode": "metricSavings", "details": {"type": "table", "headings": [], "items": []}, "guidanceLevel": 3}, "bf-cache": {"id": "bf-cache", "title": "Page prevented back/forward cache restoration", "description": "Many navigations are performed by going back to a previous page, or forwards again. The back/forward cache (bfcache) can speed up these return navigations. [Learn more about the bfcache](https://developer.chrome.com/docs/lighthouse/performance/bf-cache/)", "score": 0, "scoreDisplayMode": "binary", "displayValue": "4 failure reasons", "details": {"type": "table", "headings": [{"key": "reason", "valueType": "text", "subItemsHeading": {"key": "frameUrl", "valueType": "url"}, "label": "Failure reason"}, {"key": "failureType", "valueType": "text", "label": "Failure type"}], "items": [{"reason": "Pages with WebSocket cannot enter back/forward cache.", "failureType": "Pending browser support", "subItems": {"type": "subitems", "items": [{"frameUrl": "http://localhost:9002/demo"}]}, "protocolReason": "WebSocket"}, {"reason": "Pages whose main resource has cache-control:no-store cannot enter back/forward cache.", "failureType": "Not actionable", "subItems": {"type": "subitems", "items": [{"frameUrl": "http://localhost:9002/demo"}]}, "protocolReason": "MainResourceHasCacheControlNoStore"}, {"reason": "Back/forward cache is disabled because some JavaScript network request received resource with Cache-Control: no-store header.", "failureType": "Not actionable", "subItems": {"type": "subitems", "items": [{"frameUrl": "http://localhost:9002/demo"}]}, "protocolReason": "JsNetworkRequestReceivedCacheControlNoStoreResource"}, {"reason": "Back/forward cache is disabled because WebSocket has been used.", "failureType": "Not actionable", "subItems": {"type": "subitems", "items": [{"frameUrl": "http://localhost:9002/demo"}]}, "protocolReason": "WebSocketSticky"}]}, "guidanceLevel": 4}, "cache-insight": {"id": "cache-insight", "title": "Use efficient cache lifetimes", "description": "A long cache lifetime can speed up repeat visits to your page. [Learn more](https://web.dev/uses-long-cache-ttl/).", "score": null, "scoreDisplayMode": "notApplicable", "guidanceLevel": 3, "replacesAudits": ["uses-long-cache-ttl"]}, "cls-culprits-insight": {"id": "cls-culprits-insight", "title": "Layout shift culprits", "description": "Layout shifts occur when elements move absent any user interaction. [Investigate the causes of layout shifts](https://web.dev/articles/optimize-cls), such as elements being added, removed, or their fonts changing as the page loads.", "score": 1, "scoreDisplayMode": "numeric", "metricSavings": {"CLS": 0}, "details": {"type": "list", "items": []}, "guidanceLevel": 3, "replacesAudits": ["layout-shifts", "non-composited-animations", "unsized-images"]}, "document-latency-insight": {"id": "document-latency-insight", "title": "Document request latency", "description": "Your first network request is the most important.  Reduce its latency by avoiding redirects, ensuring a fast server response, and enabling text compression.", "score": 1, "scoreDisplayMode": "metricSavings", "metricSavings": {"FCP": 0, "LCP": 0}, "details": {"type": "checklist", "items": {"noRedirects": {"label": "Avoids redirects", "value": true}, "serverResponseIsFast": {"label": "Server responds quickly", "value": true}, "usesCompression": {"label": "Applies text compression", "value": true}}}, "guidanceLevel": 3, "replacesAudits": ["redirects", "server-response-time", "uses-text-compression"]}, "dom-size-insight": {"id": "dom-size-insight", "title": "Optimize DOM size", "description": "A large DOM can increase the duration of style calculations and layout reflows, impacting page responsiveness. A large DOM will also increase memory usage. [Learn how to avoid an excessive DOM size](https://developer.chrome.com/docs/lighthouse/performance/dom-size/).", "score": 1, "scoreDisplayMode": "numeric", "metricSavings": {"INP": 0}, "details": {"type": "table", "headings": [{"key": "statistic", "valueType": "text", "label": "Statistic"}, {"key": "node", "valueType": "node", "label": "Element"}, {"key": "value", "valueType": "numeric", "label": "Value"}], "items": [{"statistic": "Total elements", "value": {"type": "numeric", "granularity": 1, "value": 509}}, {"statistic": "Most children", "node": {"type": "node", "lhId": "page-4-BODY", "path": "1,<PERSON><PERSON><PERSON>,1,<PERSON><PERSON><PERSON>", "selector": "body.geist_7261fffb-module__FDFYPa__variable", "boundingRect": {"top": 0, "bottom": 2210, "left": 0, "right": 412, "width": 412, "height": 2210}, "snippet": "<body class=\"geist_7261fffb-module__FDFYPa__variable geist_mono_b5dd9970-module__erib8q…\">", "nodeLabel": "body.geist_7261fffb-module__FDFYPa__variable"}, "value": {"type": "numeric", "granularity": 1, "value": 16}}, {"statistic": "DOM depth", "node": {"type": "node", "lhId": "page-5-rect", "path": "1,DIV,0,DIV,1,DIV,0,DIV,1,DIV,0,DIV,0,DIV,0,DIV,1,DIV,0,LI,0,A,0,DIV,0,svg,0,rect", "selector": "a.peer/menu-button > div.relative > svg.lucide > rect", "boundingRect": {"top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "snippet": "<rect width=\"7\" height=\"9\" x=\"3\" y=\"3\" rx=\"1\">", "nodeLabel": "a.peer/menu-button > div.relative > svg.lucide > rect"}, "value": {"type": "numeric", "granularity": 1, "value": 18}}]}, "guidanceLevel": 3, "replacesAudits": ["dom-size"]}, "duplicated-javascript-insight": {"id": "duplicated-javascript-insight", "title": "Duplicated JavaScript", "description": "Remove large, duplicate JavaScript modules from bundles to reduce unnecessary bytes consumed by network activity.", "score": null, "scoreDisplayMode": "notApplicable", "guidanceLevel": 2, "replacesAudits": ["duplicated-javascript"]}, "font-display-insight": {"id": "font-display-insight", "title": "Font display", "description": "Consider setting [font-display](https://developer.chrome.com/blog/font-display) to swap or optional to ensure text is consistently visible. swap can be further optimized to mitigate layout shifts with [font metric overrides](https://developer.chrome.com/blog/font-fallbacks).", "score": null, "scoreDisplayMode": "notApplicable", "guidanceLevel": 3, "replacesAudits": ["font-display"]}, "forced-reflow-insight": {"id": "forced-reflow-insight", "title": "Forced reflow", "description": "Many APIs, typically reading layout geometry, force the rendering engine to pause script execution in order to calculate the style and layout. Learn more about [forced reflow](https://developers.google.com/web/fundamentals/performance/rendering/avoid-large-complex-layouts-and-layout-thrashing#avoid-forced-synchronous-layouts) and its mitigations.", "score": 0, "scoreDisplayMode": "numeric", "details": {"type": "list", "items": [{"type": "table", "headings": [{"key": "source", "valueType": "source-location", "label": "Top function call"}, {"key": "reflowTime", "valueType": "ms", "granularity": 1, "label": "Total reflow time"}], "items": [{"source": {"type": "source-location", "url": "http://localhost:9002/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "urlProvider": "network", "line": 1932, "column": 38}, "reflowTime": 4.577}]}, {"type": "table", "headings": [{"key": "source", "valueType": "source-location", "label": "Source"}, {"key": "reflowTime", "valueType": "ms", "granularity": 1, "label": "Total reflow time"}], "items": [{"source": {"type": "text", "value": "[unattributed]"}, "reflowTime": 26.799}, {"source": {"type": "source-location", "url": "http://localhost:9002/_next/static/chunks/node_modules_4cec1fd0._.js", "urlProvider": "network", "line": 6035, "column": 17}, "reflowTime": 1.286}, {"source": {"type": "source-location", "url": "http://localhost:9002/_next/static/chunks/src_7b7cfb18._.js", "urlProvider": "network", "line": 26, "column": 31}, "reflowTime": 3.291}]}]}, "guidanceLevel": 3}, "image-delivery-insight": {"id": "image-delivery-insight", "title": "Improve image delivery", "description": "Reducing the download time of images can improve the perceived load time of the page and LCP. [Learn more about optimizing image size](https://developer.chrome.com/docs/lighthouse/performance/uses-optimized-images/)", "score": null, "scoreDisplayMode": "notApplicable", "guidanceLevel": 3, "replacesAudits": ["modern-image-formats", "uses-optimized-images", "efficient-animated-content", "uses-responsive-images"]}, "interaction-to-next-paint-insight": {"id": "interaction-to-next-paint-insight", "title": "INP by phase", "description": "Start investigating with the longest phase. [Delays can be minimized](https://web.dev/articles/optimize-inp#optimize_interactions). To reduce processing duration, [optimize the main-thread costs](https://web.dev/articles/optimize-long-tasks), often JS.", "score": null, "scoreDisplayMode": "notApplicable", "guidanceLevel": 3, "replacesAudits": ["work-during-interaction"]}, "lcp-discovery-insight": {"id": "lcp-discovery-insight", "title": "LCP request discovery", "description": "Optimize LCP by making the LCP image [discoverable](https://web.dev/articles/optimize-lcp#1_eliminate_resource_load_delay) from the HTML immediately, and [avoiding lazy-loading](https://web.dev/articles/lcp-lazy-loading)", "score": null, "scoreDisplayMode": "notApplicable", "guidanceLevel": 3, "replacesAudits": ["prioritize-lcp-image", "lcp-lazy-loaded"]}, "lcp-phases-insight": {"id": "lcp-phases-insight", "title": "LCP by phase", "description": "Each [phase has specific improvement strategies](https://web.dev/articles/optimize-lcp#lcp-breakdown). Ideally, most of the LCP time should be spent on loading the resources, not within delays.", "score": 1, "scoreDisplayMode": "informative", "metricSavings": {"LCP": 0}, "details": {"type": "list", "items": [{"type": "table", "headings": [{"key": "label", "valueType": "text", "label": "Phase"}, {"key": "duration", "valueType": "ms", "label": "Duration"}], "items": [{"phase": "timeToFirstByte", "label": "Time to first byte", "duration": 189.779}, {"phase": "elementRenderDelay", "label": "Element render delay", "duration": 340.36699999999996}]}, {"type": "node", "lhId": "page-0-P", "path": "1,HTML,1,BODY,1,DIV,0,DIV,0,DIV,1,MAIN,0,DIV,0,DIV,0,DIV,1,P", "selector": "div.space-y-4 > div.flex > div.space-y-1 > p.text-muted-foreground", "boundingRect": {"top": 166, "bottom": 206, "left": 24, "right": 388, "width": 364, "height": 40}, "snippet": "<p class=\"text-muted-foreground text-sm sm:text-base\">", "nodeLabel": "Real-time portfolio overview and trading insights (Demo Data)"}]}, "guidanceLevel": 3, "replacesAudits": ["largest-contentful-paint-element"]}, "legacy-javascript-insight": {"id": "legacy-javascript-insight", "title": "Legacy JavaScript", "description": "Polyfills and transforms enable legacy browsers to use new JavaScript features. However, many aren't necessary for modern browsers. Consider modifying your JavaScript build process to not transpile [Baseline](https://web.dev/articles/baseline-and-polyfills) features, unless you know you must support legacy browsers. [Learn why most sites can deploy ES6+ code without transpiling](https://philipwalton.com/articles/the-state-of-es5-on-the-web/)", "score": 0, "scoreDisplayMode": "metricSavings", "displayValue": "Est savings of 8 KiB", "metricSavings": {"FCP": 0, "LCP": 150}, "details": {"type": "table", "headings": [{"key": "url", "valueType": "url", "subItemsHeading": {"key": "location", "valueType": "source-location"}, "label": "URL"}, {"key": null, "valueType": "code", "subItemsHeading": {"key": "signal"}, "label": ""}, {"key": "wastedBytes", "valueType": "bytes", "label": "Wasted bytes"}], "items": [{"url": "http://localhost:9002/_next/static/chunks/node_modules_next_dist_3bfaed20._.js", "wastedBytes": 8420, "subItems": {"type": "subitems", "items": [{"signal": "Array.prototype.at", "location": {"type": "source-location", "url": "http://localhost:9002/_next/static/chunks/node_modules_next_dist_3bfaed20._.js", "urlProvider": "network", "line": 40, "column": 27}}, {"signal": "Array.prototype.flat", "location": {"type": "source-location", "url": "http://localhost:9002/_next/static/chunks/node_modules_next_dist_3bfaed20._.js", "urlProvider": "network", "line": 20, "column": 29}}, {"signal": "Array.prototype.flatMap", "location": {"type": "source-location", "url": "http://localhost:9002/_next/static/chunks/node_modules_next_dist_3bfaed20._.js", "urlProvider": "network", "line": 22, "column": 3}}, {"signal": "Object.fromEntries", "location": {"type": "source-location", "url": "http://localhost:9002/_next/static/chunks/node_modules_next_dist_3bfaed20._.js", "urlProvider": "network", "line": 36, "column": 27}}, {"signal": "Object.hasOwn", "location": {"type": "source-location", "url": "http://localhost:9002/_next/static/chunks/node_modules_next_dist_3bfaed20._.js", "urlProvider": "network", "line": 43, "column": 22}}, {"signal": "String.prototype.trimEnd", "location": {"type": "source-location", "url": "http://localhost:9002/_next/static/chunks/node_modules_next_dist_3bfaed20._.js", "urlProvider": "network", "line": 14, "column": 127}}, {"signal": "String.prototype.trimStart", "location": {"type": "source-location", "url": "http://localhost:9002/_next/static/chunks/node_modules_next_dist_3bfaed20._.js", "urlProvider": "network", "line": 14, "column": 36}}]}}]}, "guidanceLevel": 2}, "modern-http-insight": {"id": "modern-http-insight", "title": "Modern HTTP", "description": "HTTP/2 and HTTP/3 offer many benefits over HTTP/1.1, such as multiplexing. [Learn more about using modern HTTP](https://developer.chrome.com/docs/lighthouse/best-practices/uses-http2/).", "score": null, "scoreDisplayMode": "notApplicable", "guidanceLevel": 3}, "network-dependency-tree-insight": {"id": "network-dependency-tree-insight", "title": "Network dependency tree", "description": "[Avoid chaining critical requests](https://developer.chrome.com/docs/lighthouse/performance/critical-request-chains) by reducing the length of chains, reducing the download size of resources, or deferring the download of unnecessary resources to improve page load.", "score": 1, "scoreDisplayMode": "numeric", "metricSavings": {"LCP": 0}, "details": {"type": "network-tree", "chains": {"9FA7086E4070BA1265A8FD4FFA2A4FC6": {"url": "http://localhost:9002/demo", "navStartToEndTime": 212, "transferSize": 20233, "isLongest": true, "children": {}}}, "longestChain": {"duration": 212}}, "guidanceLevel": 1, "replacesAudits": ["critical-request-chains"]}, "render-blocking-insight": {"id": "render-blocking-insight", "title": "Render blocking requests", "description": "Requests are blocking the page's initial render, which may delay LCP. [Deferring or inlining](https://web.dev/learn/performance/understanding-the-critical-path#render-blocking_resources/) can move these network requests out of the critical path.", "score": null, "scoreDisplayMode": "notApplicable", "guidanceLevel": 3, "replacesAudits": ["render-blocking-resources"]}, "third-parties-insight": {"id": "third-parties-insight", "title": "3rd parties", "description": "3rd party code can significantly impact load performance. [Reduce and defer loading of 3rd party code](https://developers.google.com/web/fundamentals/performance/optimizing-content-efficiency/loading-third-party-javascript/) to prioritize your page's content.", "score": null, "scoreDisplayMode": "notApplicable", "guidanceLevel": 3, "replacesAudits": ["third-party-summary"]}, "viewport-insight": {"id": "viewport-insight", "title": "Optimize viewport for mobile", "description": "Tap interactions may be [delayed by up to 300 ms](https://developer.chrome.com/blog/300ms-tap-delay-gone-away/) if the viewport is not optimized for mobile.", "score": 1, "scoreDisplayMode": "numeric", "metricSavings": {"INP": 0}, "details": {"type": "table", "headings": [{"key": "node", "valueType": "node", "label": ""}], "items": [{"node": {"type": "node", "lhId": "page-3-META", "path": "1,H<PERSON><PERSON>,0,HEAD,1,<PERSON><PERSON>", "selector": "head > meta", "boundingRect": {"top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "snippet": "<meta name=\"viewport\" content=\"width=device-width, initial-scale=1\">", "nodeLabel": "head > meta"}}]}, "guidanceLevel": 3, "replacesAudits": ["viewport"]}}, "configSettings": {"output": ["json"], "maxWaitForFcp": 30000, "maxWaitForLoad": 45000, "pauseAfterFcpMs": 1000, "pauseAfterLoadMs": 1000, "networkQuietThresholdMs": 1000, "cpuQuietThresholdMs": 1000, "formFactor": "mobile", "throttling": {"rttMs": 150, "throughputKbps": 1638.4, "requestLatencyMs": 562.5, "downloadThroughputKbps": 1474.5600000000002, "uploadThroughputKbps": 675, "cpuSlowdownMultiplier": 4}, "throttlingMethod": "simulate", "screenEmulation": {"mobile": true, "width": 412, "height": 823, "deviceScaleFactor": 1.75, "disabled": false}, "emulatedUserAgent": "Mozilla/5.0 (Linux; Android 11; moto g power (2022)) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "auditMode": false, "gatherMode": false, "clearStorageTypes": ["file_systems", "shader_cache", "service_workers", "cache_storage"], "disableStorageReset": false, "debugNavigation": false, "channel": "cli", "usePassiveGathering": false, "disableFullPageScreenshot": false, "skipAboutBlank": false, "blankPage": "about:blank", "ignoreStatusCode": false, "locale": "en-US", "blockedUrlPatterns": null, "additionalTraceCategories": null, "extraHeaders": null, "precomputedLanternData": null, "onlyAudits": null, "onlyCategories": ["performance"], "skipAudits": null}, "categories": {"performance": {"title": "Performance", "supportedModes": ["navigation", "timespan", "snapshot"], "auditRefs": [{"id": "first-contentful-paint", "weight": 10, "group": "metrics", "acronym": "FCP"}, {"id": "largest-contentful-paint", "weight": 25, "group": "metrics", "acronym": "LCP"}, {"id": "total-blocking-time", "weight": 30, "group": "metrics", "acronym": "TBT"}, {"id": "cumulative-layout-shift", "weight": 25, "group": "metrics", "acronym": "CLS"}, {"id": "speed-index", "weight": 10, "group": "metrics", "acronym": "SI"}, {"id": "cache-insight", "weight": 0, "group": "hidden"}, {"id": "cls-culprits-insight", "weight": 0, "group": "hidden"}, {"id": "document-latency-insight", "weight": 0, "group": "hidden"}, {"id": "dom-size-insight", "weight": 0, "group": "hidden"}, {"id": "duplicated-javascript-insight", "weight": 0, "group": "hidden"}, {"id": "font-display-insight", "weight": 0, "group": "hidden"}, {"id": "forced-reflow-insight", "weight": 0, "group": "hidden"}, {"id": "image-delivery-insight", "weight": 0, "group": "hidden"}, {"id": "interaction-to-next-paint-insight", "weight": 0, "group": "hidden"}, {"id": "lcp-discovery-insight", "weight": 0, "group": "hidden"}, {"id": "lcp-phases-insight", "weight": 0, "group": "hidden"}, {"id": "legacy-javascript-insight", "weight": 0, "group": "hidden"}, {"id": "modern-http-insight", "weight": 0, "group": "hidden"}, {"id": "network-dependency-tree-insight", "weight": 0, "group": "hidden"}, {"id": "render-blocking-insight", "weight": 0, "group": "hidden"}, {"id": "third-parties-insight", "weight": 0, "group": "hidden"}, {"id": "viewport-insight", "weight": 0, "group": "hidden"}, {"id": "interactive", "weight": 0, "group": "hidden", "acronym": "TTI"}, {"id": "max-potential-fid", "weight": 0, "group": "hidden"}, {"id": "first-meaningful-paint", "weight": 0, "acronym": "FMP", "group": "hidden"}, {"id": "render-blocking-resources", "weight": 0, "group": "diagnostics"}, {"id": "uses-responsive-images", "weight": 0, "group": "diagnostics"}, {"id": "offscreen-images", "weight": 0, "group": "diagnostics"}, {"id": "unminified-css", "weight": 0, "group": "diagnostics"}, {"id": "unminified-javascript", "weight": 0, "group": "diagnostics"}, {"id": "unused-css-rules", "weight": 0, "group": "diagnostics"}, {"id": "unused-javascript", "weight": 0, "group": "diagnostics"}, {"id": "uses-optimized-images", "weight": 0, "group": "diagnostics"}, {"id": "modern-image-formats", "weight": 0, "group": "diagnostics"}, {"id": "uses-text-compression", "weight": 0, "group": "diagnostics"}, {"id": "uses-rel-preconnect", "weight": 0, "group": "diagnostics"}, {"id": "server-response-time", "weight": 0, "group": "diagnostics"}, {"id": "redirects", "weight": 0, "group": "diagnostics"}, {"id": "uses-http2", "weight": 0, "group": "diagnostics"}, {"id": "efficient-animated-content", "weight": 0, "group": "diagnostics"}, {"id": "duplicated-javascript", "weight": 0, "group": "diagnostics"}, {"id": "legacy-javascript", "weight": 0, "group": "diagnostics"}, {"id": "prioritize-lcp-image", "weight": 0, "group": "diagnostics"}, {"id": "total-byte-weight", "weight": 0, "group": "diagnostics"}, {"id": "uses-long-cache-ttl", "weight": 0, "group": "diagnostics"}, {"id": "dom-size", "weight": 0, "group": "diagnostics"}, {"id": "critical-request-chains", "weight": 0, "group": "diagnostics"}, {"id": "user-timings", "weight": 0, "group": "diagnostics"}, {"id": "bootup-time", "weight": 0, "group": "diagnostics"}, {"id": "mainthread-work-breakdown", "weight": 0, "group": "diagnostics"}, {"id": "font-display", "weight": 0, "group": "diagnostics"}, {"id": "third-party-summary", "weight": 0, "group": "diagnostics"}, {"id": "third-party-facades", "weight": 0, "group": "diagnostics"}, {"id": "largest-contentful-paint-element", "weight": 0, "group": "diagnostics"}, {"id": "lcp-lazy-loaded", "weight": 0, "group": "diagnostics"}, {"id": "layout-shifts", "weight": 0, "group": "diagnostics"}, {"id": "uses-passive-event-listeners", "weight": 0, "group": "diagnostics"}, {"id": "no-document-write", "weight": 0, "group": "diagnostics"}, {"id": "long-tasks", "weight": 0, "group": "diagnostics"}, {"id": "non-composited-animations", "weight": 0, "group": "diagnostics"}, {"id": "unsized-images", "weight": 0, "group": "diagnostics"}, {"id": "viewport", "weight": 0, "group": "diagnostics"}, {"id": "bf-cache", "weight": 0, "group": "diagnostics"}, {"id": "network-requests", "weight": 0, "group": "hidden"}, {"id": "network-rtt", "weight": 0, "group": "hidden"}, {"id": "network-server-latency", "weight": 0, "group": "hidden"}, {"id": "main-thread-tasks", "weight": 0, "group": "hidden"}, {"id": "diagnostics", "weight": 0, "group": "hidden"}, {"id": "metrics", "weight": 0, "group": "hidden"}, {"id": "screenshot-thumbnails", "weight": 0, "group": "hidden"}, {"id": "final-screenshot", "weight": 0, "group": "hidden"}, {"id": "script-treemap-data", "weight": 0, "group": "hidden"}, {"id": "resource-summary", "weight": 0, "group": "hidden"}], "id": "performance", "score": 0.59}}, "categoryGroups": {"metrics": {"title": "Metrics"}, "insights": {"title": "Insights", "description": "These insights are also available in the Chrome DevTools Performance Panel - [record a trace](https://developer.chrome.com/docs/devtools/performance/reference) to view more detailed information."}, "diagnostics": {"title": "Diagnostics", "description": "More information about the performance of your application. These numbers don't [directly affect](https://developer.chrome.com/docs/lighthouse/performance/performance-scoring/) the Performance score."}, "a11y-best-practices": {"title": "Best practices", "description": "These items highlight common accessibility best practices."}, "a11y-color-contrast": {"title": "Contrast", "description": "These are opportunities to improve the legibility of your content."}, "a11y-names-labels": {"title": "Names and labels", "description": "These are opportunities to improve the semantics of the controls in your application. This may enhance the experience for users of assistive technology, like a screen reader."}, "a11y-navigation": {"title": "Navigation", "description": "These are opportunities to improve keyboard navigation in your application."}, "a11y-aria": {"title": "ARIA", "description": "These are opportunities to improve the usage of ARIA in your application which may enhance the experience for users of assistive technology, like a screen reader."}, "a11y-language": {"title": "Internationalization and localization", "description": "These are opportunities to improve the interpretation of your content by users in different locales."}, "a11y-audio-video": {"title": "Audio and video", "description": "These are opportunities to provide alternative content for audio and video. This may improve the experience for users with hearing or vision impairments."}, "a11y-tables-lists": {"title": "Tables and lists", "description": "These are opportunities to improve the experience of reading tabular or list data using assistive technology, like a screen reader."}, "seo-mobile": {"title": "Mobile Friendly", "description": "Make sure your pages are mobile friendly so users don’t have to pinch or zoom in order to read the content pages. [Learn how to make pages mobile-friendly](https://developers.google.com/search/mobile-sites/)."}, "seo-content": {"title": "Content Best Practices", "description": "Format your HTML in a way that enables crawlers to better understand your app’s content."}, "seo-crawl": {"title": "Crawling and Indexing", "description": "To appear in search results, crawlers need access to your app."}, "best-practices-trust-safety": {"title": "Trust and Safety"}, "best-practices-ux": {"title": "User Experience"}, "best-practices-browser-compat": {"title": "Browser Compatibility"}, "best-practices-general": {"title": "General"}, "hidden": {"title": ""}}, "stackPacks": [], "entities": [{"name": "localhost", "origins": ["http://localhost:9002"], "isFirstParty": true, "isUnrecognized": true}], "fullPageScreenshot": {"screenshot": {"data": "data:image/webp;base64,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", "width": 412, "height": 2210}, "nodes": {"page-0-P": {"id": "", "top": 166, "bottom": 206, "left": 24, "right": 388, "width": 364, "height": 40}, "page-1-path": {"id": "", "top": 2167, "bottom": 2178, "left": 42, "right": 42, "width": 0, "height": 12}, "page-2-path": {"id": "", "top": 2166, "bottom": 2180, "left": 33, "right": 44, "width": 11, "height": 14}, "page-3-META": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "page-4-BODY": {"id": "", "top": 0, "bottom": 2210, "left": 0, "right": 412, "width": 412, "height": 2210}, "page-5-rect": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "page-6-DIV": {"id": "", "top": 2154, "bottom": 2190, "left": 64, "right": 72, "width": 8, "height": 36}, "page-7-DIV": {"id": "", "top": 2142, "bottom": 2178, "left": 64, "right": 72, "width": 8, "height": 36}, "page-8-DIV": {"id": "", "top": 274, "bottom": 982, "left": 24, "right": 388, "width": 364, "height": 708}, "1-0-SPAN": {"id": "", "top": 1213, "bottom": 1233, "left": 76, "right": 86, "width": 10, "height": 20}, "1-1-BODY": {"id": "", "top": 0, "bottom": 2210, "left": 0, "right": 412, "width": 412, "height": 2210}, "1-2-LINK": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "1-3-LINK": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "1-4-LINK": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "1-5-LINK": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "1-6-LINK": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "1-7-LINK": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "1-8-LINK": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "1-9-LINK": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "1-10-LINK": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "1-11-LINK": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "1-12-LINK": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "1-13-LINK": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "1-14-META": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "1-15-META": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "1-16-META": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}, "1-17-META": {"id": "", "top": 0, "bottom": 0, "left": 0, "right": 0, "width": 0, "height": 0}}}, "timing": {"entries": [{"startTime": 1588.8, "name": "lh:config", "duration": 593.5, "entryType": "measure"}, {"startTime": 1590.19, "name": "lh:config:resolveArtifactsToDefns", "duration": 63.7, "entryType": "measure"}, {"startTime": 2182.43, "name": "lh:runner:gather", "duration": 8855.02, "entryType": "measure"}, {"startTime": 2239.28, "name": "lh:driver:connect", "duration": 5.87, "entryType": "measure"}, {"startTime": 2245.35, "name": "lh:driver:navigate", "duration": 4.41, "entryType": "measure"}, {"startTime": 2250.05, "name": "lh:gather:getBenchmarkIndex", "duration": 1002.76, "entryType": "measure"}, {"startTime": 3253.01, "name": "lh:gather:getVersion", "duration": 0.95, "entryType": "measure"}, {"startTime": 3254.83, "name": "lh:prepare:navigationMode", "duration": 36.48, "entryType": "measure"}, {"startTime": 3266.58, "name": "lh:storage:clearDataForOrigin", "duration": 11, "entryType": "measure"}, {"startTime": 3277.72, "name": "lh:storage:clearBrowserCaches", "duration": 11.93, "entryType": "measure"}, {"startTime": 3290.38, "name": "lh:gather:prepareThrottlingAndNetwork", "duration": 0.84, "entryType": "measure"}, {"startTime": 3318.03, "name": "lh:driver:navigate", "duration": 3876.9, "entryType": "measure"}, {"startTime": 7772.74, "name": "lh:computed:NetworkRecords", "duration": 1.19, "entryType": "measure"}, {"startTime": 7774.36, "name": "lh:gather:getArtifact:DevtoolsLog", "duration": 0.03, "entryType": "measure"}, {"startTime": 7774.4, "name": "lh:gather:getArtifact:Trace", "duration": 0.02, "entryType": "measure"}, {"startTime": 7774.43, "name": "lh:gather:getArtifact:ConsoleMessages", "duration": 0.02, "entryType": "measure"}, {"startTime": 7774.46, "name": "lh:gather:getArtifact:CSSUsage", "duration": 34, "entryType": "measure"}, {"startTime": 7808.48, "name": "lh:gather:getArtifact:DOMStats", "duration": 6.34, "entryType": "measure"}, {"startTime": 7814.84, "name": "lh:gather:getArtifact:ImageElements", "duration": 12.15, "entryType": "measure"}, {"startTime": 7827.01, "name": "lh:gather:getArtifact:JsUsage", "duration": 0.09, "entryType": "measure"}, {"startTime": 7827.19, "name": "lh:gather:getArtifact:LinkElements", "duration": 3.73, "entryType": "measure"}, {"startTime": 7830.08, "name": "lh:computed:MainResource", "duration": 0.17, "entryType": "measure"}, {"startTime": 7830.93, "name": "lh:gather:getArtifact:MetaElements", "duration": 2.04, "entryType": "measure"}, {"startTime": 7833, "name": "lh:gather:getArtifact:NetworkUserAgent", "duration": 0.06, "entryType": "measure"}, {"startTime": 7833.07, "name": "lh:gather:getArtifact:OptimizedImages", "duration": 0.27, "entryType": "measure"}, {"startTime": 7833.37, "name": "lh:gather:getArtifact:ResponseCompression", "duration": 6.1, "entryType": "measure"}, {"startTime": 7839.48, "name": "lh:gather:getArtifact:Scripts", "duration": 0.16, "entryType": "measure"}, {"startTime": 7839.66, "name": "lh:gather:getArtifact:SourceMaps", "duration": 73.38, "entryType": "measure"}, {"startTime": 7913.06, "name": "lh:gather:getArtifact:Stacks", "duration": 8.22, "entryType": "measure"}, {"startTime": 7913.18, "name": "lh:gather:collectStacks", "duration": 8.08, "entryType": "measure"}, {"startTime": 7921.3, "name": "lh:gather:getArtifact:Stylesheets", "duration": 9.79, "entryType": "measure"}, {"startTime": 7931.12, "name": "lh:gather:getArtifact:TraceElements", "duration": 1329.29, "entryType": "measure"}, {"startTime": 7931.29, "name": "lh:computed:TraceEngineResult", "duration": 1294.88, "entryType": "measure"}, {"startTime": 7931.37, "name": "lh:computed:ProcessedTrace", "duration": 22.18, "entryType": "measure"}, {"startTime": 7955.64, "name": "lh:computed:TraceEngineResult:total", "duration": 1254.45, "entryType": "measure"}, {"startTime": 7955.67, "name": "lh:computed:TraceEngineResult:parse", "duration": 570.32, "entryType": "measure"}, {"startTime": 7956.5, "name": "lh:computed:TraceEngineResult:parse:handleEvent", "duration": 99.83, "entryType": "measure"}, {"startTime": 8056.37, "name": "lh:computed:TraceEngineResult:parse:Meta:finalize", "duration": 9.36, "entryType": "measure"}, {"startTime": 8065.89, "name": "lh:computed:TraceEngineResult:parse:AnimationFrames:finalize", "duration": 15.32, "entryType": "measure"}, {"startTime": 8081.27, "name": "lh:computed:TraceEngineResult:parse:Animations:finalize", "duration": 14.42, "entryType": "measure"}, {"startTime": 8095.73, "name": "lh:computed:TraceEngineResult:parse:Samples:finalize", "duration": 14.77, "entryType": "measure"}, {"startTime": 8110.55, "name": "lh:computed:TraceEngineResult:parse:AuctionWorklets:finalize", "duration": 15.12, "entryType": "measure"}, {"startTime": 8125.73, "name": "lh:computed:TraceEngineResult:parse:NetworkRequests:finalize", "duration": 19.96, "entryType": "measure"}, {"startTime": 8145.74, "name": "lh:computed:TraceEngineResult:parse:<PERSON><PERSON>er:finalize", "duration": 28.3, "entryType": "measure"}, {"startTime": 8174.14, "name": "lh:computed:TraceEngineResult:parse:Flows:finalize", "duration": 16.6, "entryType": "measure"}, {"startTime": 8190.78, "name": "lh:computed:TraceEngineResult:parse:AsyncJSCalls:finalize", "duration": 13.16, "entryType": "measure"}, {"startTime": 8204.04, "name": "lh:computed:TraceEngineResult:parse:DOMStats:finalize", "duration": 12.93, "entryType": "measure"}, {"startTime": 8217.02, "name": "lh:computed:TraceEngineResult:parse:UserTimings:finalize", "duration": 15.67, "entryType": "measure"}, {"startTime": 8232.74, "name": "lh:computed:TraceEngineResult:parse:ExtensionTraceData:finalize", "duration": 15.6, "entryType": "measure"}, {"startTime": 8248.38, "name": "lh:computed:TraceEngineResult:parse:LayerTree:finalize", "duration": 15.16, "entryType": "measure"}, {"startTime": 8263.58, "name": "lh:computed:TraceEngineResult:parse:Frames:finalize", "duration": 27.98, "entryType": "measure"}, {"startTime": 8291.58, "name": "lh:computed:TraceEngineResult:parse:GPU:finalize", "duration": 1.9, "entryType": "measure"}, {"startTime": 8293.51, "name": "lh:computed:TraceEngineResult:parse:ImagePainting:finalize", "duration": 15.14, "entryType": "measure"}, {"startTime": 8308.7, "name": "lh:computed:TraceEngineResult:parse:Initiators:finalize", "duration": 16.01, "entryType": "measure"}, {"startTime": 8324.74, "name": "lh:computed:TraceEngineResult:parse:Invalidations:finalize", "duration": 14.62, "entryType": "measure"}, {"startTime": 8339.41, "name": "lh:computed:TraceEngineResult:parse:PageLoadMetrics:finalize", "duration": 16.02, "entryType": "measure"}, {"startTime": 8355.46, "name": "lh:computed:TraceEngineResult:parse:LargestImagePaint:finalize", "duration": 14.57, "entryType": "measure"}, {"startTime": 8370.07, "name": "lh:computed:TraceEngineResult:parse:LargestTextPaint:finalize", "duration": 14.82, "entryType": "measure"}, {"startTime": 8384.93, "name": "lh:computed:TraceEngineResult:parse:Screenshots:finalize", "duration": 17.39, "entryType": "measure"}, {"startTime": 8402.38, "name": "lh:computed:TraceEngineResult:parse:LayoutShifts:finalize", "duration": 14.37, "entryType": "measure"}, {"startTime": 8416.84, "name": "lh:computed:TraceEngineResult:parse:Memory:finalize", "duration": 13.64, "entryType": "measure"}, {"startTime": 8430.52, "name": "lh:computed:TraceEngineResult:parse:PageFrames:finalize", "duration": 15.13, "entryType": "measure"}, {"startTime": 8445.7, "name": "lh:computed:TraceEngineResult:parse:Scripts:finalize", "duration": 15.89, "entryType": "measure"}, {"startTime": 8461.63, "name": "lh:computed:TraceEngineResult:parse:SelectorStats:finalize", "duration": 14.42, "entryType": "measure"}, {"startTime": 8476.09, "name": "lh:computed:TraceEngineResult:parse:UserInteractions:finalize", "duration": 15.87, "entryType": "measure"}, {"startTime": 8492, "name": "lh:computed:TraceEngineResult:parse:Warnings:finalize", "duration": 15.41, "entryType": "measure"}, {"startTime": 8507.45, "name": "lh:computed:TraceEngineResult:parse:Workers:finalize", "duration": 15.1, "entryType": "measure"}, {"startTime": 8522.67, "name": "lh:computed:TraceEngineResult:parse:clone", "duration": 3.26, "entryType": "measure"}, {"startTime": 8525.99, "name": "lh:computed:TraceEngineResult:insights", "duration": 684.08, "entryType": "measure"}, {"startTime": 8526.2, "name": "lh:computed:TraceEngineResult:insights:createLanternContext", "duration": 30.28, "entryType": "measure"}, {"startTime": 8556.66, "name": "lh:computed:TraceEngineResult:insights:CLSCulprits", "duration": 0.77, "entryType": "measure"}, {"startTime": 8557.45, "name": "lh:computed:TraceEngineResult:insights:<PERSON>ache", "duration": 0.65, "entryType": "measure"}, {"startTime": 8558.11, "name": "lh:computed:TraceEngineResult:insights:DOMSize", "duration": 0.42, "entryType": "measure"}, {"startTime": 8558.55, "name": "lh:computed:TraceEngineResult:insights:DocumentLatency", "duration": 0.35, "entryType": "measure"}, {"startTime": 8558.92, "name": "lh:computed:TraceEngineResult:insights:DuplicatedJavaScript", "duration": 1.55, "entryType": "measure"}, {"startTime": 8560.49, "name": "lh:computed:TraceEngineResult:insights:FontDisplay", "duration": 0.18, "entryType": "measure"}, {"startTime": 8560.67, "name": "lh:computed:TraceEngineResult:insights:ForcedReflow", "duration": 0.68, "entryType": "measure"}, {"startTime": 8561.37, "name": "lh:computed:TraceEngineResult:insights:ImageDelivery", "duration": 0.34, "entryType": "measure"}, {"startTime": 8561.73, "name": "lh:computed:TraceEngineResult:insights:InteractionToNextPaint", "duration": 0.1, "entryType": "measure"}, {"startTime": 8561.84, "name": "lh:computed:TraceEngineResult:insights:LCPDiscovery", "duration": 0.17, "entryType": "measure"}, {"startTime": 8562.03, "name": "lh:computed:TraceEngineResult:insights:LCPPhases", "duration": 0.22, "entryType": "measure"}, {"startTime": 8562.26, "name": "lh:computed:TraceEngineResult:insights:LegacyJavaScript", "duration": 631.99, "entryType": "measure"}, {"startTime": 9194.29, "name": "lh:computed:TraceEngineResult:insights:ModernHTTP", "duration": 1.88, "entryType": "measure"}, {"startTime": 9196.19, "name": "lh:computed:TraceEngineResult:insights:NetworkDependencyTree", "duration": 0.68, "entryType": "measure"}, {"startTime": 9196.89, "name": "lh:computed:TraceEngineResult:insights:RenderBlocking", "duration": 0.3, "entryType": "measure"}, {"startTime": 9197.24, "name": "lh:computed:TraceEngineResult:insights:SlowCSSSelector", "duration": 0.18, "entryType": "measure"}, {"startTime": 9197.49, "name": "lh:computed:TraceEngineResult:insights:ThirdParties", "duration": 11.37, "entryType": "measure"}, {"startTime": 9208.89, "name": "lh:computed:TraceEngineResult:insights:Viewport", "duration": 0.32, "entryType": "measure"}, {"startTime": 9232.95, "name": "lh:computed:ProcessedNavigation", "duration": 0.69, "entryType": "measure"}, {"startTime": 9233.72, "name": "lh:computed:CumulativeLayoutShift", "duration": 7.39, "entryType": "measure"}, {"startTime": 9242.01, "name": "lh:computed:Responsiveness", "duration": 0.12, "entryType": "measure"}, {"startTime": 9260.45, "name": "lh:gather:getArtifact:ViewportDimensions", "duration": 0.79, "entryType": "measure"}, {"startTime": 9261.25, "name": "lh:gather:getArtifact:FullPageScreenshot", "duration": 1256.7, "entryType": "measure"}, {"startTime": 10518, "name": "lh:gather:getArtifact:BFCacheFailures", "duration": 490.26, "entryType": "measure"}, {"startTime": 11037.93, "name": "lh:runner:audit", "duration": 1048.42, "entryType": "measure"}, {"startTime": 11038.08, "name": "lh:runner:auditing", "duration": 1047.85, "entryType": "measure"}, {"startTime": 11039.58, "name": "lh:audit:viewport", "duration": 1.44, "entryType": "measure"}, {"startTime": 11039.89, "name": "lh:computed:ViewportMeta", "duration": 0.41, "entryType": "measure"}, {"startTime": 11041.25, "name": "lh:audit:first-contentful-paint", "duration": 10.3, "entryType": "measure"}, {"startTime": 11041.57, "name": "lh:computed:First<PERSON><PERSON>ntful<PERSON><PERSON>t", "duration": 8.68, "entryType": "measure"}, {"startTime": 11041.77, "name": "lh:computed:Lantern<PERSON><PERSON>t<PERSON><PERSON>ntful<PERSON><PERSON>t", "duration": 8.46, "entryType": "measure"}, {"startTime": 11041.88, "name": "lh:computed:PageDependencyGraph", "duration": 7.17, "entryType": "measure"}, {"startTime": 11049.08, "name": "lh:computed:LoadSimulator", "duration": 0.45, "entryType": "measure"}, {"startTime": 11049.14, "name": "lh:computed:NetworkAnalysis", "duration": 0.33, "entryType": "measure"}, {"startTime": 11051.75, "name": "lh:audit:largest-contentful-paint", "duration": 3.82, "entryType": "measure"}, {"startTime": 11052.11, "name": "lh:computed:Largest<PERSON><PERSON>ntful<PERSON><PERSON>t", "duration": 2.48, "entryType": "measure"}, {"startTime": 11052.17, "name": "lh:computed:LanternLargestContentfulPaint", "duration": 2.39, "entryType": "measure"}, {"startTime": 11055.79, "name": "lh:audit:first-meaningful-paint", "duration": 0.73, "entryType": "measure"}, {"startTime": 11056.71, "name": "lh:audit:speed-index", "duration": 135.58, "entryType": "measure"}, {"startTime": 11057, "name": "lh:computed:SpeedIndex", "duration": 134.51, "entryType": "measure"}, {"startTime": 11057.06, "name": "lh:computed:LanternSpeedIndex", "duration": 134.43, "entryType": "measure"}, {"startTime": 11057.1, "name": "lh:computed:Speedline", "duration": 131.83, "entryType": "measure"}, {"startTime": 11192.32, "name": "lh:audit:screenshot-thumbnails", "duration": 0.41, "entryType": "measure"}, {"startTime": 11192.75, "name": "lh:audit:final-screenshot", "duration": 0.88, "entryType": "measure"}, {"startTime": 11192.83, "name": "lh:computed:Screenshots", "duration": 0.76, "entryType": "measure"}, {"startTime": 11193.87, "name": "lh:audit:total-blocking-time", "duration": 13.8, "entryType": "measure"}, {"startTime": 11194.15, "name": "lh:computed:TotalBlockingTime", "duration": 12.51, "entryType": "measure"}, {"startTime": 11194.21, "name": "lh:computed:LanternTotalBlockingTime", "duration": 12.45, "entryType": "measure"}, {"startTime": 11194.27, "name": "lh:computed:LanternInteractive", "duration": 10.68, "entryType": "measure"}, {"startTime": 11207.89, "name": "lh:audit:max-potential-fid", "duration": 3.47, "entryType": "measure"}, {"startTime": 11208.22, "name": "lh:computed:MaxPotentialFID", "duration": 1.86, "entryType": "measure"}, {"startTime": 11208.27, "name": "lh:computed:LanternMaxPotentialFID", "duration": 1.79, "entryType": "measure"}, {"startTime": 11211.63, "name": "lh:audit:cumulative-layout-shift", "duration": 0.85, "entryType": "measure"}, {"startTime": 11212.72, "name": "lh:audit:server-response-time", "duration": 1.27, "entryType": "measure"}, {"startTime": 11214.22, "name": "lh:audit:interactive", "duration": 1.06, "entryType": "measure"}, {"startTime": 11214.52, "name": "lh:computed:Interactive", "duration": 0.07, "entryType": "measure"}, {"startTime": 11215.47, "name": "lh:audit:user-timings", "duration": 5.25, "entryType": "measure"}, {"startTime": 11215.71, "name": "lh:computed:UserTimings", "duration": 1.09, "entryType": "measure"}, {"startTime": 11221, "name": "lh:audit:critical-request-chains", "duration": 1.9, "entryType": "measure"}, {"startTime": 11221.6, "name": "lh:computed:CriticalRequest<PERSON><PERSON>ns", "duration": 0.69, "entryType": "measure"}, {"startTime": 11223.05, "name": "lh:audit:redirects", "duration": 2.05, "entryType": "measure"}, {"startTime": 11225.28, "name": "lh:audit:mainthread-work-breakdown", "duration": 12.97, "entryType": "measure"}, {"startTime": 11225.63, "name": "lh:computed:MainThreadTasks", "duration": 10.81, "entryType": "measure"}, {"startTime": 11238.45, "name": "lh:audit:bootup-time", "duration": 11.69, "entryType": "measure"}, {"startTime": 11242.54, "name": "lh:computed:TBTImpactTasks", "duration": 5.48, "entryType": "measure"}, {"startTime": 11250.29, "name": "lh:audit:uses-rel-preconnect", "duration": 1.47, "entryType": "measure"}, {"startTime": 11252.09, "name": "lh:audit:font-display", "duration": 2.36, "entryType": "measure"}, {"startTime": 11254.49, "name": "lh:audit:diagnostics", "duration": 0.96, "entryType": "measure"}, {"startTime": 11255.46, "name": "lh:audit:network-requests", "duration": 1.88, "entryType": "measure"}, {"startTime": 11255.61, "name": "lh:computed:EntityClassification", "duration": 1.37, "entryType": "measure"}, {"startTime": 11257.51, "name": "lh:audit:network-rtt", "duration": 0.81, "entryType": "measure"}, {"startTime": 11258.48, "name": "lh:audit:network-server-latency", "duration": 0.72, "entryType": "measure"}, {"startTime": 11259.22, "name": "lh:audit:main-thread-tasks", "duration": 0.26, "entryType": "measure"}, {"startTime": 11259.48, "name": "lh:audit:metrics", "duration": 2.06, "entryType": "measure"}, {"startTime": 11259.58, "name": "lh:computed:<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "duration": 1.83, "entryType": "measure"}, {"startTime": 11259.82, "name": "lh:computed:FirstContentfulPaintAllFrames", "duration": 0.05, "entryType": "measure"}, {"startTime": 11259.91, "name": "lh:computed:LargestContentfulPaintAllFrames", "duration": 0.08, "entryType": "measure"}, {"startTime": 11260.04, "name": "lh:computed:LCPBreakdown", "duration": 1.05, "entryType": "measure"}, {"startTime": 11260.12, "name": "lh:computed:TimeToFirstByte", "duration": 0.18, "entryType": "measure"}, {"startTime": 11260.31, "name": "lh:computed:LCPImageRecord", "duration": 0.76, "entryType": "measure"}, {"startTime": 11261.56, "name": "lh:audit:resource-summary", "duration": 1.06, "entryType": "measure"}, {"startTime": 11261.66, "name": "lh:computed:ResourceSummary", "duration": 0.32, "entryType": "measure"}, {"startTime": 11262.85, "name": "lh:audit:third-party-summary", "duration": 2.31, "entryType": "measure"}, {"startTime": 11265.34, "name": "lh:audit:third-party-facades", "duration": 1.52, "entryType": "measure"}, {"startTime": 11267, "name": "lh:audit:largest-contentful-paint-element", "duration": 1.22, "entryType": "measure"}, {"startTime": 11268.45, "name": "lh:audit:lcp-lazy-loaded", "duration": 0.8, "entryType": "measure"}, {"startTime": 11269.47, "name": "lh:audit:layout-shifts", "duration": 1.1, "entryType": "measure"}, {"startTime": 11270.73, "name": "lh:audit:long-tasks", "duration": 6.37, "entryType": "measure"}, {"startTime": 11277.43, "name": "lh:audit:non-composited-animations", "duration": 1.81, "entryType": "measure"}, {"startTime": 11279.49, "name": "lh:audit:unsized-images", "duration": 0.88, "entryType": "measure"}, {"startTime": 11280.54, "name": "lh:audit:prioritize-lcp-image", "duration": 0.58, "entryType": "measure"}, {"startTime": 11281.14, "name": "lh:audit:script-treemap-data", "duration": 31.42, "entryType": "measure"}, {"startTime": 11281.37, "name": "lh:computed:<PERSON><PERSON><PERSON><PERSON>", "duration": 0.09, "entryType": "measure"}, {"startTime": 11281.47, "name": "lh:computed:ModuleDuplication", "duration": 0.16, "entryType": "measure"}, {"startTime": 11281.66, "name": "lh:computed:UnusedJavascriptSummary", "duration": 0.2, "entryType": "measure"}, {"startTime": 11281.92, "name": "lh:computed:UnusedJavascriptSummary", "duration": 0.06, "entryType": "measure"}, {"startTime": 11282.01, "name": "lh:computed:UnusedJavascriptSummary", "duration": 0.02, "entryType": "measure"}, {"startTime": 11282.06, "name": "lh:computed:UnusedJavascriptSummary", "duration": 0.06, "entryType": "measure"}, {"startTime": 11282.15, "name": "lh:computed:UnusedJavascriptSummary", "duration": 0.06, "entryType": "measure"}, {"startTime": 11282.25, "name": "lh:computed:UnusedJavascriptSummary", "duration": 0.07, "entryType": "measure"}, {"startTime": 11282.36, "name": "lh:computed:UnusedJavascriptSummary", "duration": 0.06, "entryType": "measure"}, {"startTime": 11282.46, "name": "lh:computed:UnusedJavascriptSummary", "duration": 0.16, "entryType": "measure"}, {"startTime": 11282.67, "name": "lh:computed:UnusedJavascriptSummary", "duration": 0.08, "entryType": "measure"}, {"startTime": 11282.79, "name": "lh:computed:UnusedJavascriptSummary", "duration": 0.72, "entryType": "measure"}, {"startTime": 11283.6, "name": "lh:computed:UnusedJavascriptSummary", "duration": 0.09, "entryType": "measure"}, {"startTime": 11283.75, "name": "lh:computed:UnusedJavascriptSummary", "duration": 0.1, "entryType": "measure"}, {"startTime": 11283.91, "name": "lh:computed:UnusedJavascriptSummary", "duration": 0.06, "entryType": "measure"}, {"startTime": 11284.04, "name": "lh:computed:UnusedJavascriptSummary", "duration": 0.09, "entryType": "measure"}, {"startTime": 11284.21, "name": "lh:computed:UnusedJavascriptSummary", "duration": 0.08, "entryType": "measure"}, {"startTime": 11284.53, "name": "lh:computed:UnusedJavascriptSummary", "duration": 0.16, "entryType": "measure"}, {"startTime": 11284.8, "name": "lh:computed:UnusedJavascriptSummary", "duration": 0.1, "entryType": "measure"}, {"startTime": 11285, "name": "lh:computed:UnusedJavascriptSummary", "duration": 4.3, "entryType": "measure"}, {"startTime": 11289.44, "name": "lh:computed:UnusedJavascriptSummary", "duration": 0.99, "entryType": "measure"}, {"startTime": 11290.51, "name": "lh:computed:UnusedJavascriptSummary", "duration": 0.08, "entryType": "measure"}, {"startTime": 11290.68, "name": "lh:computed:UnusedJavascriptSummary", "duration": 3.08, "entryType": "measure"}, {"startTime": 11293.86, "name": "lh:computed:UnusedJavascriptSummary", "duration": 0.12, "entryType": "measure"}, {"startTime": 11294.09, "name": "lh:computed:UnusedJavascriptSummary", "duration": 0.09, "entryType": "measure"}, {"startTime": 11294.28, "name": "lh:computed:UnusedJavascriptSummary", "duration": 2.95, "entryType": "measure"}, {"startTime": 11297.37, "name": "lh:computed:UnusedJavascriptSummary", "duration": 0.21, "entryType": "measure"}, {"startTime": 11297.72, "name": "lh:computed:UnusedJavascriptSummary", "duration": 0.12, "entryType": "measure"}, {"startTime": 11297.96, "name": "lh:computed:UnusedJavascriptSummary", "duration": 0.83, "entryType": "measure"}, {"startTime": 11299, "name": "lh:computed:UnusedJavascriptSummary", "duration": 4.57, "entryType": "measure"}, {"startTime": 11303.79, "name": "lh:computed:UnusedJavascriptSummary", "duration": 3.72, "entryType": "measure"}, {"startTime": 11307.63, "name": "lh:computed:UnusedJavascriptSummary", "duration": 0.1, "entryType": "measure"}, {"startTime": 11307.83, "name": "lh:computed:UnusedJavascriptSummary", "duration": 3.5, "entryType": "measure"}, {"startTime": 11311.43, "name": "lh:computed:UnusedJavascriptSummary", "duration": 0.1, "entryType": "measure"}, {"startTime": 11312.28, "name": "lh:computed:UnusedJavascriptSummary", "duration": 0.17, "entryType": "measure"}, {"startTime": 11312.85, "name": "lh:audit:uses-long-cache-ttl", "duration": 1.95, "entryType": "measure"}, {"startTime": 11315, "name": "lh:audit:total-byte-weight", "duration": 1.04, "entryType": "measure"}, {"startTime": 11316.2, "name": "lh:audit:offscreen-images", "duration": 2.47, "entryType": "measure"}, {"startTime": 11318.89, "name": "lh:audit:render-blocking-resources", "duration": 5.67, "entryType": "measure"}, {"startTime": 11319.29, "name": "lh:computed:UnusedCSS", "duration": 4.51, "entryType": "measure"}, {"startTime": 11323.83, "name": "lh:computed:NavigationInsights", "duration": 0.08, "entryType": "measure"}, {"startTime": 11323.95, "name": "lh:computed:First<PERSON><PERSON>ntful<PERSON><PERSON>t", "duration": 0.04, "entryType": "measure"}, {"startTime": 11324.7, "name": "lh:audit:unminified-css", "duration": 8.52, "entryType": "measure"}, {"startTime": 11333.42, "name": "lh:audit:unminified-javascript", "duration": 84.16, "entryType": "measure"}, {"startTime": 11417.77, "name": "lh:audit:unused-css-rules", "duration": 2.37, "entryType": "measure"}, {"startTime": 11420.55, "name": "lh:audit:unused-javascript", "duration": 3.37, "entryType": "measure"}, {"startTime": 11424.09, "name": "lh:audit:modern-image-formats", "duration": 1.7, "entryType": "measure"}, {"startTime": 11425.94, "name": "lh:audit:uses-optimized-images", "duration": 1.6, "entryType": "measure"}, {"startTime": 11427.69, "name": "lh:audit:uses-text-compression", "duration": 1.52, "entryType": "measure"}, {"startTime": 11429.35, "name": "lh:audit:uses-responsive-images", "duration": 1.85, "entryType": "measure"}, {"startTime": 11429.61, "name": "lh:computed:ImageRecords", "duration": 0.2, "entryType": "measure"}, {"startTime": 11431.42, "name": "lh:audit:efficient-animated-content", "duration": 1.46, "entryType": "measure"}, {"startTime": 11433.02, "name": "lh:audit:duplicated-javascript", "duration": 1.49, "entryType": "measure"}, {"startTime": 11434.68, "name": "lh:audit:legacy-javascript", "duration": 621.11, "entryType": "measure"}, {"startTime": 12056, "name": "lh:audit:dom-size", "duration": 1.49, "entryType": "measure"}, {"startTime": 12057.69, "name": "lh:audit:no-document-write", "duration": 0.73, "entryType": "measure"}, {"startTime": 12058.55, "name": "lh:audit:uses-http2", "duration": 1.76, "entryType": "measure"}, {"startTime": 12060.49, "name": "lh:audit:uses-passive-event-listeners", "duration": 0.64, "entryType": "measure"}, {"startTime": 12061.31, "name": "lh:audit:bf-cache", "duration": 0.98, "entryType": "measure"}, {"startTime": 12062.45, "name": "lh:audit:cache-insight", "duration": 0.83, "entryType": "measure"}, {"startTime": 12063.45, "name": "lh:audit:cls-culprits-insight", "duration": 0.74, "entryType": "measure"}, {"startTime": 12064.35, "name": "lh:audit:document-latency-insight", "duration": 0.57, "entryType": "measure"}, {"startTime": 12065.08, "name": "lh:audit:dom-size-insight", "duration": 0.83, "entryType": "measure"}, {"startTime": 12066.07, "name": "lh:audit:duplicated-javascript-insight", "duration": 1.38, "entryType": "measure"}, {"startTime": 12067.61, "name": "lh:audit:font-display-insight", "duration": 0.7, "entryType": "measure"}, {"startTime": 12068.5, "name": "lh:audit:forced-reflow-insight", "duration": 0.96, "entryType": "measure"}, {"startTime": 12069.76, "name": "lh:audit:image-delivery-insight", "duration": 0.72, "entryType": "measure"}, {"startTime": 12070.71, "name": "lh:audit:interaction-to-next-paint-insight", "duration": 0.67, "entryType": "measure"}, {"startTime": 12071.55, "name": "lh:audit:lcp-discovery-insight", "duration": 0.58, "entryType": "measure"}, {"startTime": 12072.3, "name": "lh:audit:lcp-phases-insight", "duration": 0.86, "entryType": "measure"}, {"startTime": 12073.33, "name": "lh:audit:legacy-javascript-insight", "duration": 1.12, "entryType": "measure"}, {"startTime": 12074.61, "name": "lh:audit:modern-http-insight", "duration": 0.63, "entryType": "measure"}, {"startTime": 12075.4, "name": "lh:audit:network-dependency-tree-insight", "duration": 0.64, "entryType": "measure"}, {"startTime": 12076.21, "name": "lh:audit:render-blocking-insight", "duration": 0.72, "entryType": "measure"}, {"startTime": 12077.1, "name": "lh:audit:third-parties-insight", "duration": 8.02, "entryType": "measure"}, {"startTime": 12085.29, "name": "lh:audit:viewport-insight", "duration": 0.62, "entryType": "measure"}, {"startTime": 12085.93, "name": "lh:runner:generate", "duration": 0.41, "entryType": "measure"}], "total": 9903.44}, "i18n": {"rendererFormattedStrings": {"calculatorLink": "See calculator.", "collapseView": "Collapse view", "crcInitialNavigation": "Initial Navigation", "crcLongestDurationLabel": "Maximum critical path latency:", "dropdownCopyJSON": "Copy JSON", "dropdownDarkTheme": "Toggle Dark Theme", "dropdownPrintExpanded": "Print Expanded", "dropdownPrintSummary": "Print Summary", "dropdownSaveGist": "Save as Gist", "dropdownSaveHTML": "Save as HTML", "dropdownSaveJSON": "Save as JSON", "dropdownViewUnthrottledTrace": "View Unthrottled Trace", "dropdownViewer": "Open in Viewer", "errorLabel": "Error!", "errorMissingAuditInfo": "Report error: no audit information", "expandView": "Expand view", "firstPartyChipLabel": "1st party", "footerIssue": "File an issue", "goBackToAudits": "Go back to audits", "hide": "<PERSON>de", "insightsNotice": "Later this year, insights will replace performance audits. [Learn more and provide feedback here](https://github.com/GoogleChrome/lighthouse/discussions/16462).", "labDataTitle": "Lab Data", "lsPerformanceCategoryDescription": "[Lighthouse](https://developers.google.com/web/tools/lighthouse/) analysis of the current page on an emulated mobile network. Values are estimated and may vary.", "manualAuditsGroupTitle": "Additional items to manually check", "notApplicableAuditsGroupTitle": "Not applicable", "openInANewTabTooltip": "Open in a new tab", "opportunityResourceColumnLabel": "Opportunity", "opportunitySavingsColumnLabel": "Estimated Savings", "passedAuditsGroupTitle": "Passed audits", "runtimeAnalysisWindow": "Initial page load", "runtimeAnalysisWindowSnapshot": "Point-in-time snapshot", "runtimeAnalysisWindowTimespan": "User interactions timespan", "runtimeCustom": "Custom throttling", "runtimeDesktopEmulation": "Emulated Desktop", "runtimeMobileEmulation": "Emulated Moto G Power", "runtimeNoEmulation": "No emulation", "runtimeSettingsAxeVersion": "Axe version", "runtimeSettingsBenchmark": "Unthrottled CPU/Memory Power", "runtimeSettingsCPUThrottling": "CPU throttling", "runtimeSettingsDevice": "<PERSON><PERSON>", "runtimeSettingsNetworkThrottling": "Network throttling", "runtimeSettingsScreenEmulation": "Screen emulation", "runtimeSettingsUANetwork": "User agent (network)", "runtimeSingleLoad": "Single page session", "runtimeSingleLoadTooltip": "This data is taken from a single page session, as opposed to field data summarizing many sessions.", "runtimeSlow4g": "Slow 4G throttling", "runtimeUnknown": "Unknown", "show": "Show", "showRelevantAudits": "Show audits relevant to:", "snippetCollapseButtonLabel": "Collapse snippet", "snippetExpandButtonLabel": "Expand snippet", "thirdPartyResourcesLabel": "Show 3rd-party resources", "throttlingProvided": "Provided by environment", "toplevelWarningsMessage": "There were issues affecting this run of Lighthouse:", "tryInsights": "Try insights", "unattributable": "Unattributable", "varianceDisclaimer": "Values are estimated and may vary. The [performance score is calculated](https://developer.chrome.com/docs/lighthouse/performance/performance-scoring/) directly from these metrics.", "viewTraceLabel": "View Trace", "viewTreemapLabel": "View Treemap", "warningAuditsGroupTitle": "Passed audits but with warnings", "warningHeader": "Warnings: "}, "icuMessagePaths": {"core/audits/viewport.js | title": ["audits.viewport.title"], "core/audits/viewport.js | description": ["audits.viewport.description"], "core/lib/i18n/i18n.js | firstContentfulPaintMetric": ["audits[first-contentful-paint].title"], "core/audits/metrics/first-contentful-paint.js | description": ["audits[first-contentful-paint].description"], "core/lib/i18n/i18n.js | seconds": [{"values": {"timeInMs": 1062.222}, "path": "audits[first-contentful-paint].displayValue"}, {"values": {"timeInMs": 5585.222}, "path": "audits[largest-contentful-paint].displayValue"}, {"values": {"timeInMs": 2549.47733092971}, "path": "audits[speed-index].displayValue"}, {"values": {"timeInMs": 6813.************}, "path": "audits.interactive.displayValue"}, {"values": {"timeInMs": 3670.************}, "path": "audits[mainthread-work-breakdown].displayValue"}, {"values": {"timeInMs": 3054.************}, "path": "audits[bootup-time].displayValue"}], "core/lib/i18n/i18n.js | largestContentfulPaintMetric": ["audits[largest-contentful-paint].title"], "core/audits/metrics/largest-contentful-paint.js | description": ["audits[largest-contentful-paint].description"], "core/lib/i18n/i18n.js | firstMeaningfulPaintMetric": ["audits[first-meaningful-paint].title"], "core/audits/metrics/first-meaningful-paint.js | description": ["audits[first-meaningful-paint].description"], "core/lib/i18n/i18n.js | speedIndexMetric": ["audits[speed-index].title"], "core/audits/metrics/speed-index.js | description": ["audits[speed-index].description"], "core/lib/i18n/i18n.js | totalBlockingTimeMetric": ["audits[total-blocking-time].title"], "core/audits/metrics/total-blocking-time.js | description": ["audits[total-blocking-time].description"], "core/lib/i18n/i18n.js | ms": [{"values": {"timeInMs": 837}, "path": "audits[total-blocking-time].displayValue"}, {"values": {"timeInMs": 371}, "path": "audits[max-potential-fid].displayValue"}, {"values": {"timeInMs": 0.141}, "path": "audits[network-rtt].displayValue"}, {"values": {"timeInMs": 6.110999999999999}, "path": "audits[network-server-latency].displayValue"}, {"values": {"timeInMs": 5585.222}, "path": "audits[largest-contentful-paint-element].displayValue"}], "core/lib/i18n/i18n.js | maxPotentialFIDMetric": ["audits[max-potential-fid].title"], "core/audits/metrics/max-potential-fid.js | description": ["audits[max-potential-fid].description"], "core/lib/i18n/i18n.js | cumulativeLayoutShiftMetric": ["audits[cumulative-layout-shift].title"], "core/audits/metrics/cumulative-layout-shift.js | description": ["audits[cumulative-layout-shift].description"], "core/audits/server-response-time.js | title": ["audits[server-response-time].title"], "core/audits/server-response-time.js | description": ["audits[server-response-time].description"], "core/audits/server-response-time.js | displayValue": [{"values": {"timeInMs": 187.697}, "path": "audits[server-response-time].displayValue"}], "core/lib/i18n/i18n.js | columnURL": ["audits[server-response-time].details.headings[0].label", "audits[bootup-time].details.headings[0].label", "audits[network-rtt].details.headings[0].label", "audits[network-server-latency].details.headings[0].label", "audits[long-tasks].details.headings[0].label", "audits[total-byte-weight].details.headings[0].label", "audits[unminified-javascript].details.headings[0].label", "audits[unused-css-rules].details.headings[0].label", "audits[unused-javascript].details.headings[0].label", "audits[legacy-javascript].details.headings[0].label", "audits[legacy-javascript-insight].details.headings[0].label"], "core/lib/i18n/i18n.js | columnTimeSpent": ["audits[server-response-time].details.headings[1].label", "audits[mainthread-work-breakdown].details.headings[1].label", "audits[network-rtt].details.headings[1].label", "audits[network-server-latency].details.headings[1].label"], "core/lib/i18n/i18n.js | interactiveMetric": ["audits.interactive.title"], "core/audits/metrics/interactive.js | description": ["audits.interactive.description"], "core/audits/user-timings.js | title": ["audits[user-timings].title"], "core/audits/user-timings.js | description": ["audits[user-timings].description"], "core/audits/critical-request-chains.js | title": ["audits[critical-request-chains].title"], "core/audits/critical-request-chains.js | description": ["audits[critical-request-chains].description"], "core/audits/redirects.js | title": ["audits.redirects.title"], "core/audits/redirects.js | description": ["audits.redirects.description"], "core/audits/mainthread-work-breakdown.js | failureTitle": ["audits[mainthread-work-breakdown].title"], "core/audits/mainthread-work-breakdown.js | description": ["audits[mainthread-work-breakdown].description"], "core/audits/mainthread-work-breakdown.js | columnCategory": ["audits[mainthread-work-breakdown].details.headings[0].label"], "core/audits/bootup-time.js | failureTitle": ["audits[bootup-time].title"], "core/audits/bootup-time.js | description": ["audits[bootup-time].description"], "core/audits/bootup-time.js | columnTotal": ["audits[bootup-time].details.headings[1].label"], "core/audits/bootup-time.js | columnScriptEval": ["audits[bootup-time].details.headings[2].label"], "core/audits/bootup-time.js | columnScriptParse": ["audits[bootup-time].details.headings[3].label"], "core/audits/uses-rel-preconnect.js | title": ["audits[uses-rel-preconnect].title"], "core/audits/uses-rel-preconnect.js | description": ["audits[uses-rel-preconnect].description"], "core/audits/uses-rel-preconnect.js | unusedWarning": [{"values": {"securityOrigin": "https://fonts.googleapis.com"}, "path": "audits[uses-rel-preconnect].warnings[0]"}, {"values": {"securityOrigin": "https://fonts.gstatic.com"}, "path": "audits[uses-rel-preconnect].warnings[1]"}], "core/audits/font-display.js | title": ["audits[font-display].title"], "core/audits/font-display.js | description": ["audits[font-display].description"], "core/audits/network-rtt.js | title": ["audits[network-rtt].title"], "core/audits/network-rtt.js | description": ["audits[network-rtt].description"], "core/audits/network-server-latency.js | title": ["audits[network-server-latency].title"], "core/audits/network-server-latency.js | description": ["audits[network-server-latency].description"], "core/lib/i18n/i18n.js | columnResourceType": ["audits[resource-summary].details.headings[0].label"], "core/lib/i18n/i18n.js | columnRequests": ["audits[resource-summary].details.headings[1].label"], "core/lib/i18n/i18n.js | columnTransferSize": ["audits[resource-summary].details.headings[2].label", "audits[total-byte-weight].details.headings[1].label", "audits[unminified-javascript].details.headings[1].label", "audits[unused-css-rules].details.headings[1].label", "audits[unused-javascript].details.headings[1].label"], "core/lib/i18n/i18n.js | total": ["audits[resource-summary].details.items[0].label"], "core/lib/i18n/i18n.js | scriptResourceType": ["audits[resource-summary].details.items[1].label"], "core/lib/i18n/i18n.js | fontResourceType": ["audits[resource-summary].details.items[2].label"], "core/lib/i18n/i18n.js | imageResourceType": ["audits[resource-summary].details.items[3].label"], "core/lib/i18n/i18n.js | documentResourceType": ["audits[resource-summary].details.items[4].label"], "core/lib/i18n/i18n.js | stylesheetResourceType": ["audits[resource-summary].details.items[5].label"], "core/lib/i18n/i18n.js | otherResourceType": ["audits[resource-summary].details.items[6].label"], "core/lib/i18n/i18n.js | mediaResourceType": ["audits[resource-summary].details.items[7].label"], "core/lib/i18n/i18n.js | thirdPartyResourceType": ["audits[resource-summary].details.items[8].label"], "core/audits/third-party-summary.js | title": ["audits[third-party-summary].title"], "core/audits/third-party-summary.js | description": ["audits[third-party-summary].description"], "core/audits/third-party-facades.js | title": ["audits[third-party-facades].title"], "core/audits/third-party-facades.js | description": ["audits[third-party-facades].description"], "core/audits/largest-contentful-paint-element.js | title": ["audits[largest-contentful-paint-element].title"], "core/audits/largest-contentful-paint-element.js | description": ["audits[largest-contentful-paint-element].description"], "core/lib/i18n/i18n.js | columnElement": ["audits[largest-contentful-paint-element].details.items[0].headings[0].label", "audits[non-composited-animations].details.headings[0].label", "audits[dom-size].details.headings[1].label", "audits[dom-size-insight].details.headings[1].label"], "core/audits/largest-contentful-paint-element.js | columnPhase": ["audits[largest-contentful-paint-element].details.items[1].headings[0].label"], "core/audits/largest-contentful-paint-element.js | columnPercentOfLCP": ["audits[largest-contentful-paint-element].details.items[1].headings[1].label"], "core/audits/largest-contentful-paint-element.js | columnTiming": ["audits[largest-contentful-paint-element].details.items[1].headings[2].label"], "core/audits/largest-contentful-paint-element.js | itemTTFB": ["audits[largest-contentful-paint-element].details.items[1].items[0].phase"], "core/audits/largest-contentful-paint-element.js | itemLoadDelay": ["audits[largest-contentful-paint-element].details.items[1].items[1].phase"], "core/audits/largest-contentful-paint-element.js | itemLoadTime": ["audits[largest-contentful-paint-element].details.items[1].items[2].phase"], "core/audits/largest-contentful-paint-element.js | itemRenderDelay": ["audits[largest-contentful-paint-element].details.items[1].items[3].phase"], "core/audits/lcp-lazy-loaded.js | title": ["audits[lcp-lazy-loaded].title"], "core/audits/lcp-lazy-loaded.js | description": ["audits[lcp-lazy-loaded].description"], "core/audits/layout-shifts.js | title": ["audits[layout-shifts].title"], "core/audits/layout-shifts.js | description": ["audits[layout-shifts].description"], "core/audits/long-tasks.js | title": ["audits[long-tasks].title"], "core/audits/long-tasks.js | description": ["audits[long-tasks].description"], "core/audits/long-tasks.js | displayValue": [{"values": {"itemCount": 15}, "path": "audits[long-tasks].displayValue"}], "core/lib/i18n/i18n.js | columnStartTime": ["audits[long-tasks].details.headings[1].label"], "core/lib/i18n/i18n.js | columnDuration": ["audits[long-tasks].details.headings[2].label", "audits[lcp-phases-insight].details.items[0].headings[1].label"], "core/audits/non-composited-animations.js | title": ["audits[non-composited-animations].title"], "core/audits/non-composited-animations.js | description": ["audits[non-composited-animations].description"], "core/audits/non-composited-animations.js | displayValue": [{"values": {"itemCount": 2}, "path": "audits[non-composited-animations].displayValue"}], "core/lib/i18n/i18n.js | columnName": ["audits[non-composited-animations].details.headings[1].label"], "core/audits/non-composited-animations.js | unsupportedCSSProperty": [{"values": {"propertyCount": 1, "properties": "stroke-dashoffset"}, "path": "audits[non-composited-animations].details.items[0].subItems.items[0].failureReason"}, {"values": {"propertyCount": 1, "properties": "stroke-dashoffset"}, "path": "audits[non-composited-animations].details.items[1].subItems.items[0].failureReason"}], "core/audits/unsized-images.js | title": ["audits[unsized-images].title"], "core/audits/unsized-images.js | description": ["audits[unsized-images].description"], "core/audits/prioritize-lcp-image.js | title": ["audits[prioritize-lcp-image].title"], "core/audits/prioritize-lcp-image.js | description": ["audits[prioritize-lcp-image].description"], "core/audits/byte-efficiency/uses-long-cache-ttl.js | title": ["audits[uses-long-cache-ttl].title"], "core/audits/byte-efficiency/uses-long-cache-ttl.js | description": ["audits[uses-long-cache-ttl].description"], "core/audits/byte-efficiency/uses-long-cache-ttl.js | displayValue": [{"values": {"itemCount": 0}, "path": "audits[uses-long-cache-ttl].displayValue"}], "core/audits/byte-efficiency/total-byte-weight.js | title": ["audits[total-byte-weight].title"], "core/audits/byte-efficiency/total-byte-weight.js | description": ["audits[total-byte-weight].description"], "core/audits/byte-efficiency/total-byte-weight.js | displayValue": [{"values": {"totalBytes": 775563}, "path": "audits[total-byte-weight].displayValue"}], "core/audits/byte-efficiency/offscreen-images.js | title": ["audits[offscreen-images].title"], "core/audits/byte-efficiency/offscreen-images.js | description": ["audits[offscreen-images].description"], "core/audits/byte-efficiency/render-blocking-resources.js | title": ["audits[render-blocking-resources].title"], "core/audits/byte-efficiency/render-blocking-resources.js | description": ["audits[render-blocking-resources].description"], "core/audits/byte-efficiency/unminified-css.js | title": ["audits[unminified-css].title"], "core/audits/byte-efficiency/unminified-css.js | description": ["audits[unminified-css].description"], "core/audits/byte-efficiency/unminified-javascript.js | title": ["audits[unminified-javascript].title"], "core/audits/byte-efficiency/unminified-javascript.js | description": ["audits[unminified-javascript].description"], "core/lib/i18n/i18n.js | displayValueByteSavings": [{"values": {"wastedBytes": 177091}, "path": "audits[unminified-javascript].displayValue"}, {"values": {"wastedBytes": 11422}, "path": "audits[unused-css-rules].displayValue"}, {"values": {"wastedBytes": 181297}, "path": "audits[unused-javascript].displayValue"}, {"values": {"wastedBytes": 8390}, "path": "audits[legacy-javascript].displayValue"}, {"values": {"wastedBytes": 8420}, "path": "audits[legacy-javascript-insight].displayValue"}], "core/lib/i18n/i18n.js | columnWastedBytes": ["audits[unminified-javascript].details.headings[2].label", "audits[unused-css-rules].details.headings[2].label", "audits[unused-javascript].details.headings[2].label", "audits[legacy-javascript].details.headings[2].label"], "core/audits/byte-efficiency/unused-css-rules.js | title": ["audits[unused-css-rules].title"], "core/audits/byte-efficiency/unused-css-rules.js | description": ["audits[unused-css-rules].description"], "core/audits/byte-efficiency/unused-javascript.js | title": ["audits[unused-javascript].title"], "core/audits/byte-efficiency/unused-javascript.js | description": ["audits[unused-javascript].description"], "core/audits/byte-efficiency/modern-image-formats.js | title": ["audits[modern-image-formats].title"], "core/audits/byte-efficiency/modern-image-formats.js | description": ["audits[modern-image-formats].description"], "core/audits/byte-efficiency/uses-optimized-images.js | title": ["audits[uses-optimized-images].title"], "core/audits/byte-efficiency/uses-optimized-images.js | description": ["audits[uses-optimized-images].description"], "core/audits/byte-efficiency/uses-text-compression.js | title": ["audits[uses-text-compression].title"], "core/audits/byte-efficiency/uses-text-compression.js | description": ["audits[uses-text-compression].description"], "core/audits/byte-efficiency/uses-responsive-images.js | title": ["audits[uses-responsive-images].title"], "core/audits/byte-efficiency/uses-responsive-images.js | description": ["audits[uses-responsive-images].description"], "core/audits/byte-efficiency/efficient-animated-content.js | title": ["audits[efficient-animated-content].title"], "core/audits/byte-efficiency/efficient-animated-content.js | description": ["audits[efficient-animated-content].description"], "core/audits/byte-efficiency/duplicated-javascript.js | title": ["audits[duplicated-javascript].title"], "core/audits/byte-efficiency/duplicated-javascript.js | description": ["audits[duplicated-javascript].description"], "core/audits/byte-efficiency/legacy-javascript.js | title": ["audits[legacy-javascript].title"], "core/audits/byte-efficiency/legacy-javascript.js | description": ["audits[legacy-javascript].description"], "core/audits/dobetterweb/dom-size.js | title": ["audits[dom-size].title"], "core/audits/dobetterweb/dom-size.js | description": ["audits[dom-size].description"], "core/audits/dobetterweb/dom-size.js | displayValue": [{"values": {"itemCount": 385}, "path": "audits[dom-size].displayValue"}], "core/audits/dobetterweb/dom-size.js | columnStatistic": ["audits[dom-size].details.headings[0].label"], "core/audits/dobetterweb/dom-size.js | columnValue": ["audits[dom-size].details.headings[2].label"], "core/audits/dobetterweb/dom-size.js | statisticDOMElements": ["audits[dom-size].details.items[0].statistic"], "core/audits/dobetterweb/dom-size.js | statisticDOMDepth": ["audits[dom-size].details.items[1].statistic"], "core/audits/dobetterweb/dom-size.js | statisticDOMWidth": ["audits[dom-size].details.items[2].statistic"], "core/audits/dobetterweb/no-document-write.js | title": ["audits[no-document-write].title"], "core/audits/dobetterweb/no-document-write.js | description": ["audits[no-document-write].description"], "core/audits/dobetterweb/uses-http2.js | title": ["audits[uses-http2].title"], "core/audits/dobetterweb/uses-http2.js | description": ["audits[uses-http2].description"], "core/audits/dobetterweb/uses-passive-event-listeners.js | title": ["audits[uses-passive-event-listeners].title"], "core/audits/dobetterweb/uses-passive-event-listeners.js | description": ["audits[uses-passive-event-listeners].description"], "core/audits/bf-cache.js | failureTitle": ["audits[bf-cache].title"], "core/audits/bf-cache.js | description": ["audits[bf-cache].description"], "core/audits/bf-cache.js | displayValue": [{"values": {"itemCount": 4}, "path": "audits[bf-cache].displayValue"}], "core/audits/bf-cache.js | failureReasonColumn": ["audits[bf-cache].details.headings[0].label"], "core/audits/bf-cache.js | failureTypeColumn": ["audits[bf-cache].details.headings[1].label"], "node_modules/@paulirish/trace_engine/panels/application/components/BackForwardCacheStrings.js | webSocket": ["audits[bf-cache].details.items[0].reason"], "core/audits/bf-cache.js | supportPendingFailureType": ["audits[bf-cache].details.items[0].failureType"], "node_modules/@paulirish/trace_engine/panels/application/components/BackForwardCacheStrings.js | mainResourceHasCacheControlNoStore": ["audits[bf-cache].details.items[1].reason"], "core/audits/bf-cache.js | notActionableFailureType": ["audits[bf-cache].details.items[1].failureType", "audits[bf-cache].details.items[2].failureType", "audits[bf-cache].details.items[3].failureType"], "node_modules/@paulirish/trace_engine/panels/application/components/BackForwardCacheStrings.js | jsNetworkRequestReceivedCacheControlNoStoreResource": ["audits[bf-cache].details.items[2].reason"], "node_modules/@paulirish/trace_engine/panels/application/components/BackForwardCacheStrings.js | webSocketSticky": ["audits[bf-cache].details.items[3].reason"], "node_modules/@paulirish/trace_engine/models/trace/insights/Cache.js | title": ["audits[cache-insight].title"], "node_modules/@paulirish/trace_engine/models/trace/insights/Cache.js | description": ["audits[cache-insight].description"], "node_modules/@paulirish/trace_engine/models/trace/insights/CLSCulprits.js | title": ["audits[cls-culprits-insight].title"], "node_modules/@paulirish/trace_engine/models/trace/insights/CLSCulprits.js | description": ["audits[cls-culprits-insight].description"], "node_modules/@paulirish/trace_engine/models/trace/insights/DocumentLatency.js | title": ["audits[document-latency-insight].title"], "node_modules/@paulirish/trace_engine/models/trace/insights/DocumentLatency.js | description": ["audits[document-latency-insight].description"], "node_modules/@paulirish/trace_engine/models/trace/insights/DocumentLatency.js | passingRedirects": ["audits[document-latency-insight].details.items.noRedirects.label"], "node_modules/@paulirish/trace_engine/models/trace/insights/DocumentLatency.js | passingServerResponseTime": ["audits[document-latency-insight].details.items.serverResponseIsFast.label"], "node_modules/@paulirish/trace_engine/models/trace/insights/DocumentLatency.js | passingTextCompression": ["audits[document-latency-insight].details.items.usesCompression.label"], "node_modules/@paulirish/trace_engine/models/trace/insights/DOMSize.js | title": ["audits[dom-size-insight].title"], "node_modules/@paulirish/trace_engine/models/trace/insights/DOMSize.js | description": ["audits[dom-size-insight].description"], "node_modules/@paulirish/trace_engine/models/trace/insights/DOMSize.js | statistic": ["audits[dom-size-insight].details.headings[0].label"], "node_modules/@paulirish/trace_engine/models/trace/insights/DOMSize.js | value": ["audits[dom-size-insight].details.headings[2].label"], "node_modules/@paulirish/trace_engine/models/trace/insights/DOMSize.js | totalElements": ["audits[dom-size-insight].details.items[0].statistic"], "node_modules/@paulirish/trace_engine/models/trace/insights/DOMSize.js | maxChildren": ["audits[dom-size-insight].details.items[1].statistic"], "node_modules/@paulirish/trace_engine/models/trace/insights/DOMSize.js | maxDOMDepth": ["audits[dom-size-insight].details.items[2].statistic"], "node_modules/@paulirish/trace_engine/models/trace/insights/DuplicatedJavaScript.js | title": ["audits[duplicated-javascript-insight].title"], "node_modules/@paulirish/trace_engine/models/trace/insights/DuplicatedJavaScript.js | description": ["audits[duplicated-javascript-insight].description"], "node_modules/@paulirish/trace_engine/models/trace/insights/FontDisplay.js | title": ["audits[font-display-insight].title"], "node_modules/@paulirish/trace_engine/models/trace/insights/FontDisplay.js | description": ["audits[font-display-insight].description"], "node_modules/@paulirish/trace_engine/models/trace/insights/ForcedReflow.js | title": ["audits[forced-reflow-insight].title"], "node_modules/@paulirish/trace_engine/models/trace/insights/ForcedReflow.js | description": ["audits[forced-reflow-insight].description"], "node_modules/@paulirish/trace_engine/models/trace/insights/ForcedReflow.js | topTimeConsumingFunctionCall": ["audits[forced-reflow-insight].details.items[0].headings[0].label"], "node_modules/@paulirish/trace_engine/models/trace/insights/ForcedReflow.js | totalReflowTime": ["audits[forced-reflow-insight].details.items[0].headings[1].label", "audits[forced-reflow-insight].details.items[1].headings[1].label"], "core/lib/i18n/i18n.js | columnSource": ["audits[forced-reflow-insight].details.items[1].headings[0].label"], "node_modules/@paulirish/trace_engine/models/trace/insights/ForcedReflow.js | unattributed": ["audits[forced-reflow-insight].details.items[1].items[0].source.value"], "node_modules/@paulirish/trace_engine/models/trace/insights/ImageDelivery.js | title": ["audits[image-delivery-insight].title"], "node_modules/@paulirish/trace_engine/models/trace/insights/ImageDelivery.js | description": ["audits[image-delivery-insight].description"], "node_modules/@paulirish/trace_engine/models/trace/insights/InteractionToNextPaint.js | title": ["audits[interaction-to-next-paint-insight].title"], "node_modules/@paulirish/trace_engine/models/trace/insights/InteractionToNextPaint.js | description": ["audits[interaction-to-next-paint-insight].description"], "node_modules/@paulirish/trace_engine/models/trace/insights/LCPDiscovery.js | title": ["audits[lcp-discovery-insight].title"], "node_modules/@paulirish/trace_engine/models/trace/insights/LCPDiscovery.js | description": ["audits[lcp-discovery-insight].description"], "node_modules/@paulirish/trace_engine/models/trace/insights/LCPPhases.js | title": ["audits[lcp-phases-insight].title"], "node_modules/@paulirish/trace_engine/models/trace/insights/LCPPhases.js | description": ["audits[lcp-phases-insight].description"], "node_modules/@paulirish/trace_engine/models/trace/insights/LCPPhases.js | phase": ["audits[lcp-phases-insight].details.items[0].headings[0].label"], "node_modules/@paulirish/trace_engine/models/trace/insights/LCPPhases.js | timeToFirstByte": ["audits[lcp-phases-insight].details.items[0].items[0].label"], "node_modules/@paulirish/trace_engine/models/trace/insights/LCPPhases.js | elementRenderDelay": ["audits[lcp-phases-insight].details.items[0].items[1].label"], "node_modules/@paulirish/trace_engine/models/trace/insights/LegacyJavaScript.js | title": ["audits[legacy-javascript-insight].title"], "node_modules/@paulirish/trace_engine/models/trace/insights/LegacyJavaScript.js | description": ["audits[legacy-javascript-insight].description"], "node_modules/@paulirish/trace_engine/models/trace/insights/LegacyJavaScript.js | columnWastedBytes": ["audits[legacy-javascript-insight].details.headings[2].label"], "node_modules/@paulirish/trace_engine/models/trace/insights/ModernHTTP.js | title": ["audits[modern-http-insight].title"], "node_modules/@paulirish/trace_engine/models/trace/insights/ModernHTTP.js | description": ["audits[modern-http-insight].description"], "node_modules/@paulirish/trace_engine/models/trace/insights/NetworkDependencyTree.js | title": ["audits[network-dependency-tree-insight].title"], "node_modules/@paulirish/trace_engine/models/trace/insights/NetworkDependencyTree.js | description": ["audits[network-dependency-tree-insight].description"], "node_modules/@paulirish/trace_engine/models/trace/insights/RenderBlocking.js | title": ["audits[render-blocking-insight].title"], "node_modules/@paulirish/trace_engine/models/trace/insights/RenderBlocking.js | description": ["audits[render-blocking-insight].description"], "node_modules/@paulirish/trace_engine/models/trace/insights/ThirdParties.js | title": ["audits[third-parties-insight].title"], "node_modules/@paulirish/trace_engine/models/trace/insights/ThirdParties.js | description": ["audits[third-parties-insight].description"], "node_modules/@paulirish/trace_engine/models/trace/insights/Viewport.js | title": ["audits[viewport-insight].title"], "node_modules/@paulirish/trace_engine/models/trace/insights/Viewport.js | description": ["audits[viewport-insight].description"], "core/config/default-config.js | performanceCategoryTitle": ["categories.performance.title"], "core/config/default-config.js | metricGroupTitle": ["categoryGroups.metrics.title"], "core/config/default-config.js | insightsGroupTitle": ["categoryGroups.insights.title"], "core/config/default-config.js | insightsGroupDescription": ["categoryGroups.insights.description"], "core/config/default-config.js | diagnosticsGroupTitle": ["categoryGroups.diagnostics.title"], "core/config/default-config.js | diagnosticsGroupDescription": ["categoryGroups.diagnostics.description"], "core/config/default-config.js | a11yBestPracticesGroupTitle": ["categoryGroups[a11y-best-practices].title"], "core/config/default-config.js | a11yBestPracticesGroupDescription": ["categoryGroups[a11y-best-practices].description"], "core/config/default-config.js | a11yColorContrastGroupTitle": ["categoryGroups[a11y-color-contrast].title"], "core/config/default-config.js | a11yColorContrastGroupDescription": ["categoryGroups[a11y-color-contrast].description"], "core/config/default-config.js | a11yNamesLabelsGroupTitle": ["categoryGroups[a11y-names-labels].title"], "core/config/default-config.js | a11yNamesLabelsGroupDescription": ["categoryGroups[a11y-names-labels].description"], "core/config/default-config.js | a11yNavigationGroupTitle": ["categoryGroups[a11y-navigation].title"], "core/config/default-config.js | a11yNavigationGroupDescription": ["categoryGroups[a11y-navigation].description"], "core/config/default-config.js | a11yAriaGroupTitle": ["categoryGroups[a11y-aria].title"], "core/config/default-config.js | a11yAriaGroupDescription": ["categoryGroups[a11y-aria].description"], "core/config/default-config.js | a11yLanguageGroupTitle": ["categoryGroups[a11y-language].title"], "core/config/default-config.js | a11yLanguageGroupDescription": ["categoryGroups[a11y-language].description"], "core/config/default-config.js | a11yAudioVideoGroupTitle": ["categoryGroups[a11y-audio-video].title"], "core/config/default-config.js | a11yAudioVideoGroupDescription": ["categoryGroups[a11y-audio-video].description"], "core/config/default-config.js | a11yTablesListsVideoGroupTitle": ["categoryGroups[a11y-tables-lists].title"], "core/config/default-config.js | a11yTablesListsVideoGroupDescription": ["categoryGroups[a11y-tables-lists].description"], "core/config/default-config.js | seoMobileGroupTitle": ["categoryGroups[seo-mobile].title"], "core/config/default-config.js | seoMobileGroupDescription": ["categoryGroups[seo-mobile].description"], "core/config/default-config.js | seoContentGroupTitle": ["categoryGroups[seo-content].title"], "core/config/default-config.js | seoContentGroupDescription": ["categoryGroups[seo-content].description"], "core/config/default-config.js | seoCrawlingGroupTitle": ["categoryGroups[seo-crawl].title"], "core/config/default-config.js | seoCrawlingGroupDescription": ["categoryGroups[seo-crawl].description"], "core/config/default-config.js | bestPracticesTrustSafetyGroupTitle": ["categoryGroups[best-practices-trust-safety].title"], "core/config/default-config.js | bestPracticesUXGroupTitle": ["categoryGroups[best-practices-ux].title"], "core/config/default-config.js | bestPracticesBrowserCompatGroupTitle": ["categoryGroups[best-practices-browser-compat].title"], "core/config/default-config.js | bestPracticesGeneralGroupTitle": ["categoryGroups[best-practices-general].title"]}}}