
import type { NextRequest } from 'next/server';
import { NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    // Fetch the server's public IP address from an external service
    const response = await fetch('https://api.ipify.org?format=json', {
      cache: 'no-store', // Ensure we get a fresh IP, not a cached one
    });

    if (!response.ok) {
      console.error(`Failed to fetch IP from external service: ${response.status} ${response.statusText}`);
      throw new Error(`External IP service request failed with status: ${response.status}`);
    }

    const data = await response.json();

    if (data && data.ip) {
      // Basic validation for common IP formats (IPv4 and IPv6)
      // A more robust validation might be needed for all edge cases, but ipify is generally reliable.
      const ipRegex = /^(?:[0-9]{1,3}\.){3}[0-9]{1,3}$|^([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$|^::([0-9a-fA-F]{1,4}:){0,5}[0-9a-fA-F]{1,4}$|^([0-9a-fA-F]{1,4}:){1,6}:$/; // Simplified regex for example
      if (typeof data.ip === 'string' && data.ip.length > 0) { // Check if it's a non-empty string
        return NextResponse.json({ ip: data.ip });
      } else {
        console.error('Invalid IP format received from external service:', data.ip);
        throw new Error('Invalid IP format received from external service.');
      }
    } else {
      console.error('Invalid response structure from IP service:', data);
      throw new Error('Invalid response structure from IP service.');
    }
  } catch (error) {
    const errorMessage = "Could not determine server's public IP. Please check manually.";
    if (error instanceof Error) {
      console.error("Error fetching public IP:", error.message);
      // Optionally, you could expose more specific error messages if needed,
      // but for the user, a generic failure message is often sufficient.
    } else {
      console.error("Unknown error fetching public IP:", error);
    }
    return NextResponse.json({ ip: errorMessage });
  }
}
