"use client";

import { useState, useEffect, useCallback } from 'react';
import { toast } from '@/hooks/use-toast';

interface UseApiKeysReturn {
  apiKey: string | null;
  apiSecret: string | null;
  isLoading: boolean;
  isConfigured: boolean;
  setApiKeys: (apiKey: string, apiSecret: string) => void;
  clearApiKeys: () => void;
  validateKeys: () => Promise<boolean>;
  isValidating: boolean;
}

export function useApiKeys(): UseApiKeysReturn {
  const [apiKey, setApiKey] = useState<string | null>(null);
  const [apiSecret, setApiSecret] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isValidating, setIsValidating] = useState(false);

  // Load API keys from localStorage on mount
  useEffect(() => {
    try {
      const storedApiKey = localStorage.getItem("binanceApiKey");
      const storedApiSecret = localStorage.getItem("binanceApiSecret");
      
      setApiKey(storedApiKey);
      setApiSecret(storedApiSecret);
    } catch (error) {
      console.error('Failed to load API keys from localStorage:', error);
      toast({
        title: "Storage Error",
        description: "Failed to load API keys from storage.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  }, []);

  const isConfigured = Boolean(apiKey && apiSecret);

  const setApiKeys = useCallback((newApiKey: string, newApiSecret: string) => {
    try {
      // Validate input
      if (!newApiKey.trim() || !newApiSecret.trim()) {
        throw new Error("API key and secret cannot be empty");
      }

      // Store in localStorage
      localStorage.setItem("binanceApiKey", newApiKey.trim());
      localStorage.setItem("binanceApiSecret", newApiSecret.trim());
      
      // Update state
      setApiKey(newApiKey.trim());
      setApiSecret(newApiSecret.trim());

      toast({
        title: "API Keys Updated",
        description: "Your Binance API keys have been saved successfully.",
      });
    } catch (error: any) {
      console.error('Failed to set API keys:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to save API keys.",
        variant: "destructive",
      });
    }
  }, []);

  const clearApiKeys = useCallback(() => {
    try {
      // Remove from localStorage
      localStorage.removeItem("binanceApiKey");
      localStorage.removeItem("binanceApiSecret");
      
      // Clear state
      setApiKey(null);
      setApiSecret(null);

      toast({
        title: "API Keys Cleared",
        description: "Your API keys have been removed successfully.",
      });
    } catch (error) {
      console.error('Failed to clear API keys:', error);
      toast({
        title: "Error",
        description: "Failed to clear API keys.",
        variant: "destructive",
      });
    }
  }, []);

  const validateKeys = useCallback(async (): Promise<boolean> => {
    if (!apiKey || !apiSecret) {
      toast({
        title: "Validation Error",
        description: "API keys are not configured.",
        variant: "destructive",
      });
      return false;
    }

    setIsValidating(true);

    try {
      const response = await fetch('/api/binance/test-connection', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ apiKey, apiSecret }),
      });

      if (!response.ok) {
        let errorMessage = `HTTP error! status: ${response.status}`;
        try {
          const errorResult = await response.json();
          if (errorResult?.message) {
            errorMessage = errorResult.message;
          }
        } catch (e) {
          errorMessage = response.statusText || errorMessage;
        }
        throw new Error(errorMessage);
      }

      const result = await response.json();

      if (result.success) {
        toast({
          title: "Validation Successful",
          description: "Your API keys are valid and working correctly.",
        });
        return true;
      } else {
        throw new Error(result.message || 'API key validation failed');
      }
    } catch (error: any) {
      console.error('API key validation failed:', error);
      
      let errorMessage = error.message;
      
      // Provide more specific error messages
      if (errorMessage.includes('Invalid API-key')) {
        errorMessage = "Invalid API key. Please check your Binance API key.";
      } else if (errorMessage.includes('Invalid signature')) {
        errorMessage = "Invalid API secret. Please check your Binance API secret.";
      } else if (errorMessage.includes('IP not allowed')) {
        errorMessage = "Your IP address is not whitelisted for this API key.";
      } else if (errorMessage.includes('API-key format invalid')) {
        errorMessage = "API key format is invalid. Please check your key.";
      }

      toast({
        title: "Validation Failed",
        description: errorMessage,
        variant: "destructive",
      });
      
      return false;
    } finally {
      setIsValidating(false);
    }
  }, [apiKey, apiSecret]);

  return {
    apiKey,
    apiSecret,
    isLoading,
    isConfigured,
    setApiKeys,
    clearApiKeys,
    validateKeys,
    isValidating,
  };
}
