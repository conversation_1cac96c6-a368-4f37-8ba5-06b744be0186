{"IDX.aI.enableInlineCompletion": true, "IDX.aI.enableCodebaseIndexing": true, "css.lint.unknownAtRules": "ignore", "scss.lint.unknownAtRules": "ignore", "css.validate": false, "scss.validate": false, "tailwindCSS.includeLanguages": {"typescript": "html", "javascript": "html", "html": "html"}, "tailwindCSS.experimental.classRegex": [["cva\\(([^)]*)\\)", "[\"'`]([^\"'`]*).*?[\"'`]"], ["cn\\(([^)]*)\\)", "[\"'`]([^\"'`]*).*?[\"'`]"]]}