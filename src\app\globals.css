@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 210 40% 9%; /* Deep dark cool blue/grey */
    --foreground: 210 40% 95%; /* Bright near-white */

    --card: 210 40% 12%; /* Slightly lighter than background */
    --card-foreground: 210 40% 95%;

    --popover: 210 40% 12%;
    --popover-foreground: 210 40% 95%;

    --primary: 200 100% 50%; /* Vibrant cyan/blue */
    --primary-foreground: 210 40% 9%; /* Dark text for contrast on primary */

    --secondary: 210 40% 18%; /* Darker secondary for subtle elements */
    --secondary-foreground: 210 40% 85%;

    --muted: 210 40% 15%;
    --muted-foreground: 210 40% 65%;

    --accent: 270 100% 60%; /* Vibrant purple/magenta */
    --accent-foreground: 210 40% 95%; /* Light text for contrast on accent */

    --destructive: 0 72% 51%; /* Kept similar for clear error indication */
    --destructive-foreground: 0 0% 98%;

    --border: 210 40% 20%; /* Subtle but visible borders */
    --input: 210 40% 16%; /* Input background */
    --ring: 200 100% 50%; /* Ring matches primary */

    --radius: 0.5rem;

    /* Success and warning colors for trading */
    --success: 142 76% 36%;
    --success-foreground: 355 100% 97%;
    --warning: 38 92% 50%;
    --warning-foreground: 20 14% 4%;

    --chart-1: hsl(var(--primary));
    --chart-2: hsl(var(--accent));
    --chart-3: 170 80% 45%; /* Contrasting teal/green */
    --chart-4: 30 100% 55%; /* Warm orange */
    --chart-5: 330 90% 60%; /* Pink/rose */

    /* Sidebar specific theme variables */
    --sidebar-background: 210 40% 7%; /* Even darker for depth */
    --sidebar-foreground: 210 40% 80%;
    --sidebar-primary: hsl(var(--primary));
    --sidebar-primary-foreground: hsl(var(--primary-foreground));
    --sidebar-accent: 210 40% 15%;
    --sidebar-accent-foreground: 210 40% 95%;
    --sidebar-border: 210 40% 15%;
    --sidebar-ring: hsl(var(--primary));

    /* Glass morphism effects */
    --glass-bg: rgba(255, 255, 255, 0.05);
    --glass-border: rgba(255, 255, 255, 0.1);
  }

  .dark {
    --background: 210 40% 9%;
    --foreground: 210 40% 95%;
    --card: 210 40% 12%;
    --card-foreground: 210 40% 95%;
    --popover: 210 40% 12%;
    --popover-foreground: 210 40% 95%;
    --primary: 200 100% 50%;
    --primary-foreground: 210 40% 9%;
    --secondary: 210 40% 18%;
    --secondary-foreground: 210 40% 85%;
    --muted: 210 40% 15%;
    --muted-foreground: 210 40% 65%;
    --accent: 270 100% 60%;
    --accent-foreground: 210 40% 95%;
    --destructive: 0 72% 51%;
    --destructive-foreground: 0 0% 98%;
    --border: 210 40% 20%;
    --input: 210 40% 16%;
    --ring: 200 100% 50%;

    --success: 142 76% 36%;
    --success-foreground: 355 100% 97%;
    --warning: 38 92% 50%;
    --warning-foreground: 20 14% 4%;

    --chart-1: hsl(var(--primary));
    --chart-2: hsl(var(--accent));
    --chart-3: 170 80% 45%;
    --chart-4: 30 100% 55%;
    --chart-5: 330 90% 60%;
    
    --sidebar-background: 210 40% 7%;
    --sidebar-foreground: 210 40% 80%;
    --sidebar-primary: hsl(var(--primary));
    --sidebar-primary-foreground: hsl(var(--primary-foreground));
    --sidebar-accent: 210 40% 15%;
    --sidebar-accent-foreground: 210 40% 95%;
    --sidebar-border: 210 40% 15%;
    --sidebar-ring: hsl(var(--primary));

    --glass-bg: rgba(255, 255, 255, 0.05);
    --glass-border: rgba(255, 255, 255, 0.1);
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-family: var(--font-geist-sans), sans-serif;
    overflow-x: hidden;
    -webkit-overflow-scrolling: touch;
    scroll-behavior: auto; /* Remove smooth scroll para melhor performance */
  }

  /* Optimized scrolling */
  html {
    scroll-behavior: auto;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  /* Optimized scrollbars */
  ::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  ::-webkit-scrollbar-track {
    background: transparent;
  }

  ::-webkit-scrollbar-thumb {
    background: rgba(59, 130, 246, 0.3);
    border-radius: 3px;
    border: none;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: rgba(59, 130, 246, 0.5);
  }

  ::-webkit-scrollbar-corner {
    background: transparent;
  }
}

@layer components {
  /* Enhanced card styles */
  .card-interactive {
    @apply rounded-lg border bg-card text-card-foreground shadow-lg;
  }

  .card-glass {
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    @apply rounded-lg shadow-lg;
  }

  /* Trading specific styles */
  .trading-card {
    @apply card-interactive bg-gradient-to-br from-card via-card to-card/80 backdrop-blur-sm;
  }

  .profit-text {
    @apply text-green-400 font-semibold;
  }

  .loss-text {
    @apply text-red-400 font-semibold;
  }

  .neutral-text {
    @apply text-muted-foreground;
  }

  /* Gradient backgrounds */
  .gradient-primary {
    background: linear-gradient(135deg, hsl(var(--primary)) 0%, hsl(var(--accent)) 100%);
  }

  .gradient-success {
    background: linear-gradient(135deg, hsl(var(--success)) 0%, #22c55e 100%);
  }

  .gradient-warning {
    background: linear-gradient(135deg, hsl(var(--warning)) 0%, #f59e0b 100%);
  }

  .gradient-destructive {
    background: linear-gradient(135deg, hsl(var(--destructive)) 0%, #dc2626 100%);
  }

  /* Loading animations */
  .pulse-glow {
    animation: pulse-glow 2s ease-in-out infinite alternate;
  }

  @keyframes pulse-glow {
    from {
      box-shadow: 0 0 20px hsl(var(--primary) / 0.4);
    }
    to {
      box-shadow: 0 0 30px hsl(var(--primary) / 0.7);
    }
  }

  /* Data visualization styles */
  .metric-card {
    @apply trading-card p-6 space-y-3;
  }

  .metric-value {
    @apply text-2xl font-bold tracking-tight;
  }

  .metric-label {
    @apply text-sm text-muted-foreground font-medium;
  }

  .metric-change {
    @apply text-xs font-medium flex items-center gap-1;
  }

  /* Button enhancements */
  .btn-trading {
    @apply relative overflow-hidden transition-all duration-300 hover:scale-105 active:scale-95;
  }

  .btn-trading::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
  }

  .btn-trading:hover::before {
    left: 100%;
  }

  /* Table enhancements */
  .table-trading {
    @apply w-full border-separate border-spacing-0;
  }

  .table-trading th {
    @apply sticky top-0 z-10 bg-muted/50 backdrop-blur-sm px-4 py-3 text-left text-xs font-semibold text-muted-foreground uppercase tracking-wider border-b border-border/50;
  }

  .table-trading td {
    @apply px-4 py-3 text-sm border-b border-border/30 transition-colors;
  }

  .table-trading tbody tr:hover {
    @apply bg-muted/30;
  }

  /* Alert styles */
  .alert-success {
    @apply border-green-500/50 bg-green-500/10 text-green-400;
  }

  .alert-warning {
    @apply border-yellow-500/50 bg-yellow-500/10 text-yellow-400;
  }

  .alert-error {
    @apply border-red-500/50 bg-red-500/10 text-red-400;
  }

  /* Status indicators */
  .status-dot {
    @apply w-2 h-2 rounded-full;
  }

  .status-online {
    @apply bg-green-400 shadow-sm shadow-green-400/50;
  }

  .status-offline {
    @apply bg-red-400 shadow-sm shadow-red-400/50;
  }

  .status-warning {
    @apply bg-yellow-400 shadow-sm shadow-yellow-400/50;
  }

  /* Form enhancements */
  .form-field {
    @apply space-y-2;
  }

  .form-input {
    @apply transition-all duration-200 focus:ring-2 focus:ring-primary/50 focus:border-primary/70;
  }

  /* Navigation enhancements */
  .nav-link {
    @apply relative transition-all duration-200 hover:text-primary;
  }

  .nav-link::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 0;
    height: 2px;
    background: hsl(var(--primary));
    transition: width 0.3s ease;
  }

  .nav-link:hover::after,
  .nav-link.active::after {
    width: 100%;
  }

  /* Performance optimizations */
  .will-change-transform {
    will-change: transform;
    transform: translateZ(0);
  }

  .will-change-contents {
    will-change: contents;
  }

  .will-change-scroll {
    will-change: scroll-position;
  }

  .transform-gpu {
    transform: translate3d(0, 0, 0);
    backface-visibility: hidden;
  }

  .critical-layer {
    transform: translateZ(0);
    will-change: transform;
    isolation: isolate;
  }

  /* High-performance scrolling */
  * {
    scroll-behavior: auto !important;
  }

  body {
    -webkit-overflow-scrolling: touch;
    transform: translateZ(0);
    backface-visibility: hidden;
    perspective: 1000px;
  }

  /* Ultra-optimized containers */
  .trading-card {
    contain: layout style paint;
    transform: translateZ(0);
    will-change: auto;
    backface-visibility: hidden;
  }

  .trading-card:hover {
    transform: translate3d(0, -1px, 0);
    transition: transform 0.1s ease-out;
  }

  /* High-performance scroll */
  .scroll-container {
    contain: layout style;
    transform: translateZ(0);
    -webkit-overflow-scrolling: touch;
    overflow-scrolling: touch;
    scrollbar-width: thin;
    scroll-snap-type: none;
  }

  /* Disable any scroll animations */
  * {
    scroll-margin: 0 !important;
    scroll-padding: 0 !important;
  }

  /* Optimize for 60fps scrolling */
  main {
    will-change: scroll-position;
    transform: translateZ(0);
    backface-visibility: hidden;
  }

  /* Grid and flexbox optimizations */
  .grid {
    contain: layout;
    transform: translateZ(0);
  }

  /* Card optimizations for scroll */
  [class*="card"] {
    contain: layout style paint;
    transform: translateZ(0);
  }

  /* Skeleton animations */
  @keyframes shimmer {
    0% {
      background-position: -200% 0;
    }
    100% {
      background-position: 200% 0;
    }
  }

  .animate-shimmer {
    animation: shimmer 2s infinite;
  }

  /* Smooth transitions */
  .transition-smooth {
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  }

  /* Focus improvements */
  .focus-ring {
    @apply focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 focus:ring-offset-background;
  }

  /* Custom scrollbar styles */
  .scroll-container::-webkit-scrollbar {
    width: 6px;
  }

  .scroll-container::-webkit-scrollbar-track {
    background: transparent;
  }

  .scroll-container::-webkit-scrollbar-thumb {
    background-color: hsl(var(--muted-foreground));
    border-radius: 3px;
  }

  .scroll-container::-webkit-scrollbar-thumb:hover {
    background-color: hsl(var(--foreground));
  }
}
