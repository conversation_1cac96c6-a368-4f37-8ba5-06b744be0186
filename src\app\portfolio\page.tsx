
import { AssetTable } from "@/components/dashboard/AssetTable";
import { <PERSON>, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import type { PortfolioAsset } from "@/lib/types";
import { DollarSign, Percent, BarChartBig } from "lucide-react";

// Mock assets now do not need iconUrl, AssetTable will handle it.
const mockAssets: PortfolioAsset[] = [
  { id: '1', name: 'Bitcoin', symbol: 'BTC', quantity: 0.5, price: 60000, value: 30000, change24h: 2.5, exchange: 'Binance' },
  { id: '2', name: 'Ethereum', symbol: 'ETH', quantity: 10, price: 3000, value: 30000, change24h: -1.2, exchange: '<PERSON>rak<PERSON>' },
  { id: '3', name: 'Sol<PERSON>', symbol: 'SOL', quantity: 100, price: 150, value: 15000, change24h: 5.8, exchange: 'Coinbase' },
  { id: '4', name: '<PERSON><PERSON>', symbol: 'ADA', quantity: 5000, price: 0.45, value: 2250, change24h: 1.0, exchange: 'Binance' },
  { id: '5', name: '<PERSON><PERSON><PERSON>', symbol: 'DOT', quantity: 200, price: 7.20, value: 1440, change24h: -0.5, exchange: 'Kraken' },
];

export default function PortfolioPage() {
  const totalPortfolioValue = mockAssets.reduce((sum, asset) => sum + asset.value, 0);
  const average24hChange = mockAssets.reduce((sum, asset) => sum + asset.change24h, 0) / (mockAssets.length || 1);

  return (
    <div className="flex flex-col gap-6">
      <Card className="bg-card text-card-foreground border-border shadow-sm"> {/* Basic card for title, not interactive */}
        <CardHeader>
          <CardTitle className="text-3xl">Aggregated Portfolio</CardTitle>
          <CardDescription>A detailed view of all your assets across connected exchanges.</CardDescription>
        </CardHeader>
      </Card>
      
      <div className="grid gap-4 md:grid-cols-3">
        <Card className="card-interactive">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Value</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              ${totalPortfolioValue.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
            </div>
          </CardContent>
        </Card>
        
        <Card className="card-interactive">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Assets</CardTitle>
            <BarChartBig className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{mockAssets.length}</div>
          </CardContent>
        </Card>
        
        <Card className="card-interactive">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg. 24h Change</CardTitle>
            <Percent className={`h-4 w-4 ${average24hChange >= 0 ? "text-green-500" : "text-red-500"}`} />
          </CardHeader>
          <CardContent>
            <div className={`text-2xl font-bold ${average24hChange >= 0 ? "text-green-500" : "text-red-500"}`}>
              {average24hChange.toFixed(2)}%
            </div>
          </CardContent>
        </Card>
      </div>

      <Card className="card-interactive">
        <CardHeader>
          <CardTitle>All Assets</CardTitle>
          <CardDescription>Detailed breakdown of each asset in your portfolio.</CardDescription>
        </CardHeader>
        <CardContent>
          <AssetTable assets={mockAssets} />
        </CardContent>
      </Card>
    </div>
  );
}
