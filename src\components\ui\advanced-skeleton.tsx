"use client";

import React from 'react';
import { cn } from '@/lib/utils';

interface SkeletonProps extends React.HTMLAttributes<HTMLDivElement> {
  variant?: 'default' | 'pulse' | 'wave' | 'shimmer';
  speed?: 'slow' | 'normal' | 'fast';
}

const Skeleton = React.forwardRef<HTMLDivElement, SkeletonProps>(
  ({ className, variant = 'default', speed = 'normal', ...props }, ref) => {
    const speedClasses = {
      slow: 'animate-pulse [animation-duration:2s]',
      normal: 'animate-pulse',
      fast: 'animate-pulse [animation-duration:0.8s]',
    };

    const variantClasses = {
      default: 'bg-muted',
      pulse: 'bg-gradient-to-r from-muted via-muted/50 to-muted',
      wave: 'bg-gradient-to-r from-muted via-primary/10 to-muted',
      shimmer: 'bg-gradient-to-r from-muted via-muted/30 to-muted bg-[length:200%_100%] animate-[shimmer_2s_infinite]',
    };

    return (
      <div
        ref={ref}
        className={cn(
          'rounded-md',
          variantClasses[variant],
          speedClasses[speed],
          className
        )}
        {...props}
      />
    );
  }
);
Skeleton.displayName = 'Skeleton';

// Portfolio Summary Skeleton
const PortfolioSummarySkeleton = () => (
  <div className="trading-card p-6 space-y-6">
    <div className="flex items-center justify-between">
      <div className="flex items-center gap-2">
        <Skeleton className="h-5 w-5 rounded-full" variant="shimmer" />
        <Skeleton className="h-6 w-32" variant="shimmer" />
      </div>
      <Skeleton className="h-6 w-20 rounded-full" variant="pulse" />
    </div>
    
    <div className="space-y-4">
      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <Skeleton className="h-4 w-20" />
          <div className="flex items-center gap-1">
            <Skeleton className="h-3 w-3 rounded-full" />
            <Skeleton className="h-3 w-12" />
          </div>
        </div>
        <div className="flex items-baseline gap-2">
          <Skeleton className="h-8 w-32" variant="wave" />
          <Skeleton className="h-5 w-16" />
        </div>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {[1, 2].map((i) => (
          <div key={i} className="space-y-3 p-4 rounded-lg bg-muted/30">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Skeleton className="w-3 h-3 rounded-full" />
                <Skeleton className="h-4 w-12" />
              </div>
              <Skeleton className="h-5 w-12 rounded-full" />
            </div>
            <div className="space-y-1">
              <Skeleton className="h-5 w-24" />
              <Skeleton className="h-2 w-full rounded-full" />
            </div>
          </div>
        ))}
      </div>
    </div>
  </div>
);

// Asset Table Skeleton
const AssetTableSkeleton = () => (
  <div className="space-y-4">
    <div className="flex items-center justify-between">
      <Skeleton className="h-9 w-64 rounded-md" />
      <Skeleton className="h-9 w-32 rounded-md" />
    </div>
    
    <div className="rounded-md border">
      <div className="grid grid-cols-6 gap-4 p-4 border-b bg-muted/50">
        {Array.from({ length: 6 }).map((_, i) => (
          <Skeleton key={i} className="h-4 w-full" />
        ))}
      </div>
      
      {Array.from({ length: 5 }).map((_, i) => (
        <div key={i} className="grid grid-cols-6 gap-4 p-4 border-b last:border-b-0">
          <div className="flex items-center gap-2">
            <Skeleton className="h-8 w-8 rounded-full" variant="shimmer" />
            <div className="space-y-1">
              <Skeleton className="h-4 w-16" />
              <Skeleton className="h-3 w-12" />
            </div>
          </div>
          {Array.from({ length: 5 }).map((_, j) => (
            <Skeleton key={j} className="h-4 w-full" />
          ))}
        </div>
      ))}
    </div>
  </div>
);

// Positions Table Skeleton
const PositionsTableSkeleton = () => (
  <div className="space-y-4">
    <div className="flex items-center justify-between">
      <Skeleton className="h-6 w-32" />
      <Skeleton className="h-5 w-24 rounded-full" />
    </div>
    
    <div className="rounded-md border">
      <div className="grid grid-cols-8 gap-4 p-4 border-b bg-muted/50">
        {Array.from({ length: 8 }).map((_, i) => (
          <Skeleton key={i} className="h-4 w-full" />
        ))}
      </div>
      
      {Array.from({ length: 3 }).map((_, i) => (
        <div key={i} className="grid grid-cols-8 gap-4 p-4 border-b last:border-b-0">
          <div className="flex items-center gap-2">
            <Skeleton className="h-6 w-6 rounded-full" variant="shimmer" />
            <Skeleton className="h-4 w-16" />
          </div>
          {Array.from({ length: 6 }).map((_, j) => (
            <Skeleton key={j} className="h-4 w-full" />
          ))}
          <Skeleton className="h-8 w-20 rounded-md" />
        </div>
      ))}
    </div>
  </div>
);

// Trading Terminal Skeleton
const TradingTerminalSkeleton = () => (
  <div className="trading-card p-6 space-y-6">
    <div className="space-y-2">
      <div className="flex items-center gap-2">
        <Skeleton className="h-6 w-6" variant="shimmer" />
        <Skeleton className="h-6 w-48" />
      </div>
      <Skeleton className="h-4 w-full max-w-md" />
    </div>
    
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <div className="space-y-4">
        <Skeleton className="h-10 w-full rounded-md" />
        <div className="grid grid-cols-2 gap-4">
          <Skeleton className="h-10 w-full rounded-md" />
          <Skeleton className="h-10 w-full rounded-md" />
        </div>
        <Skeleton className="h-10 w-full rounded-md" />
        <Skeleton className="h-10 w-full rounded-md" />
        <Skeleton className="h-12 w-full rounded-md" variant="wave" />
      </div>
      
      <div className="space-y-4">
        <Skeleton className="h-64 w-full rounded-md" variant="pulse" />
        <div className="grid grid-cols-3 gap-2">
          {Array.from({ length: 6 }).map((_, i) => (
            <Skeleton key={i} className="h-8 w-full rounded-md" />
          ))}
        </div>
      </div>
    </div>
  </div>
);

// Dashboard Skeleton
const DashboardSkeleton = () => (
  <div className="space-y-6">
    <div className="flex justify-between items-center">
      <div className="space-y-2">
        <Skeleton className="h-8 w-64" variant="wave" />
        <Skeleton className="h-4 w-48" />
      </div>
      <div className="flex items-center gap-2">
        <Skeleton className="h-6 w-20 rounded-full" />
        <Skeleton className="h-9 w-24 rounded-md" />
      </div>
    </div>
    
    <PortfolioSummarySkeleton />
    
    <div className="space-y-4">
      <div className="flex space-x-1 rounded-lg bg-muted p-1">
        {Array.from({ length: 4 }).map((_, i) => (
          <Skeleton key={i} className="h-9 flex-1 rounded-md" />
        ))}
      </div>
      
      <AssetTableSkeleton />
    </div>
  </div>
);

export {
  Skeleton,
  PortfolioSummarySkeleton,
  AssetTableSkeleton,
  PositionsTableSkeleton,
  TradingTerminalSkeleton,
  DashboardSkeleton,
};

// Add shimmer animation to global CSS
const shimmerKeyframes = `
@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}
`;
