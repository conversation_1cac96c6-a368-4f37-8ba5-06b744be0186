import {
  Accordion,
  Accordion<PERSON>ontent,
  Accordion<PERSON><PERSON>,
  AccordionTrigger,
} from "@/components/ui/accordion";
import Image from "next/image";
import { Card, CardContent } from "@/components/ui/card";
import { CheckCircle2 } from "lucide-react";

const onboardingSteps = [
  {
    id: "step1",
    title: "Connecting Your Exchanges",
    content: "Securely connect your cryptocurrency exchange accounts (like Binance, Kraken, Coinbase) using API keys. CryptoPilot uses read-only API keys by default for maximum security when viewing your portfolio. For trading features, carefully manage API key permissions.",
    image: "https://placehold.co/600x400.png?text=Connect+Exchange+API",
    aiHint: "api integration",
    points: [
        "Navigate to the 'Exchanges' or 'API Management' section in your brokerage account.",
        "Create a new API key. Ensure it has the necessary permissions (e.g., read-only for portfolio tracking, trade permissions if you intend to use trading features).",
        "Copy the API Key and Secret Key.",
        "In CryptoPilot, go to 'Settings > Connected Exchanges' and add a new exchange, pasting your keys.",
    ]
  },
  {
    id: "step2",
    title: "Understanding Your Aggregated Portfolio",
    content: "The Dashboard provides a unified view of all your assets across connected exchanges. Track your total balance, profit/loss, and individual asset performance at a glance. Dive deeper into transaction history and open positions.",
    image: "https://placehold.co/600x400.png?text=Aggregated+Portfolio+View",
    aiHint: "dashboard charts",
    points: [
        "View total portfolio value and 24-hour P&L.",
        "See a detailed list of all your assets, their current price, quantity, and value.",
        "Analyze your transaction history to understand your trading patterns.",
        "Monitor your open derivative positions and their performance.",
    ]
  },
  {
    id: "step3",
    title: "Setting Up Price Alerts",
    content: "Never miss a critical market movement. Set custom price alerts for any cryptocurrency. CryptoPilot will notify you via your preferred method (push notification or email - setup in Settings) when your target price is reached.",
    image: "https://placehold.co/600x400.png?text=Set+Price+Alerts",
    aiHint: "notification bell",
    points: [
        "Go to the 'Price Alerts' page.",
        "Select the cryptocurrency, choose whether you want an alert for price 'above' or 'below' a certain value.",
        "Enter your target price.",
        "Active alerts will be listed, and you can manage them anytime.",
    ]
  },
  {
    id: "step4",
    title: "Leveraging AI News Summaries",
    content: "Stay informed with AI-powered news summaries. Our GenAI tool scans market news and provides concise summaries relevant to the cryptocurrencies in your portfolio, helping you make informed decisions.",
    image: "https://placehold.co/600x400.png?text=AI+News+Summary",
    aiHint: "ai news",
    points: [
        "Visit the 'News Summary' page.",
        "If your portfolio is connected, the AI will automatically try to find relevant news. You can also provide custom articles.",
        "Read concise summaries highlighting key events and potential impacts on your holdings.",
        "Use these insights to complement your trading strategy.",
    ]
  },
];

export function OnboardingContent() {
  return (
    <Card className="card-interactive">
      <CardContent className="p-6">
        <Accordion type="single" collapsible className="w-full">
          {onboardingSteps.map((step, index) => (
            <AccordionItem value={step.id} key={step.id}>
              <AccordionTrigger className="text-lg hover:no-underline">
                <div className="flex items-center gap-3">
                  <div className="flex h-8 w-8 items-center justify-center rounded-full bg-primary text-primary-foreground">
                    {index + 1}
                  </div>
                  {step.title}
                </div>
              </AccordionTrigger>
              <AccordionContent className="pt-2 pb-4 space-y-4">
                <p className="text-muted-foreground">{step.content}</p>
                <div className="grid md:grid-cols-2 gap-4 items-start">
                  <div>
                    <h4 className="font-semibold mb-2 text-foreground">Key Actions:</h4>
                    <ul className="space-y-1.5">
                      {step.points.map((point, pIndex) => (
                        <li key={pIndex} className="flex items-start">
                          <CheckCircle2 className="h-4 w-4 mr-2 mt-0.5 text-primary flex-shrink-0" />
                          <span className="text-sm text-muted-foreground">{point}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                  <div className="rounded-lg overflow-hidden border shadow-md">
                    <Image
                      src={step.image}
                      alt={step.title}
                      width={600}
                      height={400}
                      className="object-cover aspect-video"
                      data-ai-hint={step.aiHint}
                    />
                  </div>
                </div>
              </AccordionContent>
            </AccordionItem>
          ))}
        </Accordion>
      </CardContent>
    </Card>
  );
}
