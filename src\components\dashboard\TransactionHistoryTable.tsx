
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import type { Transaction } from "@/lib/types";
import { format, parseISO } from "date-fns";
import { cn } from "@/lib/utils";

interface TransactionHistoryTableProps {
  transactions: Transaction[];
}

const formatPriceForTable = (price?: number) => {
  if (price === undefined || price === null) return '-';
  if (price === 0) return "$0.00";
  
  let options: Intl.NumberFormatOptions = { // eslint-disable-line prefer-const
    style: "currency",
    currency: "USD",
    minimumFractionDigits: 2,
  };

  if (Math.abs(price) < 0.01 && Math.abs(price) !== 0) {
    options.maximumFractionDigits = 8;
  } else if (Math.abs(price) < 1 && Math.abs(price) !== 0) {
    options.maximumFractionDigits = 6;
  } else {
    options.maximumFractionDigits = 2;
  }
  return price.toLocaleString(undefined, options);
};

const formatQuantityForTable = (quantity: number) => {
  return quantity.toLocaleString(undefined, {
    minimumFractionDigits: 2, 
    maximumFractionDigits: 8,
  });
};

const formatTotalValueForTable = (value?: number) => {
    if (value === undefined || value === null) return '-';
    return value.toLocaleString(undefined, {
        style: "currency",
        currency: "USD",
        minimumFractionDigits: 2,
        maximumFractionDigits: 2,
    });
};

export function TransactionHistoryTable({ transactions }: TransactionHistoryTableProps) {
  if (!transactions || transactions.length === 0) {
    return <p className="text-muted-foreground text-center py-4">Sem dados de transações recentes obtidos.</p>;
  }

  return (
    <Table>
      <TableHeader>
        <TableRow>
          <TableHead>Data</TableHead>
          <TableHead>Tipo</TableHead>
          <TableHead>Ativo</TableHead>
          <TableHead className="hidden md:table-cell">Exchange</TableHead>
          <TableHead className="text-right">Quantidade</TableHead>
          <TableHead className="text-right hidden sm:table-cell">Preço/Unid.</TableHead>
          <TableHead className="text-right">Valor Total</TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {transactions.map((tx) => (
          <TableRow key={tx.id} className="hover:bg-muted/50">
            <TableCell className="text-xs text-muted-foreground">
              {format(parseISO(tx.date), "dd MMM yyyy, HH:mm")}
            </TableCell>
            <TableCell>
              <Badge 
                variant={tx.type === 'buy' || tx.type === 'deposit' ? 'default' : 'destructive'}
                className={
                  tx.type === 'buy' ? 'bg-green-600/20 text-green-400 border-green-600/30 hover:bg-green-600/30' :
                  tx.type === 'sell' ? 'bg-red-600/20 text-red-400 border-red-600/30 hover:bg-red-600/30' :
                  tx.type === 'deposit' ? 'bg-blue-600/20 text-blue-400 border-blue-600/30 hover:bg-blue-600/30' :
                   tx.type === 'withdraw' ? 'bg-orange-600/20 text-orange-400 border-orange-600/30 hover:bg-orange-600/30' : 
                  'bg-yellow-600/20 text-yellow-400 border-yellow-600/30 hover:bg-yellow-600/30' 
                }
              >
                {tx.type.charAt(0).toUpperCase() + tx.type.slice(1)}
              </Badge>
            </TableCell>
            <TableCell>
              <div className="font-medium">{tx.cryptoName}</div>
              <div className="text-xs text-muted-foreground">{tx.cryptoSymbol}</div>
            </TableCell>
            <TableCell className="hidden md:table-cell">
              <Badge variant="outline">{tx.exchange}</Badge>
            </TableCell>
            <TableCell className={cn("text-right", "tabular-nums")}>{formatQuantityForTable(tx.amount)}</TableCell>
            <TableCell className={cn("text-right", "hidden sm:table-cell", "tabular-nums")}>
              {formatPriceForTable(tx.pricePerUnit)}
            </TableCell>
            <TableCell className={cn("text-right", "tabular-nums")}>
              {formatTotalValueForTable(tx.totalValue)}
            </TableCell>
          </TableRow>
        ))}
      </TableBody>
    </Table>
  );
}
