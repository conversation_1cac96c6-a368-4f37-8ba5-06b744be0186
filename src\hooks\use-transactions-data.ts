"use client";

import { useState, useEffect, useCallback, useRef } from 'react';
import { toast } from '@/hooks/use-toast';
import type { Transaction } from '@/lib/types';

interface UseTransactionsDataReturn {
  data: Transaction[];
  isLoading: boolean;
  error: string | null;
  isRefreshing: boolean;
  refetch: () => Promise<void>;
  lastUpdated: Date | null;
}

const CACHE_KEY = 'transactions_data_cache';
const CACHE_DURATION = 60000; // 1 minute (transactions don't change as frequently)

export function useTransactionsData(
  apiKey: string | null,
  apiSecret: string | null,
  autoRefresh = true,
  refreshInterval = 60000
): UseTransactionsDataReturn {
  const [data, setData] = useState<Transaction[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);
  
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const abortControllerRef = useRef<AbortController | null>(null);

  // Cache management
  const getCachedData = useCallback(() => {
    try {
      const cached = localStorage.getItem(CACHE_KEY);
      if (cached) {
        const { data: cachedData, timestamp } = JSON.parse(cached);
        if (Date.now() - timestamp < CACHE_DURATION) {
          return cachedData;
        }
      }
    } catch (error) {
      console.warn('Failed to parse cached transactions data:', error);
    }
    return null;
  }, []);

  const setCachedData = useCallback((transactionsData: Transaction[]) => {
    try {
      localStorage.setItem(CACHE_KEY, JSON.stringify({
        data: transactionsData,
        timestamp: Date.now()
      }));
    } catch (error) {
      console.warn('Failed to cache transactions data:', error);
    }
  }, []);

  const fetchTransactionsData = useCallback(async (isInitialLoad = false, showToast = false) => {
    if (!apiKey || !apiSecret) {
      setError("API keys not configured");
      setIsLoading(false);
      return;
    }

    // Cancel previous request
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    // Create new abort controller
    abortControllerRef.current = new AbortController();

    try {
      if (isInitialLoad) {
        // Try to load from cache first
        const cachedData = getCachedData();
        if (cachedData) {
          setData(cachedData);
          setIsLoading(false);
          setError(null);
        }
      }

      if (!isInitialLoad && data.length > 0) {
        setIsRefreshing(true);
      } else {
        setIsLoading(true);
      }

      setError(null);

      const response = await fetch('/api/binance/recent-trades', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ apiKey, apiSecret }),
        signal: abortControllerRef.current.signal,
      });

      if (!response.ok) {
        let serverMessage = `HTTP error! status: ${response.status}`;
        try {
          const errorResult = await response.json();
          if (errorResult?.message) {
            serverMessage = errorResult.message;
          }
        } catch (e) {
          serverMessage = response.statusText || serverMessage;
        }
        throw new Error(serverMessage);
      }

      const result = await response.json();
      
      if (result.success && result.data?.transactions) {
        const transactionsData = result.data.transactions;
        setData(transactionsData);
        setCachedData(transactionsData);
        setError(null);
        setLastUpdated(new Date());

        if (showToast) {
          toast({
            title: "Transactions Updated",
            description: "Your transaction history has been refreshed successfully.",
            duration: 2000,
          });
        }
      } else {
        throw new Error(result.message || 'Failed to fetch transactions data');
      }
    } catch (err: any) {
      if (err.name === 'AbortError') {
        return; // Request was cancelled, don't update state
      }

      const errorMessage = `Error fetching transactions: ${err.message}`;
      setError(errorMessage);
      console.error('Transactions fetch error:', err);

      if (showToast) {
        toast({
          title: "Update Failed",
          description: errorMessage,
          variant: "destructive",
          duration: 3000,
        });
      }
    } finally {
      setIsLoading(false);
      setIsRefreshing(false);
    }
  }, [apiKey, apiSecret, data, getCachedData, setCachedData]);

  const refetch = useCallback(async () => {
    await fetchTransactionsData(false, true);
  }, [fetchTransactionsData]);

  // Initial load
  useEffect(() => {
    if (apiKey && apiSecret) {
      fetchTransactionsData(true);
    } else {
      setIsLoading(false);
      setError("API keys not configured");
    }
  }, [apiKey, apiSecret]);

  // Auto-refresh setup
  useEffect(() => {
    if (!autoRefresh || !apiKey || !apiSecret) {
      return;
    }

    intervalRef.current = setInterval(() => {
      fetchTransactionsData(false);
    }, refreshInterval);

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [autoRefresh, apiKey, apiSecret, refreshInterval, fetchTransactionsData]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, []);

  return {
    data,
    isLoading,
    error,
    isRefreshing,
    refetch,
    lastUpdated,
  };
}
