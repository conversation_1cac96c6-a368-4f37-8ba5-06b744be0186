"use client";

import { useState } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { CryptoIcon } from "@/components/ui/crypto-icon";
import type { PortfolioAsset } from "@/lib/types";
import { ArrowUpRight, ArrowDownRight, Search, ArrowUpDown, ArrowUp, ArrowDown } from "lucide-react";
import { cn } from "@/lib/utils";

interface AssetTableProps {
  assets: PortfolioAsset[];
}

type SortField = "name" | "value" | "change24h" | "quantity";
type SortDirection = "asc" | "desc";

export function AssetTable({ assets }: AssetTableProps) {
  const [searchTerm, setSearchTerm] = useState("");
  const [sortField, setSortField] = useState<SortField>("value");
  const [sortDirection, setSortDirection] = useState<SortDirection>("desc");

  const formatCurrency = (value: number): string => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(value);
  };

  const formatQuantity = (quantity: number): string => {
    if (quantity === 0) return "0";
    if (quantity < 0.01) {
      return quantity.toLocaleString(undefined, {
        minimumFractionDigits: 2,
        maximumFractionDigits: 8,
      });
    }
    if (quantity < 1) {
      return quantity.toLocaleString(undefined, {
        minimumFractionDigits: 2,
        maximumFractionDigits: 6,
      });
    }
    return quantity.toLocaleString(undefined, {
      minimumFractionDigits: 2,
      maximumFractionDigits: 4,
    });
  };

  const formatPercentage = (value: number): string => {
    return `${value >= 0 ? '+' : ''}${value.toFixed(2)}%`;
  };

  const handleSort = (field: SortField) => {
    if (sortField === field) {
      setSortDirection(sortDirection === "asc" ? "desc" : "asc");
    } else {
      setSortField(field);
      setSortDirection("desc");
    }
  };

  const getSortIcon = (field: SortField) => {
    if (sortField !== field) {
      return <ArrowUpDown className="h-4 w-4 text-muted-foreground" />;
    }
    return sortDirection === "asc" 
      ? <ArrowUp className="h-4 w-4 text-primary" />
      : <ArrowDown className="h-4 w-4 text-primary" />;
  };

  const filteredAndSortedAssets = assets
    .filter(asset => 
      asset.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      asset.symbol.toLowerCase().includes(searchTerm.toLowerCase())
    )
    .sort((a, b) => {
      let aValue: number;
      let bValue: number;

      switch (sortField) {
        case "name":
          return sortDirection === "asc" 
            ? a.name.localeCompare(b.name)
            : b.name.localeCompare(a.name);
        case "value":
          aValue = a.value;
          bValue = b.value;
          break;
        case "change24h":
          aValue = a.change24h;
          bValue = b.change24h;
          break;
        case "quantity":
          aValue = a.quantity;
          bValue = b.quantity;
          break;
        default:
          return 0;
      }

      return sortDirection === "asc" ? aValue - bValue : bValue - aValue;
    });

  if (!assets || assets.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="mx-auto w-24 h-24 bg-muted rounded-full flex items-center justify-center mb-4">
          <Search className="h-8 w-8 text-muted-foreground" />
        </div>
        <h3 className="text-lg font-semibold mb-2">No Assets Found</h3>
        <p className="text-muted-foreground">
          No cryptocurrency assets were found in your portfolio.
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Search and Filters */}
      <div className="flex items-center justify-between gap-4">
        <div className="relative flex-1 max-w-sm">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
          <Input
            placeholder="Search assets..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-9 form-input"
          />
        </div>
        <div className="flex items-center gap-2">
          <Badge variant="outline" className="text-xs">
            {filteredAndSortedAssets.length} assets
          </Badge>
        </div>
      </div>

      {/* Table */}
      <div className="rounded-lg border overflow-hidden">
        <Table className="table-trading">
          <TableHeader>
            <TableRow>
              <TableHead className="w-[300px]">
                <Button 
                  variant="ghost" 
                  size="sm" 
                  onClick={() => handleSort("name")}
                  className="h-auto p-0 font-semibold hover:text-primary"
                >
                  Asset
                  {getSortIcon("name")}
                </Button>
              </TableHead>
              <TableHead className="text-right">
                <Button 
                  variant="ghost" 
                  size="sm" 
                  onClick={() => handleSort("quantity")}
                  className="h-auto p-0 font-semibold hover:text-primary"
                >
                  Holdings
                  {getSortIcon("quantity")}
                </Button>
              </TableHead>
              <TableHead className="text-right">Price</TableHead>
              <TableHead className="text-right">
                <Button 
                  variant="ghost" 
                  size="sm" 
                  onClick={() => handleSort("value")}
                  className="h-auto p-0 font-semibold hover:text-primary"
                >
                  Value
                  {getSortIcon("value")}
                </Button>
              </TableHead>
              <TableHead className="text-right">
                <Button 
                  variant="ghost" 
                  size="sm" 
                  onClick={() => handleSort("change24h")}
                  className="h-auto p-0 font-semibold hover:text-primary"
                >
                  24h Change
                  {getSortIcon("change24h")}
                </Button>
              </TableHead>
              <TableHead className="text-right">Exchange</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredAndSortedAssets.map((asset) => (
              <TableRow key={asset.id} className="hover:bg-muted/30 transition-colors">
                <TableCell>
                  <div className="flex items-center gap-3">
                    <CryptoIcon symbol={asset.symbol} size={32} />
                    <div>
                      <div className="font-semibold text-sm">{asset.name}</div>
                      <div className="text-xs text-muted-foreground font-mono">
                        {asset.symbol}
                      </div>
                    </div>
                  </div>
                </TableCell>
                <TableCell className="text-right">
                  <div className="font-mono text-sm">
                    {formatQuantity(asset.quantity)}
                  </div>
                  <div className="text-xs text-muted-foreground">
                    {asset.symbol}
                  </div>
                </TableCell>
                <TableCell className="text-right">
                  <div className="font-mono text-sm">
                    {formatCurrency(asset.price)}
                  </div>
                </TableCell>
                <TableCell className="text-right">
                  <div className="font-mono text-sm font-semibold">
                    {formatCurrency(asset.value)}
                  </div>
                </TableCell>
                <TableCell className="text-right">
                  <div className={cn(
                    "flex items-center justify-end gap-1 text-sm font-medium",
                    asset.change24h >= 0 ? "profit-text" : "loss-text"
                  )}>
                    {asset.change24h >= 0 ? (
                      <ArrowUpRight className="h-3 w-3" />
                    ) : (
                      <ArrowDownRight className="h-3 w-3" />
                    )}
                    <span className="font-mono">
                      {formatPercentage(asset.change24h)}
                    </span>
                  </div>
                </TableCell>
                <TableCell className="text-right">
                  <Badge 
                    variant="outline" 
                    className="text-xs font-mono"
                  >
                    {asset.exchange}
                  </Badge>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      {filteredAndSortedAssets.length === 0 && searchTerm && (
        <div className="text-center py-8">
          <p className="text-muted-foreground">
            No assets found matching &quot;{searchTerm}&quot;
          </p>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setSearchTerm("")}
            className="mt-2"
          >
            Clear search
          </Button>
        </div>
      )}
    </div>
  );
}
