<!--
@license
Copyright 2018 Google LLC
SPDX-License-Identifier: Apache-2.0
-->
<!doctype html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1">
  <link rel="icon" href='data:image/svg+xml;utf8,<svg fill="none" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 48 48"><path d="m14 7 10-7 10 7v10h5v7h-5l5 24H9l5-24H9v-7h5V7Z" fill="%23F63"/><path d="M31.561 24H14l-1.689 8.105L31.561 24ZM18.983 48H9l1.022-4.907L35.723 32.27l1.663 7.98L18.983 48Z" fill="%23FFA385"/><path fill="%23FF3" d="M20.5 10h7v7h-7z"/></svg>'>
  <title>Lighthouse Report</title>
  <style>body {margin: 0}</style>
</head>
<body>
  <noscript>Lighthouse report requires JavaScript. Please enable.</noscript>

  <div id="lh-log"></div>

  <script>window.__LIGHTHOUSE_JSON__ = {"lighthouseVersion":"12.6.0","requestedUrl":"http://localhost:3000/","mainDocumentUrl":"http://localhost:3000/","finalDisplayedUrl":"http://localhost:3000/","finalUrl":"http://localhost:3000/","fetchTime":"2025-06-01T23:31:52.769Z","gatherMode":"navigation","runWarnings":[],"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","environment":{"networkUserAgent":"Mozilla/5.0 (Linux; Android 11; moto g power (2022)) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36","hostUserAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","benchmarkIndex":3137,"credits":{}},"audits":{"viewport":{"id":"viewport","title":"Has a `\u003cmeta name=\"viewport\">` tag with `width` or `initial-scale`","description":"A `\u003cmeta name=\"viewport\">` not only optimizes your app for mobile screen sizes, but also prevents [a 300 millisecond delay to user input](https://developer.chrome.com/blog/300ms-tap-delay-gone-away/). [Learn more about using the viewport meta tag](https://developer.chrome.com/docs/lighthouse/pwa/viewport/).","score":1,"scoreDisplayMode":"metricSavings","warnings":[],"metricSavings":{"INP":0},"details":{"type":"debugdata","viewportContent":"width=device-width, initial-scale=1"},"guidanceLevel":3},"first-contentful-paint":{"id":"first-contentful-paint","title":"First Contentful Paint","description":"First Contentful Paint marks the time at which the first text or image is painted. [Learn more about the First Contentful Paint metric](https://developer.chrome.com/docs/lighthouse/performance/first-contentful-paint/).","score":1,"scoreDisplayMode":"numeric","numericValue":817.4305999999999,"numericUnit":"millisecond","displayValue":"0.8 s","scoringOptions":{"p10":1800,"median":3000}},"largest-contentful-paint":{"id":"largest-contentful-paint","title":"Largest Contentful Paint","description":"Largest Contentful Paint marks the time at which the largest text or image is painted. [Learn more about the Largest Contentful Paint metric](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)","score":1,"scoreDisplayMode":"numeric","numericValue":1267.4306,"numericUnit":"millisecond","displayValue":"1.3 s","scoringOptions":{"p10":2500,"median":4000}},"first-meaningful-paint":{"id":"first-meaningful-paint","title":"First Meaningful Paint","description":"First Meaningful Paint measures when the primary content of a page is visible. [Learn more about the First Meaningful Paint metric](https://developer.chrome.com/docs/lighthouse/performance/first-meaningful-paint/).","score":null,"scoreDisplayMode":"notApplicable"},"speed-index":{"id":"speed-index","title":"Speed Index","description":"Speed Index shows how quickly the contents of a page are visibly populated. [Learn more about the Speed Index metric](https://developer.chrome.com/docs/lighthouse/performance/speed-index/).","score":0.98,"scoreDisplayMode":"numeric","numericValue":2373.445213996282,"numericUnit":"millisecond","displayValue":"2.4 s","scoringOptions":{"p10":3387,"median":5800}},"screenshot-thumbnails":{"id":"screenshot-thumbnails","title":"Screenshot Thumbnails","description":"This is what the load of your site looked like.","score":1,"scoreDisplayMode":"informative","details":{"type":"filmstrip","scale":3000,"items":[{"timing":375,"timestamp":127855499773,"data":"data:image/jpeg;base64,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"},{"timing":750,"timestamp":127855874773,"data":"data:image/jpeg;base64,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"},{"timing":1125,"timestamp":127856249773,"data":"data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wCEABALDA4MChAODQ4SERATGCgaGBYWGDEjJR0oOjM9PDkzODdASFxOQERXRTc4UG1RV19iZ2hnPk1xeXBkeFxlZ2MBERISGBUYLxoaL2NCOEJjY2NjY2NjY2NjY2NjY2NjY2NjY2NjY2NjY2NjY2NjY2NjY2NjY2NjY2NjY2NjY2NjY//AABEIAfIA+gMBEQACEQEDEQH/xAGiAAABBQEBAQEBAQAAAAAAAAAAAQIDBAUGBwgJCgsQAAIBAwMCBAMFBQQEAAABfQECAwAEEQUSITFBBhNRYQcicRQygZGhCCNCscEVUtHwJDNicoIJChYXGBkaJSYnKCkqNDU2Nzg5OkNERUZHSElKU1RVVldYWVpjZGVmZ2hpanN0dXZ3eHl6g4SFhoeIiYqSk5SVlpeYmZqio6Slpqeoqaqys7S1tre4ubrCw8TFxsfIycrS09TV1tfY2drh4uPk5ebn6Onq8fLz9PX29/j5+gEAAwEBAQEBAQEBAQAAAAAAAAECAwQFBgcICQoLEQACAQIEBAMEBwUEBAABAncAAQIDEQQFITEGEkFRB2FxEyIygQgUQpGhscEJIzNS8BVictEKFiQ04SXxFxgZGiYnKCkqNTY3ODk6Q0RFRkdISUpTVFVWV1hZWmNkZWZnaGlqc3R1dnd4eXqCg4SFhoeIiYqSk5SVlpeYmZqio6Slpqeoqaqys7S1tre4ubrCw8TFxsfIycrS09TV1tfY2dri4+Tl5ufo6ery8/T19vf4+fr/2gAMAwEAAhEDEQA/APQKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoA//9k="},{"timing":1500,"timestamp":127856624773,"data":"data:image/jpeg;base64,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"},{"timing":1875,"timestamp":127856999773,"data":"data:image/jpeg;base64,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"},{"timing":2250,"timestamp":127857374773,"data":"data:image/jpeg;base64,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"},{"timing":2625,"timestamp":127857749773,"data":"data:image/jpeg;base64,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"},{"timing":3000,"timestamp":127858124773,"data":"data:image/jpeg;base64,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"}]}},"final-screenshot":{"id":"final-screenshot","title":"Final Screenshot","description":"The last screenshot captured of the pageload.","score":1,"scoreDisplayMode":"informative","details":{"type":"screenshot","timing":1959,"timestamp":127857083611,"data":"data:image/jpeg;base64,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"}},"total-blocking-time":{"id":"total-blocking-time","title":"Total Blocking Time","description":"Sum of all time periods between FCP and Time to Interactive, when task length exceeded 50ms, expressed in milliseconds. [Learn more about the Total Blocking Time metric](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-total-blocking-time/).","score":1,"scoreDisplayMode":"numeric","numericValue":0,"numericUnit":"millisecond","displayValue":"0 ms","scoringOptions":{"p10":200,"median":600}},"max-potential-fid":{"id":"max-potential-fid","title":"Max Potential First Input Delay","description":"The maximum potential First Input Delay that your users could experience is the duration of the longest task. [Learn more about the Maximum Potential First Input Delay metric](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-max-potential-fid/).","score":1,"scoreDisplayMode":"numeric","numericValue":16,"numericUnit":"millisecond","displayValue":"20 ms"},"cumulative-layout-shift":{"id":"cumulative-layout-shift","title":"Cumulative Layout Shift","description":"Cumulative Layout Shift measures the movement of visible elements within the viewport. [Learn more about the Cumulative Layout Shift metric](https://web.dev/articles/cls).","score":1,"scoreDisplayMode":"numeric","numericValue":0,"numericUnit":"unitless","displayValue":"0","scoringOptions":{"p10":0.1,"median":0.25},"details":{"type":"debugdata","items":[{"cumulativeLayoutShiftMainFrame":0,"newEngineResult":{"cumulativeLayoutShift":0,"cumulativeLayoutShiftMainFrame":0},"newEngineResultDiffered":false}]}},"server-response-time":{"id":"server-response-time","title":"Initial server response time was short","description":"Keep the server response time for the main document short because all other requests depend on it. [Learn more about the Time to First Byte metric](https://developer.chrome.com/docs/lighthouse/performance/time-to-first-byte/).","score":1,"scoreDisplayMode":"metricSavings","numericValue":2.662,"numericUnit":"millisecond","displayValue":"Root document took 0 ms","metricSavings":{"FCP":0,"LCP":0},"details":{"type":"opportunity","headings":[{"key":"url","valueType":"url","label":"URL"},{"key":"responseTime","valueType":"timespanMs","label":"Time Spent"}],"items":[{"url":"http://localhost:3000/","responseTime":2.662}],"overallSavingsMs":0},"guidanceLevel":1},"interactive":{"id":"interactive","title":"Time to Interactive","description":"Time to Interactive is the amount of time it takes for the page to become fully interactive. [Learn more about the Time to Interactive metric](https://developer.chrome.com/docs/lighthouse/performance/interactive/).","score":1,"scoreDisplayMode":"numeric","numericValue":1267.4306,"numericUnit":"millisecond","displayValue":"1.3 s"},"user-timings":{"id":"user-timings","title":"User Timing marks and measures","description":"Consider instrumenting your app with the User Timing API to measure your app's real-world performance during key user experiences. [Learn more about User Timing marks](https://developer.chrome.com/docs/lighthouse/performance/user-timings/).","score":null,"scoreDisplayMode":"notApplicable","details":{"type":"table","headings":[],"items":[]},"guidanceLevel":2},"critical-request-chains":{"id":"critical-request-chains","title":"Avoid chaining critical requests","description":"The Critical Request Chains below show you what resources are loaded with a high priority. Consider reducing the length of chains, reducing the download size of resources, or deferring the download of unnecessary resources to improve page load. [Learn how to avoid chaining critical requests](https://developer.chrome.com/docs/lighthouse/performance/critical-request-chains/).","score":1,"scoreDisplayMode":"informative","displayValue":"1 chain found","details":{"type":"criticalrequestchain","chains":{"4A903CD56B025E6FE2F117B206BF68A0":{"request":{"url":"http://localhost:3000/","startTime":127855.126912,"endTime":127855.130971,"responseReceivedTime":127855.130588,"transferSize":7780},"children":{"14476.2":{"request":{"url":"http://localhost:3000/_next/static/css/cd2484d7a951a5bf.css","startTime":127855.138935,"endTime":127855.167647,"responseReceivedTime":127855.138935,"transferSize":0}}}}},"longestChain":{"duration":40.73499999940395,"length":2,"transferSize":0}},"guidanceLevel":1},"redirects":{"id":"redirects","title":"Avoid multiple page redirects","description":"Redirects introduce additional delays before the page can be loaded. [Learn how to avoid page redirects](https://developer.chrome.com/docs/lighthouse/performance/redirects/).","score":1,"scoreDisplayMode":"metricSavings","numericValue":0,"numericUnit":"millisecond","displayValue":"","metricSavings":{"LCP":0,"FCP":0},"details":{"type":"opportunity","headings":[],"items":[],"overallSavingsMs":0},"guidanceLevel":2},"mainthread-work-breakdown":{"id":"mainthread-work-breakdown","title":"Minimizes main-thread work","description":"Consider reducing the time spent parsing, compiling and executing JS. You may find delivering smaller JS payloads helps with this. [Learn how to minimize main-thread work](https://developer.chrome.com/docs/lighthouse/performance/mainthread-work-breakdown/)","score":1,"scoreDisplayMode":"metricSavings","numericValue":253.08000000000007,"numericUnit":"millisecond","displayValue":"0.3 s","metricSavings":{"TBT":0},"details":{"type":"table","headings":[{"key":"groupLabel","valueType":"text","label":"Category"},{"key":"duration","valueType":"ms","granularity":1,"label":"Time Spent"}],"items":[{"group":"other","groupLabel":"Other","duration":99.67200000000007},{"group":"styleLayout","groupLabel":"Style & Layout","duration":86.528},{"group":"paintCompositeRender","groupLabel":"Rendering","duration":42.335999999999984},{"group":"parseHTML","groupLabel":"Parse HTML & CSS","duration":11.219999999999999},{"group":"scriptEvaluation","groupLabel":"Script Evaluation","duration":10.167999999999997},{"group":"scriptParseCompile","groupLabel":"Script Parsing & Compilation","duration":3.1560000000000006}],"sortedBy":["duration"]},"guidanceLevel":1},"bootup-time":{"id":"bootup-time","title":"JavaScript execution time","description":"Consider reducing the time spent parsing, compiling, and executing JS. You may find delivering smaller JS payloads helps with this. [Learn how to reduce Javascript execution time](https://developer.chrome.com/docs/lighthouse/performance/bootup-time/).","score":1,"scoreDisplayMode":"metricSavings","numericValue":11.488,"numericUnit":"millisecond","displayValue":"0.0 s","metricSavings":{"TBT":0},"details":{"type":"table","headings":[{"key":"url","valueType":"url","label":"URL"},{"key":"total","granularity":1,"valueType":"ms","label":"Total CPU Time"},{"key":"scripting","granularity":1,"valueType":"ms","label":"Script Evaluation"},{"key":"scriptParseCompile","granularity":1,"valueType":"ms","label":"Script Parse"}],"items":[{"url":"http://localhost:3000/","total":164.352,"scripting":5.055999999999999,"scriptParseCompile":3.1560000000000006},{"url":"Unattributable","total":84.97200000000011,"scripting":3.276,"scriptParseCompile":0}],"summary":{"wastedMs":11.488},"sortedBy":["total"]},"guidanceLevel":1},"uses-rel-preconnect":{"id":"uses-rel-preconnect","title":"Preconnect to required origins","description":"Consider adding `preconnect` or `dns-prefetch` resource hints to establish early connections to important third-party origins. [Learn how to preconnect to required origins](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/).","score":1,"scoreDisplayMode":"metricSavings","warnings":["A `\u003clink rel=preconnect>` was found for \"https://fonts.googleapis.com\" but was not used by the browser. Only use `preconnect` for important origins that the page will certainly request.","A `\u003clink rel=preconnect>` was found for \"https://fonts.gstatic.com\" but was not used by the browser. Only use `preconnect` for important origins that the page will certainly request."],"metricSavings":{"LCP":0,"FCP":0},"guidanceLevel":3},"font-display":{"id":"font-display","title":"All text remains visible during webfont loads","description":"Leverage the `font-display` CSS feature to ensure text is user-visible while webfonts are loading. [Learn more about `font-display`](https://developer.chrome.com/docs/lighthouse/performance/font-display/).","score":1,"scoreDisplayMode":"metricSavings","warnings":[],"details":{"type":"table","headings":[],"items":[]},"guidanceLevel":3},"diagnostics":{"id":"diagnostics","title":"Diagnostics","description":"Collection of useful page vitals.","score":1,"scoreDisplayMode":"informative","details":{"type":"debugdata","items":[{"numRequests":10,"numScripts":7,"numStylesheets":1,"numFonts":0,"numTasks":265,"numTasksOver10ms":1,"numTasksOver25ms":1,"numTasksOver50ms":0,"numTasksOver100ms":0,"numTasksOver500ms":0,"rtt":0.10469999999999999,"throughput":262749292.9881202,"maxRtt":0.10469999999999999,"maxServerLatency":33.7153,"totalByteWeight":26266,"totalTaskTime":63.27000000000003,"mainDocumentTransferSize":7780}]}},"network-requests":{"id":"network-requests","title":"Network Requests","description":"Lists the network requests that were made during page load.","score":1,"scoreDisplayMode":"informative","details":{"type":"table","headings":[{"key":"url","valueType":"url","label":"URL"},{"key":"protocol","valueType":"text","label":"Protocol"},{"key":"networkRequestTime","valueType":"ms","granularity":1,"label":"Network Request Time"},{"key":"networkEndTime","valueType":"ms","granularity":1,"label":"Network End Time"},{"key":"transferSize","valueType":"bytes","displayUnit":"kb","granularity":1,"label":"Transfer Size"},{"key":"resourceSize","valueType":"bytes","displayUnit":"kb","granularity":1,"label":"Resource Size"},{"key":"statusCode","valueType":"text","label":"Status Code"},{"key":"mimeType","valueType":"text","label":"MIME Type"},{"key":"resourceType","valueType":"text","label":"Resource Type"}],"items":[{"url":"http://localhost:3000/","sessionTargetType":"page","protocol":"http/1.1","rendererStartTime":0,"networkRequestTime":0.9380000084638596,"networkEndTime":4.997000008821487,"finished":true,"transferSize":7780,"resourceSize":41130,"statusCode":200,"mimeType":"text/html","resourceType":"Document","priority":"VeryHigh","experimentalFromMainFrame":true,"entity":"localhost"},{"url":"http://localhost:3000/_next/static/css/cd2484d7a951a5bf.css","sessionTargetType":"page","protocol":"","rendererStartTime":12.961000010371208,"networkRequestTime":12.961000010371208,"networkEndTime":41.67300000786781,"finished":true,"transferSize":0,"resourceSize":0,"statusCode":-1,"mimeType":"","resourceType":"Stylesheet","priority":"VeryHigh","experimentalFromMainFrame":true,"entity":"localhost"},{"url":"http://localhost:3000/_next/static/chunks/webpack-4b467fbbb448f7f8.js","sessionTargetType":"page","protocol":"http/1.1","rendererStartTime":13.29899999499321,"networkRequestTime":13.840000003576279,"networkEndTime":77.7080000191927,"finished":true,"transferSize":421,"resourceSize":0,"statusCode":400,"mimeType":"text/html","resourceType":"Script","priority":"Low","isLinkPreload":true,"experimentalFromMainFrame":true,"entity":"localhost"},{"url":"http://localhost:3000/_next/static/chunks/vendor-7620e74be0c75f43.js","sessionTargetType":"page","protocol":"http/1.1","rendererStartTime":13.40600000321865,"networkRequestTime":16.46000000834465,"networkEndTime":77.99099999666214,"finished":true,"transferSize":421,"resourceSize":0,"statusCode":400,"mimeType":"text/html","resourceType":"Script","priority":"Low","experimentalFromMainFrame":true,"entity":"localhost"},{"url":"http://localhost:3000/_next/static/chunks/main-app-a0aeb003551427a4.js","sessionTargetType":"page","protocol":"http/1.1","rendererStartTime":13.499000012874603,"networkRequestTime":17.87500001490116,"networkEndTime":78.22100000083447,"finished":true,"transferSize":421,"resourceSize":0,"statusCode":400,"mimeType":"text/html","resourceType":"Script","priority":"Low","experimentalFromMainFrame":true,"entity":"localhost"},{"url":"http://localhost:3000/_next/static/chunks/ui-537441ee1c5c416a.js","sessionTargetType":"page","protocol":"http/1.1","rendererStartTime":13.586000010371208,"networkRequestTime":18.057000011205673,"networkEndTime":78.44900001585484,"finished":true,"transferSize":421,"resourceSize":0,"statusCode":400,"mimeType":"text/html","resourceType":"Script","priority":"Low","experimentalFromMainFrame":true,"entity":"localhost"},{"url":"http://localhost:3000/_next/static/chunks/common-ae20474526a6d508.js","sessionTargetType":"page","protocol":"http/1.1","rendererStartTime":13.666000008583069,"networkRequestTime":18.24500000476837,"networkEndTime":78.88600000739098,"finished":true,"transferSize":421,"resourceSize":0,"statusCode":400,"mimeType":"text/html","resourceType":"Script","priority":"Low","experimentalFromMainFrame":true,"entity":"localhost"},{"url":"http://localhost:3000/_next/static/chunks/app/layout-9689786e996d8ffa.js","sessionTargetType":"page","protocol":"http/1.1","rendererStartTime":13.734000012278557,"networkRequestTime":41.93200001120567,"networkEndTime":83.05600000917912,"finished":true,"transferSize":421,"resourceSize":0,"statusCode":400,"mimeType":"text/html","resourceType":"Script","priority":"Low","experimentalFromMainFrame":true,"entity":"localhost"},{"url":"http://localhost:3000/_next/static/chunks/app/page-e906fda5115b8117.js","sessionTargetType":"page","protocol":"http/1.1","rendererStartTime":13.806000009179115,"networkRequestTime":62.275000005960464,"networkEndTime":83.29999999701977,"finished":true,"transferSize":421,"resourceSize":0,"statusCode":400,"mimeType":"text/html","resourceType":"Script","priority":"Low","experimentalFromMainFrame":true,"entity":"localhost"},{"url":"http://localhost:3000/favicon.ico","sessionTargetType":"page","protocol":"http/1.1","rendererStartTime":85.733000010252,"networkRequestTime":86.15600000321865,"networkEndTime":90.14000000059605,"finished":true,"transferSize":15539,"resourceSize":15086,"statusCode":200,"mimeType":"image/x-icon","resourceType":"Other","priority":"High","experimentalFromMainFrame":true,"entity":"localhost"}],"debugData":{"type":"debugdata","networkStartTimeTs":127855125973.99998}}},"network-rtt":{"id":"network-rtt","title":"Network Round Trip Times","description":"Network round trip times (RTT) have a large impact on performance. If the RTT to an origin is high, it's an indication that servers closer to the user could improve performance. [Learn more about the Round Trip Time](https://hpbn.co/primer-on-latency-and-bandwidth/).","score":1,"scoreDisplayMode":"informative","numericValue":0.10469999999999999,"numericUnit":"millisecond","displayValue":"0 ms","details":{"type":"table","headings":[{"key":"origin","valueType":"text","label":"URL"},{"key":"rtt","valueType":"ms","granularity":1,"label":"Time Spent"}],"items":[{"origin":"http://localhost:3000","rtt":0.10469999999999999}],"sortedBy":["rtt"]}},"network-server-latency":{"id":"network-server-latency","title":"Server Backend Latencies","description":"Server latencies can impact web performance. If the server latency of an origin is high, it's an indication the server is overloaded or has poor backend performance. [Learn more about server response time](https://hpbn.co/primer-on-web-performance/#analyzing-the-resource-waterfall).","score":1,"scoreDisplayMode":"informative","numericValue":33.7153,"numericUnit":"millisecond","displayValue":"30 ms","details":{"type":"table","headings":[{"key":"origin","valueType":"text","label":"URL"},{"key":"serverResponseTime","valueType":"ms","granularity":1,"label":"Time Spent"}],"items":[{"origin":"http://localhost:3000","serverResponseTime":33.7153}],"sortedBy":["serverResponseTime"]}},"main-thread-tasks":{"id":"main-thread-tasks","title":"Tasks","description":"Lists the toplevel main thread tasks that executed during page load.","score":1,"scoreDisplayMode":"informative","details":{"type":"table","headings":[{"key":"startTime","valueType":"ms","granularity":1,"label":"Start Time"},{"key":"duration","valueType":"ms","granularity":1,"label":"End Time"}],"items":[{"duration":7.411,"startTime":8.07},{"duration":28.402,"startTime":50.045}]}},"metrics":{"id":"metrics","title":"Metrics","description":"Collects all available metrics.","score":1,"scoreDisplayMode":"informative","numericValue":1267,"numericUnit":"millisecond","details":{"type":"debugdata","items":[{"firstContentfulPaint":817,"largestContentfulPaint":1267,"interactive":1267,"speedIndex":2373,"totalBlockingTime":0,"maxPotentialFID":16,"cumulativeLayoutShift":0,"cumulativeLayoutShiftMainFrame":0,"timeToFirstByte":484,"observedTimeOrigin":0,"observedTimeOriginTs":127855124773,"observedNavigationStart":0,"observedNavigationStartTs":127855124773,"observedFirstPaint":1648,"observedFirstPaintTs":127856773258,"observedFirstContentfulPaint":1648,"observedFirstContentfulPaintTs":127856773258,"observedFirstContentfulPaintAllFrames":1648,"observedFirstContentfulPaintAllFramesTs":127856773258,"observedLargestContentfulPaint":1648,"observedLargestContentfulPaintTs":127856773258,"observedLargestContentfulPaintAllFrames":1648,"observedLargestContentfulPaintAllFramesTs":127856773258,"observedTraceEnd":4005,"observedTraceEndTs":127859129799,"observedLoad":85,"observedLoadTs":127855209507,"observedDomContentLoaded":48,"observedDomContentLoadedTs":127855172344,"observedCumulativeLayoutShift":0,"observedCumulativeLayoutShiftMainFrame":0,"observedFirstVisualChange":1428,"observedFirstVisualChangeTs":127856552773,"observedLastVisualChange":1645,"observedLastVisualChangeTs":127856769773,"observedSpeedIndex":1462,"observedSpeedIndexTs":127856586539},{"lcpInvalidated":false}]}},"resource-summary":{"id":"resource-summary","title":"Resources Summary","description":"Aggregates all network requests and groups them by type","score":1,"scoreDisplayMode":"informative","details":{"type":"table","headings":[{"key":"label","valueType":"text","label":"Resource Type"},{"key":"requestCount","valueType":"numeric","label":"Requests"},{"key":"transferSize","valueType":"bytes","label":"Transfer Size"}],"items":[{"resourceType":"total","label":"Total","requestCount":9,"transferSize":10727},{"resourceType":"document","label":"Document","requestCount":1,"transferSize":7780},{"resourceType":"script","label":"Script","requestCount":7,"transferSize":2947},{"resourceType":"stylesheet","label":"Stylesheet","requestCount":1,"transferSize":0},{"resourceType":"image","label":"Image","requestCount":0,"transferSize":0},{"resourceType":"media","label":"Media","requestCount":0,"transferSize":0},{"resourceType":"font","label":"Font","requestCount":0,"transferSize":0},{"resourceType":"other","label":"Other","requestCount":0,"transferSize":0},{"resourceType":"third-party","label":"Third-party","requestCount":0,"transferSize":0}]}},"third-party-summary":{"id":"third-party-summary","title":"Minimize third-party usage","description":"Third-party code can significantly impact load performance. Limit the number of redundant third-party providers and try to load third-party code after your page has primarily finished loading. [Learn how to minimize third-party impact](https://developers.google.com/web/fundamentals/performance/optimizing-content-efficiency/loading-third-party-javascript/).","score":null,"scoreDisplayMode":"notApplicable","metricSavings":{"TBT":0},"guidanceLevel":1},"third-party-facades":{"id":"third-party-facades","title":"Lazy load third-party resources with facades","description":"Some third-party embeds can be lazy loaded. Consider replacing them with a facade until they are required. [Learn how to defer third-parties with a facade](https://developer.chrome.com/docs/lighthouse/performance/third-party-facades/).","score":null,"scoreDisplayMode":"notApplicable","metricSavings":{"TBT":0},"guidanceLevel":3},"largest-contentful-paint-element":{"id":"largest-contentful-paint-element","title":"Largest Contentful Paint element","description":"This is the largest contentful element painted within the viewport. [Learn more about the Largest Contentful Paint element](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)","score":1,"scoreDisplayMode":"informative","displayValue":"1,270 ms","metricSavings":{"LCP":0},"details":{"type":"list","items":[{"type":"table","headings":[{"key":"node","valueType":"node","label":"Element"}],"items":[{"node":{"type":"node","lhId":"page-0-H1","path":"1,HTML,1,BODY,1,DIV,0,DIV,1,DIV,0,DIV,0,DIV,0,A,1,DIV,0,H1","selector":"div.flex > a.flex > div.group-data-[collapsible=icon]:hidden > h1.text-2xl","boundingRect":{"top":57,"bottom":94,"left":8,"right":404,"width":396,"height":37},"snippet":"\u003ch1 class=\"text-2xl font-bold\">","nodeLabel":"CryptoPilot"}}]},{"type":"table","headings":[{"key":"phase","valueType":"text","label":"Phase"},{"key":"percent","valueType":"text","label":"% of LCP"},{"key":"timing","valueType":"ms","label":"Timing"}],"items":[{"phase":"TTFB","timing":483.7153,"percent":"38%"},{"phase":"Load Delay","timing":0,"percent":"0%"},{"phase":"Load Time","timing":0,"percent":"0%"},{"phase":"Render Delay","timing":783.7152999999998,"percent":"62%"}]}]},"guidanceLevel":1},"lcp-lazy-loaded":{"id":"lcp-lazy-loaded","title":"Largest Contentful Paint image was not lazily loaded","description":"Above-the-fold images that are lazily loaded render later in the page lifecycle, which can delay the largest contentful paint. [Learn more about optimal lazy loading](https://web.dev/articles/lcp-lazy-loading).","score":null,"scoreDisplayMode":"notApplicable","metricSavings":{"LCP":0},"guidanceLevel":3},"layout-shifts":{"id":"layout-shifts","title":"Avoid large layout shifts","description":"These are the largest layout shifts observed on the page. Each table item represents a single layout shift, and shows the element that shifted the most. Below each item are possible root causes that led to the layout shift. Some of these layout shifts may not be included in the CLS metric value due to [windowing](https://web.dev/articles/cls#what_is_cls). [Learn how to improve CLS](https://web.dev/articles/optimize-cls)","score":null,"scoreDisplayMode":"notApplicable","metricSavings":{"CLS":0},"details":{"type":"table","headings":[],"items":[]},"guidanceLevel":2},"long-tasks":{"id":"long-tasks","title":"Avoid long main-thread tasks","description":"Lists the longest tasks on the main thread, useful for identifying worst contributors to input delay. [Learn how to avoid long main-thread tasks](https://web.dev/articles/optimize-long-tasks)","score":1,"scoreDisplayMode":"informative","displayValue":"1 long task found","metricSavings":{"TBT":0},"details":{"type":"table","headings":[{"key":"url","valueType":"url","label":"URL"},{"key":"startTime","valueType":"ms","granularity":1,"label":"Start Time"},{"key":"duration","valueType":"ms","granularity":1,"label":"Duration"}],"items":[{"url":"http://localhost:3000/","duration":57,"startTime":639.7153}],"sortedBy":["duration"],"skipSumming":["startTime"],"debugData":{"type":"debugdata","urls":["http://localhost:3000/"],"tasks":[{"urlIndex":0,"startTime":639.7,"duration":57,"other":57,"paintCompositeRender":0,"styleLayout":0}]}},"guidanceLevel":1},"non-composited-animations":{"id":"non-composited-animations","title":"Avoid non-composited animations","description":"Animations which are not composited can be janky and increase CLS. [Learn how to avoid non-composited animations](https://developer.chrome.com/docs/lighthouse/performance/non-composited-animations/)","score":null,"scoreDisplayMode":"notApplicable","metricSavings":{"CLS":0},"details":{"type":"table","headings":[],"items":[]},"guidanceLevel":2},"unsized-images":{"id":"unsized-images","title":"Image elements have explicit `width` and `height`","description":"Set an explicit width and height on image elements to reduce layout shifts and improve CLS. [Learn how to set image dimensions](https://web.dev/articles/optimize-cls#images_without_dimensions)","score":1,"scoreDisplayMode":"metricSavings","metricSavings":{"CLS":0},"details":{"type":"table","headings":[],"items":[]},"guidanceLevel":4},"prioritize-lcp-image":{"id":"prioritize-lcp-image","title":"Preload Largest Contentful Paint image","description":"If the LCP element is dynamically added to the page, you should preload the image in order to improve LCP. [Learn more about preloading LCP elements](https://web.dev/articles/optimize-lcp#optimize_when_the_resource_is_discovered).","score":null,"scoreDisplayMode":"notApplicable","metricSavings":{"LCP":0},"guidanceLevel":4},"script-treemap-data":{"id":"script-treemap-data","title":"Script Treemap Data","description":"Used for treemap app","score":1,"scoreDisplayMode":"informative","details":{"type":"treemap-data","nodes":[{"name":"http://localhost:3000/","resourceBytes":5937,"encodedBytes":1046,"children":[{"name":"(inline) document.addEve…","resourceBytes":151,"unusedBytes":0},{"name":"(inline) (self.__next_f=…","resourceBytes":43,"unusedBytes":0},{"name":"(inline) self.__next_f.p…","resourceBytes":1229,"unusedBytes":0},{"name":"(inline) self.__next_f.p…","resourceBytes":3966,"unusedBytes":0},{"name":"(inline) self.__next_f.p…","resourceBytes":38,"unusedBytes":0},{"name":"(inline) self.__next_f.p…","resourceBytes":186,"unusedBytes":0},{"name":"(inline) self.__next_f.p…","resourceBytes":324,"unusedBytes":0}]}]}},"uses-long-cache-ttl":{"id":"uses-long-cache-ttl","title":"Uses efficient cache policy on static assets","description":"A long cache lifetime can speed up repeat visits to your page. [Learn more about efficient cache policies](https://developer.chrome.com/docs/lighthouse/performance/uses-long-cache-ttl/).","score":1,"scoreDisplayMode":"metricSavings","numericValue":0,"numericUnit":"byte","displayValue":"0 resources found","details":{"type":"table","headings":[],"items":[]},"guidanceLevel":3},"total-byte-weight":{"id":"total-byte-weight","title":"Avoids enormous network payloads","description":"Large network payloads cost users real money and are highly correlated with long load times. [Learn how to reduce payload sizes](https://developer.chrome.com/docs/lighthouse/performance/total-byte-weight/).","score":1,"scoreDisplayMode":"metricSavings","numericValue":26266,"numericUnit":"byte","displayValue":"Total size was 26 KiB","details":{"type":"table","headings":[{"key":"url","valueType":"url","label":"URL"},{"key":"totalBytes","valueType":"bytes","label":"Transfer Size"}],"items":[{"url":"http://localhost:3000/favicon.ico","totalBytes":15539},{"url":"http://localhost:3000/","totalBytes":7780},{"url":"http://localhost:3000/_next/static/chunks/app/layout-9689786e996d8ffa.js","totalBytes":421},{"url":"http://localhost:3000/_next/static/chunks/app/page-e906fda5115b8117.js","totalBytes":421},{"url":"http://localhost:3000/_next/static/chunks/common-ae20474526a6d508.js","totalBytes":421},{"url":"http://localhost:3000/_next/static/chunks/main-app-a0aeb003551427a4.js","totalBytes":421},{"url":"http://localhost:3000/_next/static/chunks/ui-537441ee1c5c416a.js","totalBytes":421},{"url":"http://localhost:3000/_next/static/chunks/vendor-7620e74be0c75f43.js","totalBytes":421},{"url":"http://localhost:3000/_next/static/chunks/webpack-4b467fbbb448f7f8.js","totalBytes":421}],"sortedBy":["totalBytes"]},"guidanceLevel":1},"offscreen-images":{"id":"offscreen-images","title":"Defer offscreen images","description":"Consider lazy-loading offscreen and hidden images after all critical resources have finished loading to lower time to interactive. [Learn how to defer offscreen images](https://developer.chrome.com/docs/lighthouse/performance/offscreen-images/).","score":1,"scoreDisplayMode":"metricSavings","numericValue":0,"numericUnit":"millisecond","displayValue":"","warnings":[],"metricSavings":{"FCP":0,"LCP":0},"details":{"type":"opportunity","headings":[],"items":[],"overallSavingsMs":0,"overallSavingsBytes":0,"sortedBy":["wastedBytes"],"debugData":{"type":"debugdata","metricSavings":{"FCP":0,"LCP":0}}},"guidanceLevel":2},"render-blocking-resources":{"id":"render-blocking-resources","title":"Eliminate render-blocking resources","description":"Resources are blocking the first paint of your page. Consider delivering critical JS/CSS inline and deferring all non-critical JS/styles. [Learn how to eliminate render-blocking resources](https://developer.chrome.com/docs/lighthouse/performance/render-blocking-resources/).","score":1,"scoreDisplayMode":"metricSavings","numericValue":0,"numericUnit":"millisecond","metricSavings":{"FCP":0,"LCP":0},"details":{"type":"opportunity","headings":[],"items":[],"overallSavingsMs":0},"guidanceLevel":2},"unminified-css":{"id":"unminified-css","title":"Minify CSS","description":"Minifying CSS files can reduce network payload sizes. [Learn how to minify CSS](https://developer.chrome.com/docs/lighthouse/performance/unminified-css/).","score":1,"scoreDisplayMode":"metricSavings","numericValue":0,"numericUnit":"millisecond","displayValue":"","metricSavings":{"FCP":0,"LCP":0},"details":{"type":"opportunity","headings":[],"items":[],"overallSavingsMs":0,"overallSavingsBytes":0,"sortedBy":["wastedBytes"],"debugData":{"type":"debugdata","metricSavings":{"FCP":0,"LCP":0}}},"guidanceLevel":3},"unminified-javascript":{"id":"unminified-javascript","title":"Minify JavaScript","description":"Minifying JavaScript files can reduce payload sizes and script parse time. [Learn how to minify JavaScript](https://developer.chrome.com/docs/lighthouse/performance/unminified-javascript/).","score":1,"scoreDisplayMode":"metricSavings","numericValue":0,"numericUnit":"millisecond","displayValue":"","warnings":[],"metricSavings":{"FCP":0,"LCP":0},"details":{"type":"opportunity","headings":[],"items":[],"overallSavingsMs":0,"overallSavingsBytes":0,"sortedBy":["wastedBytes"],"debugData":{"type":"debugdata","metricSavings":{"FCP":0,"LCP":0}}},"guidanceLevel":3},"unused-css-rules":{"id":"unused-css-rules","title":"Reduce unused CSS","description":"Reduce unused rules from stylesheets and defer CSS not used for above-the-fold content to decrease bytes consumed by network activity. [Learn how to reduce unused CSS](https://developer.chrome.com/docs/lighthouse/performance/unused-css-rules/).","score":1,"scoreDisplayMode":"metricSavings","numericValue":0,"numericUnit":"millisecond","displayValue":"","metricSavings":{"FCP":0,"LCP":0},"details":{"type":"opportunity","headings":[],"items":[],"overallSavingsMs":0,"overallSavingsBytes":0,"sortedBy":["wastedBytes"],"debugData":{"type":"debugdata","metricSavings":{"FCP":0,"LCP":0}}},"guidanceLevel":1},"unused-javascript":{"id":"unused-javascript","title":"Reduce unused JavaScript","description":"Reduce unused JavaScript and defer loading scripts until they are required to decrease bytes consumed by network activity. [Learn how to reduce unused JavaScript](https://developer.chrome.com/docs/lighthouse/performance/unused-javascript/).","score":1,"scoreDisplayMode":"metricSavings","numericValue":0,"numericUnit":"millisecond","displayValue":"","metricSavings":{"FCP":0,"LCP":0},"details":{"type":"opportunity","headings":[],"items":[],"overallSavingsMs":0,"overallSavingsBytes":0,"sortedBy":["wastedBytes"],"debugData":{"type":"debugdata","metricSavings":{"FCP":0,"LCP":0}}},"guidanceLevel":1},"modern-image-formats":{"id":"modern-image-formats","title":"Serve images in next-gen formats","description":"Image formats like WebP and AVIF often provide better compression than PNG or JPEG, which means faster downloads and less data consumption. [Learn more about modern image formats](https://developer.chrome.com/docs/lighthouse/performance/uses-webp-images/).","score":1,"scoreDisplayMode":"metricSavings","numericValue":0,"numericUnit":"millisecond","displayValue":"","warnings":[],"metricSavings":{"FCP":0,"LCP":0},"details":{"type":"opportunity","headings":[],"items":[],"overallSavingsMs":0,"overallSavingsBytes":0,"sortedBy":["wastedBytes"],"debugData":{"type":"debugdata","metricSavings":{"FCP":0,"LCP":0}}},"guidanceLevel":3},"uses-optimized-images":{"id":"uses-optimized-images","title":"Efficiently encode images","description":"Optimized images load faster and consume less cellular data. [Learn how to efficiently encode images](https://developer.chrome.com/docs/lighthouse/performance/uses-optimized-images/).","score":1,"scoreDisplayMode":"metricSavings","numericValue":0,"numericUnit":"millisecond","displayValue":"","warnings":[],"metricSavings":{"FCP":0,"LCP":0},"details":{"type":"opportunity","headings":[],"items":[],"overallSavingsMs":0,"overallSavingsBytes":0,"sortedBy":["wastedBytes"],"debugData":{"type":"debugdata","metricSavings":{"FCP":0,"LCP":0}}},"guidanceLevel":2},"uses-text-compression":{"id":"uses-text-compression","title":"Enable text compression","description":"Text-based resources should be served with compression (gzip, deflate or brotli) to minimize total network bytes. [Learn more about text compression](https://developer.chrome.com/docs/lighthouse/performance/uses-text-compression/).","score":1,"scoreDisplayMode":"metricSavings","numericValue":0,"numericUnit":"millisecond","displayValue":"","metricSavings":{"FCP":0,"LCP":0},"details":{"type":"opportunity","headings":[],"items":[],"overallSavingsMs":0,"overallSavingsBytes":0,"sortedBy":["wastedBytes"],"debugData":{"type":"debugdata","metricSavings":{"FCP":0,"LCP":0}}},"guidanceLevel":3},"uses-responsive-images":{"id":"uses-responsive-images","title":"Properly size images","description":"Serve images that are appropriately-sized to save cellular data and improve load time. [Learn how to size images](https://developer.chrome.com/docs/lighthouse/performance/uses-responsive-images/).","score":1,"scoreDisplayMode":"metricSavings","numericValue":0,"numericUnit":"millisecond","displayValue":"","metricSavings":{"FCP":0,"LCP":0},"details":{"type":"opportunity","headings":[],"items":[],"overallSavingsMs":0,"overallSavingsBytes":0,"sortedBy":["wastedBytes"],"debugData":{"type":"debugdata","metricSavings":{"FCP":0,"LCP":0}}},"guidanceLevel":2},"efficient-animated-content":{"id":"efficient-animated-content","title":"Use video formats for animated content","description":"Large GIFs are inefficient for delivering animated content. Consider using MPEG4/WebM videos for animations and PNG/WebP for static images instead of GIF to save network bytes. [Learn more about efficient video formats](https://developer.chrome.com/docs/lighthouse/performance/efficient-animated-content/)","score":1,"scoreDisplayMode":"metricSavings","numericValue":0,"numericUnit":"millisecond","displayValue":"","metricSavings":{"FCP":0,"LCP":0},"details":{"type":"opportunity","headings":[],"items":[],"overallSavingsMs":0,"overallSavingsBytes":0,"sortedBy":["wastedBytes"],"debugData":{"type":"debugdata","metricSavings":{"FCP":0,"LCP":0}}},"guidanceLevel":3},"duplicated-javascript":{"id":"duplicated-javascript","title":"Remove duplicate modules in JavaScript bundles","description":"Remove large, duplicate JavaScript modules from bundles to reduce unnecessary bytes consumed by network activity. ","score":1,"scoreDisplayMode":"metricSavings","numericValue":0,"numericUnit":"millisecond","displayValue":"","metricSavings":{"FCP":0,"LCP":0},"details":{"type":"opportunity","headings":[],"items":[],"overallSavingsMs":0,"overallSavingsBytes":0,"sortedBy":["wastedBytes"],"debugData":{"type":"debugdata","metricSavings":{"FCP":0,"LCP":0}}},"guidanceLevel":2},"legacy-javascript":{"id":"legacy-javascript","title":"Avoid serving legacy JavaScript to modern browsers","description":"Polyfills and transforms enable legacy browsers to use new JavaScript features. However, many aren't necessary for modern browsers. Consider modifying your JavaScript build process to not transpile [Baseline](https://web.dev/baseline) features, unless you know you must support legacy browsers. [Learn why most sites can deploy ES6+ code without transpiling](https://philipwalton.com/articles/the-state-of-es5-on-the-web/)","score":1,"scoreDisplayMode":"metricSavings","numericValue":0,"numericUnit":"millisecond","displayValue":"","warnings":[],"metricSavings":{"FCP":0,"LCP":0},"details":{"type":"opportunity","headings":[],"items":[],"overallSavingsMs":0,"overallSavingsBytes":0,"sortedBy":["wastedBytes"],"debugData":{"type":"debugdata","metricSavings":{"FCP":0,"LCP":0}}},"guidanceLevel":2},"dom-size":{"id":"dom-size","title":"Avoids an excessive DOM size","description":"A large DOM will increase memory usage, cause longer [style calculations](https://developers.google.com/web/fundamentals/performance/rendering/reduce-the-scope-and-complexity-of-style-calculations), and produce costly [layout reflows](https://developers.google.com/speed/articles/reflow). [Learn how to avoid an excessive DOM size](https://developer.chrome.com/docs/lighthouse/performance/dom-size/).","score":1,"scoreDisplayMode":"metricSavings","numericValue":251,"numericUnit":"element","displayValue":"251 elements","metricSavings":{"TBT":0},"details":{"type":"table","headings":[{"key":"statistic","valueType":"text","label":"Statistic"},{"key":"node","valueType":"node","label":"Element"},{"key":"value","valueType":"numeric","label":"Value"}],"items":[{"statistic":"Total DOM Elements","value":{"type":"numeric","granularity":1,"value":251}},{"node":{"type":"node","lhId":"1-0-rect","path":"1,HTML,1,BODY,1,DIV,0,DIV,1,DIV,0,DIV,1,DIV,0,DIV,1,DIV,0,DIV,0,DIV,0,DIV,1,DIV,0,LI,0,A,0,DIV,0,svg,0,rect","selector":"a.peer/menu-button > div.relative > svg.lucide > rect","boundingRect":{"top":231,"bottom":240,"left":11,"right":18,"width":7,"height":9},"snippet":"\u003crect width=\"7\" height=\"9\" x=\"3\" y=\"3\" rx=\"1\">","nodeLabel":"a.peer/menu-button > div.relative > svg.lucide > rect"},"statistic":"Maximum DOM Depth","value":{"type":"numeric","granularity":1,"value":17}},{"node":{"type":"node","lhId":"1-1-BODY","path":"1,HTML,1,BODY","selector":"body.__variable_5cfdac","boundingRect":{"top":8,"bottom":1534,"left":8,"right":404,"width":396,"height":1526},"snippet":"\u003cbody class=\"__variable_5cfdac __variable_9a8899 antialiased transform-gpu loaded\">","nodeLabel":"body.__variable_5cfdac"},"statistic":"Maximum Child Elements","value":{"type":"numeric","granularity":1,"value":10}}]},"guidanceLevel":1},"no-document-write":{"id":"no-document-write","title":"Avoids `document.write()`","description":"For users on slow connections, external scripts dynamically injected via `document.write()` can delay page load by tens of seconds. [Learn how to avoid document.write()](https://developer.chrome.com/docs/lighthouse/best-practices/no-document-write/).","score":1,"scoreDisplayMode":"metricSavings","details":{"type":"table","headings":[],"items":[]},"guidanceLevel":2},"uses-http2":{"id":"uses-http2","title":"Use HTTP/2","description":"HTTP/2 offers many benefits over HTTP/1.1, including binary headers and multiplexing. [Learn more about HTTP/2](https://developer.chrome.com/docs/lighthouse/best-practices/uses-http2/).","score":1,"scoreDisplayMode":"metricSavings","numericValue":0,"numericUnit":"millisecond","metricSavings":{"LCP":0,"FCP":0},"details":{"type":"opportunity","headings":[],"items":[],"overallSavingsMs":0},"guidanceLevel":3},"uses-passive-event-listeners":{"id":"uses-passive-event-listeners","title":"Uses passive listeners to improve scrolling performance","description":"Consider marking your touch and wheel event listeners as `passive` to improve your page's scroll performance. [Learn more about adopting passive event listeners](https://developer.chrome.com/docs/lighthouse/best-practices/uses-passive-event-listeners/).","score":1,"scoreDisplayMode":"metricSavings","details":{"type":"table","headings":[],"items":[]},"guidanceLevel":3},"bf-cache":{"id":"bf-cache","title":"Page didn't prevent back/forward cache restoration","description":"Many navigations are performed by going back to a previous page, or forwards again. The back/forward cache (bfcache) can speed up these return navigations. [Learn more about the bfcache](https://developer.chrome.com/docs/lighthouse/performance/bf-cache/)","score":1,"scoreDisplayMode":"binary","guidanceLevel":4},"cache-insight":{"id":"cache-insight","title":"Use efficient cache lifetimes","description":"A long cache lifetime can speed up repeat visits to your page. [Learn more](https://web.dev/uses-long-cache-ttl/).","score":null,"scoreDisplayMode":"notApplicable","guidanceLevel":3,"replacesAudits":["uses-long-cache-ttl"]},"cls-culprits-insight":{"id":"cls-culprits-insight","title":"Layout shift culprits","description":"Layout shifts occur when elements move absent any user interaction. [Investigate the causes of layout shifts](https://web.dev/articles/optimize-cls), such as elements being added, removed, or their fonts changing as the page loads.","score":1,"scoreDisplayMode":"numeric","metricSavings":{"CLS":0},"details":{"type":"list","items":[]},"guidanceLevel":3,"replacesAudits":["layout-shifts","non-composited-animations","unsized-images"]},"document-latency-insight":{"id":"document-latency-insight","title":"Document request latency","description":"Your first network request is the most important.  Reduce its latency by avoiding redirects, ensuring a fast server response, and enabling text compression.","score":1,"scoreDisplayMode":"metricSavings","metricSavings":{"FCP":0,"LCP":0},"details":{"type":"checklist","items":{"noRedirects":{"label":"Avoids redirects","value":true},"serverResponseIsFast":{"label":"Server responds quickly","value":true},"usesCompression":{"label":"Applies text compression","value":true}}},"guidanceLevel":3,"replacesAudits":["redirects","server-response-time","uses-text-compression"]},"dom-size-insight":{"id":"dom-size-insight","title":"Optimize DOM size","description":"A large DOM can increase the duration of style calculations and layout reflows, impacting page responsiveness. A large DOM will also increase memory usage. [Learn how to avoid an excessive DOM size](https://developer.chrome.com/docs/lighthouse/performance/dom-size/).","score":1,"scoreDisplayMode":"numeric","metricSavings":{"INP":0},"details":{"type":"table","headings":[{"key":"statistic","valueType":"text","label":"Statistic"},{"key":"node","valueType":"node","label":"Element"},{"key":"value","valueType":"numeric","label":"Value"}],"items":[{"statistic":"Total elements","value":{"type":"numeric","granularity":1,"value":253}},{"statistic":"Most children","node":{"type":"node","lhId":"page-2-BODY","path":"1,HTML,1,BODY","selector":"body.__variable_5cfdac","boundingRect":{"top":8,"bottom":1534,"left":8,"right":404,"width":396,"height":1526},"snippet":"\u003cbody class=\"__variable_5cfdac __variable_9a8899 antialiased transform-gpu loaded\">","nodeLabel":"body.__variable_5cfdac"},"value":{"type":"numeric","granularity":1,"value":10}},{"statistic":"DOM depth","node":{"type":"node","lhId":"page-3-rect","path":"1,HTML,1,BODY,1,DIV,0,DIV,1,DIV,0,DIV,1,DIV,0,DIV,1,DIV,0,DIV,0,DIV,0,DIV,1,DIV,0,LI,0,A,0,DIV,0,svg,0,rect","selector":"a.peer/menu-button > div.relative > svg.lucide > rect","boundingRect":{"top":231,"bottom":240,"left":11,"right":18,"width":7,"height":9},"snippet":"\u003crect width=\"7\" height=\"9\" x=\"3\" y=\"3\" rx=\"1\">","nodeLabel":"a.peer/menu-button > div.relative > svg.lucide > rect"},"value":{"type":"numeric","granularity":1,"value":17}}]},"guidanceLevel":3,"replacesAudits":["dom-size"]},"duplicated-javascript-insight":{"id":"duplicated-javascript-insight","title":"Duplicated JavaScript","description":"Remove large, duplicate JavaScript modules from bundles to reduce unnecessary bytes consumed by network activity.","score":null,"scoreDisplayMode":"notApplicable","guidanceLevel":2,"replacesAudits":["duplicated-javascript"]},"font-display-insight":{"id":"font-display-insight","title":"Font display","description":"Consider setting [font-display](https://developer.chrome.com/blog/font-display) to swap or optional to ensure text is consistently visible. swap can be further optimized to mitigate layout shifts with [font metric overrides](https://developer.chrome.com/blog/font-fallbacks).","score":null,"scoreDisplayMode":"notApplicable","guidanceLevel":3,"replacesAudits":["font-display"]},"forced-reflow-insight":{"id":"forced-reflow-insight","title":"Forced reflow","description":"Many APIs, typically reading layout geometry, force the rendering engine to pause script execution in order to calculate the style and layout. Learn more about [forced reflow](https://developers.google.com/web/fundamentals/performance/rendering/avoid-large-complex-layouts-and-layout-thrashing#avoid-forced-synchronous-layouts) and its mitigations.","score":1,"scoreDisplayMode":"numeric","details":{"type":"list","items":[{"type":"table","headings":[],"items":[]}]},"guidanceLevel":3},"image-delivery-insight":{"id":"image-delivery-insight","title":"Improve image delivery","description":"Reducing the download time of images can improve the perceived load time of the page and LCP. [Learn more about optimizing image size](https://developer.chrome.com/docs/lighthouse/performance/uses-optimized-images/)","score":null,"scoreDisplayMode":"notApplicable","guidanceLevel":3,"replacesAudits":["modern-image-formats","uses-optimized-images","efficient-animated-content","uses-responsive-images"]},"interaction-to-next-paint-insight":{"id":"interaction-to-next-paint-insight","title":"INP by phase","description":"Start investigating with the longest phase. [Delays can be minimized](https://web.dev/articles/optimize-inp#optimize_interactions). To reduce processing duration, [optimize the main-thread costs](https://web.dev/articles/optimize-long-tasks), often JS.","score":null,"scoreDisplayMode":"notApplicable","guidanceLevel":3,"replacesAudits":["work-during-interaction"]},"lcp-discovery-insight":{"id":"lcp-discovery-insight","title":"LCP request discovery","description":"Optimize LCP by making the LCP image [discoverable](https://web.dev/articles/optimize-lcp#1_eliminate_resource_load_delay) from the HTML immediately, and [avoiding lazy-loading](https://web.dev/articles/lcp-lazy-loading)","score":null,"scoreDisplayMode":"notApplicable","guidanceLevel":3,"replacesAudits":["prioritize-lcp-image","lcp-lazy-loaded"]},"lcp-phases-insight":{"id":"lcp-phases-insight","title":"LCP by phase","description":"Each [phase has specific improvement strategies](https://web.dev/articles/optimize-lcp#lcp-breakdown). Ideally, most of the LCP time should be spent on loading the resources, not within delays.","score":1,"scoreDisplayMode":"informative","metricSavings":{"LCP":0},"details":{"type":"list","items":[{"type":"table","headings":[{"key":"label","valueType":"text","label":"Phase"},{"key":"duration","valueType":"ms","label":"Duration"}],"items":[{"phase":"timeToFirstByte","label":"Time to first byte","duration":5.661},{"phase":"elementRenderDelay","label":"Element render delay","duration":1642.8239999999998}]},{"type":"node","lhId":"page-0-H1","path":"1,HTML,1,BODY,1,DIV,0,DIV,1,DIV,0,DIV,0,DIV,0,A,1,DIV,0,H1","selector":"div.flex > a.flex > div.group-data-[collapsible=icon]:hidden > h1.text-2xl","boundingRect":{"top":57,"bottom":94,"left":8,"right":404,"width":396,"height":37},"snippet":"\u003ch1 class=\"text-2xl font-bold\">","nodeLabel":"CryptoPilot"}]},"guidanceLevel":3,"replacesAudits":["largest-contentful-paint-element"]},"legacy-javascript-insight":{"id":"legacy-javascript-insight","title":"Legacy JavaScript","description":"Polyfills and transforms enable legacy browsers to use new JavaScript features. However, many aren't necessary for modern browsers. Consider modifying your JavaScript build process to not transpile [Baseline](https://web.dev/articles/baseline-and-polyfills) features, unless you know you must support legacy browsers. [Learn why most sites can deploy ES6+ code without transpiling](https://philipwalton.com/articles/the-state-of-es5-on-the-web/)","score":null,"scoreDisplayMode":"notApplicable","guidanceLevel":2},"modern-http-insight":{"id":"modern-http-insight","title":"Modern HTTP","description":"HTTP/2 and HTTP/3 offer many benefits over HTTP/1.1, such as multiplexing. [Learn more about using modern HTTP](https://developer.chrome.com/docs/lighthouse/best-practices/uses-http2/).","score":null,"scoreDisplayMode":"notApplicable","guidanceLevel":3},"network-dependency-tree-insight":{"id":"network-dependency-tree-insight","title":"Network dependency tree","description":"[Avoid chaining critical requests](https://developer.chrome.com/docs/lighthouse/performance/critical-request-chains) by reducing the length of chains, reducing the download size of resources, or deferring the download of unnecessary resources to improve page load.","score":1,"scoreDisplayMode":"numeric","metricSavings":{"LCP":0},"details":{"type":"network-tree","chains":{"4A903CD56B025E6FE2F117B206BF68A0":{"url":"http://localhost:3000/","navStartToEndTime":16,"transferSize":7780,"isLongest":true,"children":{}}},"longestChain":{"duration":16}},"guidanceLevel":1,"replacesAudits":["critical-request-chains"]},"render-blocking-insight":{"id":"render-blocking-insight","title":"Render blocking requests","description":"Requests are blocking the page's initial render, which may delay LCP. [Deferring or inlining](https://web.dev/learn/performance/understanding-the-critical-path#render-blocking_resources/) can move these network requests out of the critical path.","score":null,"scoreDisplayMode":"notApplicable","guidanceLevel":3,"replacesAudits":["render-blocking-resources"]},"third-parties-insight":{"id":"third-parties-insight","title":"3rd parties","description":"3rd party code can significantly impact load performance. [Reduce and defer loading of 3rd party code](https://developers.google.com/web/fundamentals/performance/optimizing-content-efficiency/loading-third-party-javascript/) to prioritize your page's content.","score":null,"scoreDisplayMode":"notApplicable","guidanceLevel":3,"replacesAudits":["third-party-summary"]},"viewport-insight":{"id":"viewport-insight","title":"Optimize viewport for mobile","description":"Tap interactions may be [delayed by up to 300 ms](https://developer.chrome.com/blog/300ms-tap-delay-gone-away/) if the viewport is not optimized for mobile.","score":1,"scoreDisplayMode":"numeric","metricSavings":{"INP":0},"details":{"type":"table","headings":[{"key":"node","valueType":"node","label":""}],"items":[{"node":{"type":"node","lhId":"page-1-META","path":"1,HTML,0,HEAD,1,META","selector":"head > meta","boundingRect":{"top":0,"bottom":0,"left":0,"right":0,"width":0,"height":0},"snippet":"\u003cmeta name=\"viewport\" content=\"width=device-width, initial-scale=1\">","nodeLabel":"head > meta"}}]},"guidanceLevel":3,"replacesAudits":["viewport"]}},"configSettings":{"output":["html"],"maxWaitForFcp":30000,"maxWaitForLoad":45000,"pauseAfterFcpMs":1000,"pauseAfterLoadMs":1000,"networkQuietThresholdMs":1000,"cpuQuietThresholdMs":1000,"formFactor":"mobile","throttling":{"rttMs":150,"throughputKbps":1638.4,"requestLatencyMs":562.5,"downloadThroughputKbps":1474.5600000000002,"uploadThroughputKbps":675,"cpuSlowdownMultiplier":4},"throttlingMethod":"simulate","screenEmulation":{"mobile":true,"width":412,"height":823,"deviceScaleFactor":1.75,"disabled":false},"emulatedUserAgent":"Mozilla/5.0 (Linux; Android 11; moto g power (2022)) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36","auditMode":false,"gatherMode":false,"clearStorageTypes":["file_systems","shader_cache","service_workers","cache_storage"],"disableStorageReset":false,"debugNavigation":false,"channel":"cli","usePassiveGathering":false,"disableFullPageScreenshot":false,"skipAboutBlank":false,"blankPage":"about:blank","ignoreStatusCode":false,"locale":"en-US","blockedUrlPatterns":null,"additionalTraceCategories":null,"extraHeaders":null,"precomputedLanternData":null,"onlyAudits":null,"onlyCategories":["performance"],"skipAudits":null},"categories":{"performance":{"title":"Performance","supportedModes":["navigation","timespan","snapshot"],"auditRefs":[{"id":"first-contentful-paint","weight":10,"group":"metrics","acronym":"FCP"},{"id":"largest-contentful-paint","weight":25,"group":"metrics","acronym":"LCP"},{"id":"total-blocking-time","weight":30,"group":"metrics","acronym":"TBT"},{"id":"cumulative-layout-shift","weight":25,"group":"metrics","acronym":"CLS"},{"id":"speed-index","weight":10,"group":"metrics","acronym":"SI"},{"id":"cache-insight","weight":0,"group":"hidden"},{"id":"cls-culprits-insight","weight":0,"group":"hidden"},{"id":"document-latency-insight","weight":0,"group":"hidden"},{"id":"dom-size-insight","weight":0,"group":"hidden"},{"id":"duplicated-javascript-insight","weight":0,"group":"hidden"},{"id":"font-display-insight","weight":0,"group":"hidden"},{"id":"forced-reflow-insight","weight":0,"group":"hidden"},{"id":"image-delivery-insight","weight":0,"group":"hidden"},{"id":"interaction-to-next-paint-insight","weight":0,"group":"hidden"},{"id":"lcp-discovery-insight","weight":0,"group":"hidden"},{"id":"lcp-phases-insight","weight":0,"group":"hidden"},{"id":"legacy-javascript-insight","weight":0,"group":"hidden"},{"id":"modern-http-insight","weight":0,"group":"hidden"},{"id":"network-dependency-tree-insight","weight":0,"group":"hidden"},{"id":"render-blocking-insight","weight":0,"group":"hidden"},{"id":"third-parties-insight","weight":0,"group":"hidden"},{"id":"viewport-insight","weight":0,"group":"hidden"},{"id":"interactive","weight":0,"group":"hidden","acronym":"TTI"},{"id":"max-potential-fid","weight":0,"group":"hidden"},{"id":"first-meaningful-paint","weight":0,"acronym":"FMP","group":"hidden"},{"id":"render-blocking-resources","weight":0,"group":"diagnostics"},{"id":"uses-responsive-images","weight":0,"group":"diagnostics"},{"id":"offscreen-images","weight":0,"group":"diagnostics"},{"id":"unminified-css","weight":0,"group":"diagnostics"},{"id":"unminified-javascript","weight":0,"group":"diagnostics"},{"id":"unused-css-rules","weight":0,"group":"diagnostics"},{"id":"unused-javascript","weight":0,"group":"diagnostics"},{"id":"uses-optimized-images","weight":0,"group":"diagnostics"},{"id":"modern-image-formats","weight":0,"group":"diagnostics"},{"id":"uses-text-compression","weight":0,"group":"diagnostics"},{"id":"uses-rel-preconnect","weight":0,"group":"diagnostics"},{"id":"server-response-time","weight":0,"group":"diagnostics"},{"id":"redirects","weight":0,"group":"diagnostics"},{"id":"uses-http2","weight":0,"group":"diagnostics"},{"id":"efficient-animated-content","weight":0,"group":"diagnostics"},{"id":"duplicated-javascript","weight":0,"group":"diagnostics"},{"id":"legacy-javascript","weight":0,"group":"diagnostics"},{"id":"prioritize-lcp-image","weight":0,"group":"diagnostics"},{"id":"total-byte-weight","weight":0,"group":"diagnostics"},{"id":"uses-long-cache-ttl","weight":0,"group":"diagnostics"},{"id":"dom-size","weight":0,"group":"diagnostics"},{"id":"critical-request-chains","weight":0,"group":"diagnostics"},{"id":"user-timings","weight":0,"group":"diagnostics"},{"id":"bootup-time","weight":0,"group":"diagnostics"},{"id":"mainthread-work-breakdown","weight":0,"group":"diagnostics"},{"id":"font-display","weight":0,"group":"diagnostics"},{"id":"third-party-summary","weight":0,"group":"diagnostics"},{"id":"third-party-facades","weight":0,"group":"diagnostics"},{"id":"largest-contentful-paint-element","weight":0,"group":"diagnostics"},{"id":"lcp-lazy-loaded","weight":0,"group":"diagnostics"},{"id":"layout-shifts","weight":0,"group":"diagnostics"},{"id":"uses-passive-event-listeners","weight":0,"group":"diagnostics"},{"id":"no-document-write","weight":0,"group":"diagnostics"},{"id":"long-tasks","weight":0,"group":"diagnostics"},{"id":"non-composited-animations","weight":0,"group":"diagnostics"},{"id":"unsized-images","weight":0,"group":"diagnostics"},{"id":"viewport","weight":0,"group":"diagnostics"},{"id":"bf-cache","weight":0,"group":"diagnostics"},{"id":"network-requests","weight":0,"group":"hidden"},{"id":"network-rtt","weight":0,"group":"hidden"},{"id":"network-server-latency","weight":0,"group":"hidden"},{"id":"main-thread-tasks","weight":0,"group":"hidden"},{"id":"diagnostics","weight":0,"group":"hidden"},{"id":"metrics","weight":0,"group":"hidden"},{"id":"screenshot-thumbnails","weight":0,"group":"hidden"},{"id":"final-screenshot","weight":0,"group":"hidden"},{"id":"script-treemap-data","weight":0,"group":"hidden"},{"id":"resource-summary","weight":0,"group":"hidden"}],"id":"performance","score":1}},"categoryGroups":{"metrics":{"title":"Metrics"},"insights":{"title":"Insights","description":"These insights are also available in the Chrome DevTools Performance Panel - [record a trace](https://developer.chrome.com/docs/devtools/performance/reference) to view more detailed information."},"diagnostics":{"title":"Diagnostics","description":"More information about the performance of your application. These numbers don't [directly affect](https://developer.chrome.com/docs/lighthouse/performance/performance-scoring/) the Performance score."},"a11y-best-practices":{"title":"Best practices","description":"These items highlight common accessibility best practices."},"a11y-color-contrast":{"title":"Contrast","description":"These are opportunities to improve the legibility of your content."},"a11y-names-labels":{"title":"Names and labels","description":"These are opportunities to improve the semantics of the controls in your application. This may enhance the experience for users of assistive technology, like a screen reader."},"a11y-navigation":{"title":"Navigation","description":"These are opportunities to improve keyboard navigation in your application."},"a11y-aria":{"title":"ARIA","description":"These are opportunities to improve the usage of ARIA in your application which may enhance the experience for users of assistive technology, like a screen reader."},"a11y-language":{"title":"Internationalization and localization","description":"These are opportunities to improve the interpretation of your content by users in different locales."},"a11y-audio-video":{"title":"Audio and video","description":"These are opportunities to provide alternative content for audio and video. This may improve the experience for users with hearing or vision impairments."},"a11y-tables-lists":{"title":"Tables and lists","description":"These are opportunities to improve the experience of reading tabular or list data using assistive technology, like a screen reader."},"seo-mobile":{"title":"Mobile Friendly","description":"Make sure your pages are mobile friendly so users don’t have to pinch or zoom in order to read the content pages. [Learn how to make pages mobile-friendly](https://developers.google.com/search/mobile-sites/)."},"seo-content":{"title":"Content Best Practices","description":"Format your HTML in a way that enables crawlers to better understand your app’s content."},"seo-crawl":{"title":"Crawling and Indexing","description":"To appear in search results, crawlers need access to your app."},"best-practices-trust-safety":{"title":"Trust and Safety"},"best-practices-ux":{"title":"User Experience"},"best-practices-browser-compat":{"title":"Browser Compatibility"},"best-practices-general":{"title":"General"},"hidden":{"title":""}},"stackPacks":[],"entities":[{"name":"localhost","origins":["http://localhost:3000"],"isFirstParty":true,"isUnrecognized":true}],"fullPageScreenshot":{"screenshot":{"data":"data:image/webp;base64,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","width":412,"height":1550},"nodes":{"page-0-H1":{"id":"","top":57,"bottom":94,"left":8,"right":404,"width":396,"height":37},"page-1-META":{"id":"","top":0,"bottom":0,"left":0,"right":0,"width":0,"height":0},"page-2-BODY":{"id":"","top":8,"bottom":1534,"left":8,"right":404,"width":396,"height":1526},"page-3-rect":{"id":"","top":231,"bottom":240,"left":11,"right":18,"width":7,"height":9},"1-0-rect":{"id":"","top":231,"bottom":240,"left":11,"right":18,"width":7,"height":9},"1-1-BODY":{"id":"","top":8,"bottom":1534,"left":8,"right":404,"width":396,"height":1526},"1-2-LINK":{"id":"","top":0,"bottom":0,"left":0,"right":0,"width":0,"height":0},"1-3-LINK":{"id":"","top":0,"bottom":0,"left":0,"right":0,"width":0,"height":0},"1-4-LINK":{"id":"","top":0,"bottom":0,"left":0,"right":0,"width":0,"height":0},"1-5-LINK":{"id":"","top":0,"bottom":0,"left":0,"right":0,"width":0,"height":0},"1-6-LINK":{"id":"","top":0,"bottom":0,"left":0,"right":0,"width":0,"height":0},"1-7-LINK":{"id":"","top":0,"bottom":0,"left":0,"right":0,"width":0,"height":0},"1-8-LINK":{"id":"","top":0,"bottom":0,"left":0,"right":0,"width":0,"height":0},"1-9-META":{"id":"","top":0,"bottom":0,"left":0,"right":0,"width":0,"height":0},"1-10-META":{"id":"","top":0,"bottom":0,"left":0,"right":0,"width":0,"height":0},"1-11-META":{"id":"","top":0,"bottom":0,"left":0,"right":0,"width":0,"height":0}}},"timing":{"entries":[{"startTime":1588.97,"name":"lh:config","duration":683.9,"entryType":"measure"},{"startTime":1590.51,"name":"lh:config:resolveArtifactsToDefns","duration":82.73,"entryType":"measure"},{"startTime":2273.03,"name":"lh:runner:gather","duration":7397.48,"entryType":"measure"},{"startTime":2336.63,"name":"lh:driver:connect","duration":6.42,"entryType":"measure"},{"startTime":2343.26,"name":"lh:driver:navigate","duration":5.8,"entryType":"measure"},{"startTime":2349.38,"name":"lh:gather:getBenchmarkIndex","duration":1007.53,"entryType":"measure"},{"startTime":3357.21,"name":"lh:gather:getVersion","duration":0.98,"entryType":"measure"},{"startTime":3359.12,"name":"lh:prepare:navigationMode","duration":54.51,"entryType":"measure"},{"startTime":3373.26,"name":"lh:storage:clearDataForOrigin","duration":11.21,"entryType":"measure"},{"startTime":3384.71,"name":"lh:storage:clearBrowserCaches","duration":26.96,"entryType":"measure"},{"startTime":3412.47,"name":"lh:gather:prepareThrottlingAndNetwork","duration":1.08,"entryType":"measure"},{"startTime":3448.42,"name":"lh:driver:navigate","duration":4008.15,"entryType":"measure"},{"startTime":7569.95,"name":"lh:computed:NetworkRecords","duration":0.85,"entryType":"measure"},{"startTime":7571.25,"name":"lh:gather:getArtifact:DevtoolsLog","duration":0.12,"entryType":"measure"},{"startTime":7571.39,"name":"lh:gather:getArtifact:Trace","duration":0.15,"entryType":"measure"},{"startTime":7571.55,"name":"lh:gather:getArtifact:ConsoleMessages","duration":0.13,"entryType":"measure"},{"startTime":7571.69,"name":"lh:gather:getArtifact:CSSUsage","duration":14.37,"entryType":"measure"},{"startTime":7586.1,"name":"lh:gather:getArtifact:DOMStats","duration":7.05,"entryType":"measure"},{"startTime":7593.18,"name":"lh:gather:getArtifact:ImageElements","duration":5.74,"entryType":"measure"},{"startTime":7598.93,"name":"lh:gather:getArtifact:JsUsage","duration":0.22,"entryType":"measure"},{"startTime":7599.24,"name":"lh:gather:getArtifact:LinkElements","duration":4.41,"entryType":"measure"},{"startTime":7603.34,"name":"lh:computed:MainResource","duration":0.22,"entryType":"measure"},{"startTime":7603.67,"name":"lh:gather:getArtifact:MetaElements","duration":2.5,"entryType":"measure"},{"startTime":7606.2,"name":"lh:gather:getArtifact:NetworkUserAgent","duration":0.25,"entryType":"measure"},{"startTime":7606.48,"name":"lh:gather:getArtifact:OptimizedImages","duration":0.35,"entryType":"measure"},{"startTime":7606.85,"name":"lh:gather:getArtifact:ResponseCompression","duration":0.49,"entryType":"measure"},{"startTime":7607.36,"name":"lh:gather:getArtifact:Scripts","duration":0.21,"entryType":"measure"},{"startTime":7607.6,"name":"lh:gather:getArtifact:SourceMaps","duration":0.15,"entryType":"measure"},{"startTime":7607.76,"name":"lh:gather:getArtifact:Stacks","duration":9.37,"entryType":"measure"},{"startTime":7607.94,"name":"lh:gather:collectStacks","duration":9.17,"entryType":"measure"},{"startTime":7617.15,"name":"lh:gather:getArtifact:Stylesheets","duration":5.3,"entryType":"measure"},{"startTime":7622.49,"name":"lh:gather:getArtifact:TraceElements","duration":654.17,"entryType":"measure"},{"startTime":7622.85,"name":"lh:computed:TraceEngineResult","duration":566.99,"entryType":"measure"},{"startTime":7623.04,"name":"lh:computed:ProcessedTrace","duration":8.76,"entryType":"measure"},{"startTime":7632.55,"name":"lh:computed:TraceEngineResult:total","duration":554.28,"entryType":"measure"},{"startTime":7632.74,"name":"lh:computed:TraceEngineResult:parse","duration":527.62,"entryType":"measure"},{"startTime":7633.81,"name":"lh:computed:TraceEngineResult:parse:handleEvent","duration":55.99,"entryType":"measure"},{"startTime":7689.83,"name":"lh:computed:TraceEngineResult:parse:Meta:finalize","duration":17.42,"entryType":"measure"},{"startTime":7707.46,"name":"lh:computed:TraceEngineResult:parse:AnimationFrames:finalize","duration":15.21,"entryType":"measure"},{"startTime":7722.73,"name":"lh:computed:TraceEngineResult:parse:Animations:finalize","duration":15.67,"entryType":"measure"},{"startTime":7738.45,"name":"lh:computed:TraceEngineResult:parse:Samples:finalize","duration":15.22,"entryType":"measure"},{"startTime":7753.75,"name":"lh:computed:TraceEngineResult:parse:AuctionWorklets:finalize","duration":15.51,"entryType":"measure"},{"startTime":7769.37,"name":"lh:computed:TraceEngineResult:parse:NetworkRequests:finalize","duration":18.9,"entryType":"measure"},{"startTime":7788.32,"name":"lh:computed:TraceEngineResult:parse:Renderer:finalize","duration":19.3,"entryType":"measure"},{"startTime":7807.73,"name":"lh:computed:TraceEngineResult:parse:Flows:finalize","duration":8.89,"entryType":"measure"},{"startTime":7816.67,"name":"lh:computed:TraceEngineResult:parse:AsyncJSCalls:finalize","duration":14.64,"entryType":"measure"},{"startTime":7831.43,"name":"lh:computed:TraceEngineResult:parse:DOMStats:finalize","duration":15.24,"entryType":"measure"},{"startTime":7846.76,"name":"lh:computed:TraceEngineResult:parse:UserTimings:finalize","duration":15.98,"entryType":"measure"},{"startTime":7862.84,"name":"lh:computed:TraceEngineResult:parse:ExtensionTraceData:finalize","duration":15.92,"entryType":"measure"},{"startTime":7878.8,"name":"lh:computed:TraceEngineResult:parse:LayerTree:finalize","duration":15.18,"entryType":"measure"},{"startTime":7894.01,"name":"lh:computed:TraceEngineResult:parse:Frames:finalize","duration":20.11,"entryType":"measure"},{"startTime":7914.15,"name":"lh:computed:TraceEngineResult:parse:GPU:finalize","duration":10.22,"entryType":"measure"},{"startTime":7924.4,"name":"lh:computed:TraceEngineResult:parse:ImagePainting:finalize","duration":15.34,"entryType":"measure"},{"startTime":7939.79,"name":"lh:computed:TraceEngineResult:parse:Initiators:finalize","duration":16.2,"entryType":"measure"},{"startTime":7956.03,"name":"lh:computed:TraceEngineResult:parse:Invalidations:finalize","duration":15.78,"entryType":"measure"},{"startTime":7971.86,"name":"lh:computed:TraceEngineResult:parse:PageLoadMetrics:finalize","duration":15.9,"entryType":"measure"},{"startTime":7987.8,"name":"lh:computed:TraceEngineResult:parse:LargestImagePaint:finalize","duration":15.32,"entryType":"measure"},{"startTime":8003.16,"name":"lh:computed:TraceEngineResult:parse:LargestTextPaint:finalize","duration":15.91,"entryType":"measure"},{"startTime":8019.15,"name":"lh:computed:TraceEngineResult:parse:Screenshots:finalize","duration":16.19,"entryType":"measure"},{"startTime":8035.44,"name":"lh:computed:TraceEngineResult:parse:LayoutShifts:finalize","duration":14.35,"entryType":"measure"},{"startTime":8049.82,"name":"lh:computed:TraceEngineResult:parse:Memory:finalize","duration":14.49,"entryType":"measure"},{"startTime":8064.37,"name":"lh:computed:TraceEngineResult:parse:PageFrames:finalize","duration":15.9,"entryType":"measure"},{"startTime":8080.32,"name":"lh:computed:TraceEngineResult:parse:Scripts:finalize","duration":15.85,"entryType":"measure"},{"startTime":8096.21,"name":"lh:computed:TraceEngineResult:parse:SelectorStats:finalize","duration":15.68,"entryType":"measure"},{"startTime":8111.94,"name":"lh:computed:TraceEngineResult:parse:UserInteractions:finalize","duration":16.05,"entryType":"measure"},{"startTime":8128.03,"name":"lh:computed:TraceEngineResult:parse:Warnings:finalize","duration":14.87,"entryType":"measure"},{"startTime":8142.95,"name":"lh:computed:TraceEngineResult:parse:Workers:finalize","duration":15.97,"entryType":"measure"},{"startTime":8159.06,"name":"lh:computed:TraceEngineResult:parse:clone","duration":1.26,"entryType":"measure"},{"startTime":8160.36,"name":"lh:computed:TraceEngineResult:insights","duration":26.45,"entryType":"measure"},{"startTime":8160.66,"name":"lh:computed:TraceEngineResult:insights:createLanternContext","duration":11.49,"entryType":"measure"},{"startTime":8172.3,"name":"lh:computed:TraceEngineResult:insights:CLSCulprits","duration":0.93,"entryType":"measure"},{"startTime":8173.25,"name":"lh:computed:TraceEngineResult:insights:Cache","duration":0.41,"entryType":"measure"},{"startTime":8173.68,"name":"lh:computed:TraceEngineResult:insights:DOMSize","duration":0.7,"entryType":"measure"},{"startTime":8174.4,"name":"lh:computed:TraceEngineResult:insights:DocumentLatency","duration":0.47,"entryType":"measure"},{"startTime":8174.89,"name":"lh:computed:TraceEngineResult:insights:DuplicatedJavaScript","duration":1.62,"entryType":"measure"},{"startTime":8176.52,"name":"lh:computed:TraceEngineResult:insights:FontDisplay","duration":0.3,"entryType":"measure"},{"startTime":8176.84,"name":"lh:computed:TraceEngineResult:insights:ForcedReflow","duration":0.35,"entryType":"measure"},{"startTime":8177.21,"name":"lh:computed:TraceEngineResult:insights:ImageDelivery","duration":0.42,"entryType":"measure"},{"startTime":8177.64,"name":"lh:computed:TraceEngineResult:insights:InteractionToNextPaint","duration":0.23,"entryType":"measure"},{"startTime":8177.89,"name":"lh:computed:TraceEngineResult:insights:LCPDiscovery","duration":0.41,"entryType":"measure"},{"startTime":8178.33,"name":"lh:computed:TraceEngineResult:insights:LCPPhases","duration":0.43,"entryType":"measure"},{"startTime":8178.78,"name":"lh:computed:TraceEngineResult:insights:LegacyJavaScript","duration":0.3,"entryType":"measure"},{"startTime":8179.09,"name":"lh:computed:TraceEngineResult:insights:ModernHTTP","duration":1.18,"entryType":"measure"},{"startTime":8180.29,"name":"lh:computed:TraceEngineResult:insights:NetworkDependencyTree","duration":0.56,"entryType":"measure"},{"startTime":8180.86,"name":"lh:computed:TraceEngineResult:insights:RenderBlocking","duration":0.35,"entryType":"measure"},{"startTime":8181.28,"name":"lh:computed:TraceEngineResult:insights:SlowCSSSelector","duration":0.28,"entryType":"measure"},{"startTime":8181.58,"name":"lh:computed:TraceEngineResult:insights:ThirdParties","duration":3.86,"entryType":"measure"},{"startTime":8185.46,"name":"lh:computed:TraceEngineResult:insights:Viewport","duration":0.39,"entryType":"measure"},{"startTime":8191.11,"name":"lh:computed:ProcessedNavigation","duration":0.61,"entryType":"measure"},{"startTime":8191.79,"name":"lh:computed:CumulativeLayoutShift","duration":77.64,"entryType":"measure"},{"startTime":8269.99,"name":"lh:computed:Responsiveness","duration":0.15,"entryType":"measure"},{"startTime":8276.69,"name":"lh:gather:getArtifact:ViewportDimensions","duration":0.92,"entryType":"measure"},{"startTime":8277.62,"name":"lh:gather:getArtifact:FullPageScreenshot","duration":1117.44,"entryType":"measure"},{"startTime":9395.1,"name":"lh:gather:getArtifact:BFCacheFailures","duration":259.47,"entryType":"measure"},{"startTime":9670.81,"name":"lh:runner:audit","duration":308.74,"entryType":"measure"},{"startTime":9670.9,"name":"lh:runner:auditing","duration":308.22,"entryType":"measure"},{"startTime":9672.09,"name":"lh:audit:viewport","duration":1.91,"entryType":"measure"},{"startTime":9672.54,"name":"lh:computed:ViewportMeta","duration":0.4,"entryType":"measure"},{"startTime":9674.39,"name":"lh:audit:first-contentful-paint","duration":4.63,"entryType":"measure"},{"startTime":9674.98,"name":"lh:computed:FirstContentfulPaint","duration":2.76,"entryType":"measure"},{"startTime":9675.31,"name":"lh:computed:LanternFirstContentfulPaint","duration":2.43,"entryType":"measure"},{"startTime":9675.64,"name":"lh:computed:PageDependencyGraph","duration":1.38,"entryType":"measure"},{"startTime":9677.04,"name":"lh:computed:LoadSimulator","duration":0.26,"entryType":"measure"},{"startTime":9677.08,"name":"lh:computed:NetworkAnalysis","duration":0.19,"entryType":"measure"},{"startTime":9679.22,"name":"lh:audit:largest-contentful-paint","duration":2.18,"entryType":"measure"},{"startTime":9679.74,"name":"lh:computed:LargestContentfulPaint","duration":0.93,"entryType":"measure"},{"startTime":9679.81,"name":"lh:computed:LanternLargestContentfulPaint","duration":0.85,"entryType":"measure"},{"startTime":9681.6,"name":"lh:audit:first-meaningful-paint","duration":0.84,"entryType":"measure"},{"startTime":9682.63,"name":"lh:audit:speed-index","duration":171.29,"entryType":"measure"},{"startTime":9683.04,"name":"lh:computed:SpeedIndex","duration":170.17,"entryType":"measure"},{"startTime":9683.09,"name":"lh:computed:LanternSpeedIndex","duration":170.11,"entryType":"measure"},{"startTime":9683.13,"name":"lh:computed:Speedline","duration":168.93,"entryType":"measure"},{"startTime":9853.94,"name":"lh:audit:screenshot-thumbnails","duration":0.52,"entryType":"measure"},{"startTime":9854.48,"name":"lh:audit:final-screenshot","duration":0.54,"entryType":"measure"},{"startTime":9854.64,"name":"lh:computed:Screenshots","duration":0.35,"entryType":"measure"},{"startTime":9855.27,"name":"lh:audit:total-blocking-time","duration":2.35,"entryType":"measure"},{"startTime":9855.76,"name":"lh:computed:TotalBlockingTime","duration":1.29,"entryType":"measure"},{"startTime":9855.82,"name":"lh:computed:LanternTotalBlockingTime","duration":1.22,"entryType":"measure"},{"startTime":9855.89,"name":"lh:computed:LanternInteractive","duration":0.63,"entryType":"measure"},{"startTime":9857.8,"name":"lh:audit:max-potential-fid","duration":2.18,"entryType":"measure"},{"startTime":9858.24,"name":"lh:computed:MaxPotentialFID","duration":0.99,"entryType":"measure"},{"startTime":9858.29,"name":"lh:computed:LanternMaxPotentialFID","duration":0.93,"entryType":"measure"},{"startTime":9860.23,"name":"lh:audit:cumulative-layout-shift","duration":0.96,"entryType":"measure"},{"startTime":9861.39,"name":"lh:audit:server-response-time","duration":1.16,"entryType":"measure"},{"startTime":9862.74,"name":"lh:audit:interactive","duration":1.04,"entryType":"measure"},{"startTime":9863.14,"name":"lh:computed:Interactive","duration":0.06,"entryType":"measure"},{"startTime":9863.95,"name":"lh:audit:user-timings","duration":1.26,"entryType":"measure"},{"startTime":9864.28,"name":"lh:computed:UserTimings","duration":0.41,"entryType":"measure"},{"startTime":9865.36,"name":"lh:audit:critical-request-chains","duration":2.29,"entryType":"measure"},{"startTime":9865.7,"name":"lh:computed:CriticalRequestChains","duration":0.36,"entryType":"measure"},{"startTime":9867.96,"name":"lh:audit:redirects","duration":1.55,"entryType":"measure"},{"startTime":9869.73,"name":"lh:audit:mainthread-work-breakdown","duration":3.32,"entryType":"measure"},{"startTime":9870.24,"name":"lh:computed:MainThreadTasks","duration":1.95,"entryType":"measure"},{"startTime":9873.25,"name":"lh:audit:bootup-time","duration":3.59,"entryType":"measure"},{"startTime":9874.16,"name":"lh:computed:TBTImpactTasks","duration":0.64,"entryType":"measure"},{"startTime":9876.99,"name":"lh:audit:uses-rel-preconnect","duration":1.51,"entryType":"measure"},{"startTime":9878.7,"name":"lh:audit:font-display","duration":1.25,"entryType":"measure"},{"startTime":9879.98,"name":"lh:audit:diagnostics","duration":0.41,"entryType":"measure"},{"startTime":9880.4,"name":"lh:audit:network-requests","duration":1.94,"entryType":"measure"},{"startTime":9880.65,"name":"lh:computed:EntityClassification","duration":1.34,"entryType":"measure"},{"startTime":9882.52,"name":"lh:audit:network-rtt","duration":1.05,"entryType":"measure"},{"startTime":9883.74,"name":"lh:audit:network-server-latency","duration":0.96,"entryType":"measure"},{"startTime":9884.71,"name":"lh:audit:main-thread-tasks","duration":0.23,"entryType":"measure"},{"startTime":9884.95,"name":"lh:audit:metrics","duration":2.03,"entryType":"measure"},{"startTime":9885.15,"name":"lh:computed:TimingSummary","duration":1.65,"entryType":"measure"},{"startTime":9885.4,"name":"lh:computed:FirstContentfulPaintAllFrames","duration":0.06,"entryType":"measure"},{"startTime":9885.49,"name":"lh:computed:LargestContentfulPaintAllFrames","duration":0.06,"entryType":"measure"},{"startTime":9885.7,"name":"lh:computed:LCPBreakdown","duration":0.72,"entryType":"measure"},{"startTime":9885.8,"name":"lh:computed:TimeToFirstByte","duration":0.17,"entryType":"measure"},{"startTime":9885.99,"name":"lh:computed:LCPImageRecord","duration":0.42,"entryType":"measure"},{"startTime":9886.99,"name":"lh:audit:resource-summary","duration":1.13,"entryType":"measure"},{"startTime":9887.22,"name":"lh:computed:ResourceSummary","duration":0.29,"entryType":"measure"},{"startTime":9888.35,"name":"lh:audit:third-party-summary","duration":1.53,"entryType":"measure"},{"startTime":9890.1,"name":"lh:audit:third-party-facades","duration":1.05,"entryType":"measure"},{"startTime":9891.29,"name":"lh:audit:largest-contentful-paint-element","duration":1.22,"entryType":"measure"},{"startTime":9892.68,"name":"lh:audit:lcp-lazy-loaded","duration":0.93,"entryType":"measure"},{"startTime":9893.8,"name":"lh:audit:layout-shifts","duration":0.88,"entryType":"measure"},{"startTime":9894.81,"name":"lh:audit:long-tasks","duration":2.23,"entryType":"measure"},{"startTime":9897.18,"name":"lh:audit:non-composited-animations","duration":0.9,"entryType":"measure"},{"startTime":9898.27,"name":"lh:audit:unsized-images","duration":0.81,"entryType":"measure"},{"startTime":9899.22,"name":"lh:audit:prioritize-lcp-image","duration":0.61,"entryType":"measure"},{"startTime":9899.84,"name":"lh:audit:script-treemap-data","duration":1.3,"entryType":"measure"},{"startTime":9900.15,"name":"lh:computed:JSBundles","duration":0.07,"entryType":"measure"},{"startTime":9900.23,"name":"lh:computed:ModuleDuplication","duration":0.15,"entryType":"measure"},{"startTime":9900.41,"name":"lh:computed:UnusedJavascriptSummary","duration":0.2,"entryType":"measure"},{"startTime":9900.67,"name":"lh:computed:UnusedJavascriptSummary","duration":0.03,"entryType":"measure"},{"startTime":9900.73,"name":"lh:computed:UnusedJavascriptSummary","duration":0.04,"entryType":"measure"},{"startTime":9900.8,"name":"lh:computed:UnusedJavascriptSummary","duration":0.09,"entryType":"measure"},{"startTime":9900.92,"name":"lh:computed:UnusedJavascriptSummary","duration":0.03,"entryType":"measure"},{"startTime":9900.98,"name":"lh:computed:UnusedJavascriptSummary","duration":0.03,"entryType":"measure"},{"startTime":9901.05,"name":"lh:computed:UnusedJavascriptSummary","duration":0.04,"entryType":"measure"},{"startTime":9901.35,"name":"lh:audit:uses-long-cache-ttl","duration":1.43,"entryType":"measure"},{"startTime":9902.96,"name":"lh:audit:total-byte-weight","duration":1.09,"entryType":"measure"},{"startTime":9904.19,"name":"lh:audit:offscreen-images","duration":2.64,"entryType":"measure"},{"startTime":9906.98,"name":"lh:audit:render-blocking-resources","duration":2.9,"entryType":"measure"},{"startTime":9907.59,"name":"lh:computed:UnusedCSS","duration":1.5,"entryType":"measure"},{"startTime":9909.11,"name":"lh:computed:NavigationInsights","duration":0.08,"entryType":"measure"},{"startTime":9909.24,"name":"lh:computed:FirstContentfulPaint","duration":0.05,"entryType":"measure"},{"startTime":9910.05,"name":"lh:audit:unminified-css","duration":2.05,"entryType":"measure"},{"startTime":9912.23,"name":"lh:audit:unminified-javascript","duration":2.47,"entryType":"measure"},{"startTime":9914.87,"name":"lh:audit:unused-css-rules","duration":1.71,"entryType":"measure"},{"startTime":9916.77,"name":"lh:audit:unused-javascript","duration":1.85,"entryType":"measure"},{"startTime":9918.78,"name":"lh:audit:modern-image-formats","duration":1.79,"entryType":"measure"},{"startTime":9920.73,"name":"lh:audit:uses-optimized-images","duration":1.59,"entryType":"measure"},{"startTime":9922.45,"name":"lh:audit:uses-text-compression","duration":1.53,"entryType":"measure"},{"startTime":9924.14,"name":"lh:audit:uses-responsive-images","duration":4.22,"entryType":"measure"},{"startTime":9924.53,"name":"lh:computed:ImageRecords","duration":0.25,"entryType":"measure"},{"startTime":9928.51,"name":"lh:audit:efficient-animated-content","duration":1.47,"entryType":"measure"},{"startTime":9930.1,"name":"lh:audit:duplicated-javascript","duration":1.58,"entryType":"measure"},{"startTime":9931.84,"name":"lh:audit:legacy-javascript","duration":17.79,"entryType":"measure"},{"startTime":9949.87,"name":"lh:audit:dom-size","duration":2.26,"entryType":"measure"},{"startTime":9952.44,"name":"lh:audit:no-document-write","duration":1.21,"entryType":"measure"},{"startTime":9953.8,"name":"lh:audit:uses-http2","duration":1.89,"entryType":"measure"},{"startTime":9955.88,"name":"lh:audit:uses-passive-event-listeners","duration":0.83,"entryType":"measure"},{"startTime":9956.91,"name":"lh:audit:bf-cache","duration":0.85,"entryType":"measure"},{"startTime":9957.94,"name":"lh:audit:cache-insight","duration":1.01,"entryType":"measure"},{"startTime":9959.21,"name":"lh:audit:cls-culprits-insight","duration":0.9,"entryType":"measure"},{"startTime":9960.29,"name":"lh:audit:document-latency-insight","duration":0.73,"entryType":"measure"},{"startTime":9961.19,"name":"lh:audit:dom-size-insight","duration":1,"entryType":"measure"},{"startTime":9962.42,"name":"lh:audit:duplicated-javascript-insight","duration":0.92,"entryType":"measure"},{"startTime":9963.59,"name":"lh:audit:font-display-insight","duration":0.9,"entryType":"measure"},{"startTime":9964.75,"name":"lh:audit:forced-reflow-insight","duration":0.8,"entryType":"measure"},{"startTime":9965.72,"name":"lh:audit:image-delivery-insight","duration":0.89,"entryType":"measure"},{"startTime":9966.84,"name":"lh:audit:interaction-to-next-paint-insight","duration":1.13,"entryType":"measure"},{"startTime":9968.3,"name":"lh:audit:lcp-discovery-insight","duration":0.82,"entryType":"measure"},{"startTime":9969.29,"name":"lh:audit:lcp-phases-insight","duration":1.01,"entryType":"measure"},{"startTime":9970.55,"name":"lh:audit:legacy-javascript-insight","duration":1.05,"entryType":"measure"},{"startTime":9971.81,"name":"lh:audit:modern-http-insight","duration":0.84,"entryType":"measure"},{"startTime":9972.82,"name":"lh:audit:network-dependency-tree-insight","duration":0.99,"entryType":"measure"},{"startTime":9974.01,"name":"lh:audit:render-blocking-insight","duration":0.83,"entryType":"measure"},{"startTime":9975.01,"name":"lh:audit:third-parties-insight","duration":3.1,"entryType":"measure"},{"startTime":9978.29,"name":"lh:audit:viewport-insight","duration":0.82,"entryType":"measure"},{"startTime":9979.13,"name":"lh:runner:generate","duration":0.41,"entryType":"measure"}],"total":7706.219999999999},"i18n":{"rendererFormattedStrings":{"calculatorLink":"See calculator.","collapseView":"Collapse view","crcInitialNavigation":"Initial Navigation","crcLongestDurationLabel":"Maximum critical path latency:","dropdownCopyJSON":"Copy JSON","dropdownDarkTheme":"Toggle Dark Theme","dropdownPrintExpanded":"Print Expanded","dropdownPrintSummary":"Print Summary","dropdownSaveGist":"Save as Gist","dropdownSaveHTML":"Save as HTML","dropdownSaveJSON":"Save as JSON","dropdownViewUnthrottledTrace":"View Unthrottled Trace","dropdownViewer":"Open in Viewer","errorLabel":"Error!","errorMissingAuditInfo":"Report error: no audit information","expandView":"Expand view","firstPartyChipLabel":"1st party","footerIssue":"File an issue","goBackToAudits":"Go back to audits","hide":"Hide","insightsNotice":"Later this year, insights will replace performance audits. [Learn more and provide feedback here](https://github.com/GoogleChrome/lighthouse/discussions/16462).","labDataTitle":"Lab Data","lsPerformanceCategoryDescription":"[Lighthouse](https://developers.google.com/web/tools/lighthouse/) analysis of the current page on an emulated mobile network. Values are estimated and may vary.","manualAuditsGroupTitle":"Additional items to manually check","notApplicableAuditsGroupTitle":"Not applicable","openInANewTabTooltip":"Open in a new tab","opportunityResourceColumnLabel":"Opportunity","opportunitySavingsColumnLabel":"Estimated Savings","passedAuditsGroupTitle":"Passed audits","runtimeAnalysisWindow":"Initial page load","runtimeAnalysisWindowSnapshot":"Point-in-time snapshot","runtimeAnalysisWindowTimespan":"User interactions timespan","runtimeCustom":"Custom throttling","runtimeDesktopEmulation":"Emulated Desktop","runtimeMobileEmulation":"Emulated Moto G Power","runtimeNoEmulation":"No emulation","runtimeSettingsAxeVersion":"Axe version","runtimeSettingsBenchmark":"Unthrottled CPU/Memory Power","runtimeSettingsCPUThrottling":"CPU throttling","runtimeSettingsDevice":"Device","runtimeSettingsNetworkThrottling":"Network throttling","runtimeSettingsScreenEmulation":"Screen emulation","runtimeSettingsUANetwork":"User agent (network)","runtimeSingleLoad":"Single page session","runtimeSingleLoadTooltip":"This data is taken from a single page session, as opposed to field data summarizing many sessions.","runtimeSlow4g":"Slow 4G throttling","runtimeUnknown":"Unknown","show":"Show","showRelevantAudits":"Show audits relevant to:","snippetCollapseButtonLabel":"Collapse snippet","snippetExpandButtonLabel":"Expand snippet","thirdPartyResourcesLabel":"Show 3rd-party resources","throttlingProvided":"Provided by environment","toplevelWarningsMessage":"There were issues affecting this run of Lighthouse:","tryInsights":"Try insights","unattributable":"Unattributable","varianceDisclaimer":"Values are estimated and may vary. The [performance score is calculated](https://developer.chrome.com/docs/lighthouse/performance/performance-scoring/) directly from these metrics.","viewTraceLabel":"View Trace","viewTreemapLabel":"View Treemap","warningAuditsGroupTitle":"Passed audits but with warnings","warningHeader":"Warnings: "},"icuMessagePaths":{"core/audits/viewport.js | title":["audits.viewport.title"],"core/audits/viewport.js | description":["audits.viewport.description"],"core/lib/i18n/i18n.js | firstContentfulPaintMetric":["audits[first-contentful-paint].title"],"core/audits/metrics/first-contentful-paint.js | description":["audits[first-contentful-paint].description"],"core/lib/i18n/i18n.js | seconds":[{"values":{"timeInMs":817.4305999999999},"path":"audits[first-contentful-paint].displayValue"},{"values":{"timeInMs":1267.4306},"path":"audits[largest-contentful-paint].displayValue"},{"values":{"timeInMs":2373.445213996282},"path":"audits[speed-index].displayValue"},{"values":{"timeInMs":1267.4306},"path":"audits.interactive.displayValue"},{"values":{"timeInMs":253.08000000000007},"path":"audits[mainthread-work-breakdown].displayValue"},{"values":{"timeInMs":11.488},"path":"audits[bootup-time].displayValue"}],"core/lib/i18n/i18n.js | largestContentfulPaintMetric":["audits[largest-contentful-paint].title"],"core/audits/metrics/largest-contentful-paint.js | description":["audits[largest-contentful-paint].description"],"core/lib/i18n/i18n.js | firstMeaningfulPaintMetric":["audits[first-meaningful-paint].title"],"core/audits/metrics/first-meaningful-paint.js | description":["audits[first-meaningful-paint].description"],"core/lib/i18n/i18n.js | speedIndexMetric":["audits[speed-index].title"],"core/audits/metrics/speed-index.js | description":["audits[speed-index].description"],"core/lib/i18n/i18n.js | totalBlockingTimeMetric":["audits[total-blocking-time].title"],"core/audits/metrics/total-blocking-time.js | description":["audits[total-blocking-time].description"],"core/lib/i18n/i18n.js | ms":[{"values":{"timeInMs":0},"path":"audits[total-blocking-time].displayValue"},{"values":{"timeInMs":16},"path":"audits[max-potential-fid].displayValue"},{"values":{"timeInMs":0.10469999999999999},"path":"audits[network-rtt].displayValue"},{"values":{"timeInMs":33.7153},"path":"audits[network-server-latency].displayValue"},{"values":{"timeInMs":1267.4306},"path":"audits[largest-contentful-paint-element].displayValue"}],"core/lib/i18n/i18n.js | maxPotentialFIDMetric":["audits[max-potential-fid].title"],"core/audits/metrics/max-potential-fid.js | description":["audits[max-potential-fid].description"],"core/lib/i18n/i18n.js | cumulativeLayoutShiftMetric":["audits[cumulative-layout-shift].title"],"core/audits/metrics/cumulative-layout-shift.js | description":["audits[cumulative-layout-shift].description"],"core/audits/server-response-time.js | title":["audits[server-response-time].title"],"core/audits/server-response-time.js | description":["audits[server-response-time].description"],"core/audits/server-response-time.js | displayValue":[{"values":{"timeInMs":2.662},"path":"audits[server-response-time].displayValue"}],"core/lib/i18n/i18n.js | columnURL":["audits[server-response-time].details.headings[0].label","audits[bootup-time].details.headings[0].label","audits[network-rtt].details.headings[0].label","audits[network-server-latency].details.headings[0].label","audits[long-tasks].details.headings[0].label","audits[total-byte-weight].details.headings[0].label"],"core/lib/i18n/i18n.js | columnTimeSpent":["audits[server-response-time].details.headings[1].label","audits[mainthread-work-breakdown].details.headings[1].label","audits[network-rtt].details.headings[1].label","audits[network-server-latency].details.headings[1].label"],"core/lib/i18n/i18n.js | interactiveMetric":["audits.interactive.title"],"core/audits/metrics/interactive.js | description":["audits.interactive.description"],"core/audits/user-timings.js | title":["audits[user-timings].title"],"core/audits/user-timings.js | description":["audits[user-timings].description"],"core/audits/critical-request-chains.js | title":["audits[critical-request-chains].title"],"core/audits/critical-request-chains.js | description":["audits[critical-request-chains].description"],"core/audits/critical-request-chains.js | displayValue":[{"values":{"itemCount":1},"path":"audits[critical-request-chains].displayValue"}],"core/audits/redirects.js | title":["audits.redirects.title"],"core/audits/redirects.js | description":["audits.redirects.description"],"core/audits/mainthread-work-breakdown.js | title":["audits[mainthread-work-breakdown].title"],"core/audits/mainthread-work-breakdown.js | description":["audits[mainthread-work-breakdown].description"],"core/audits/mainthread-work-breakdown.js | columnCategory":["audits[mainthread-work-breakdown].details.headings[0].label"],"core/audits/bootup-time.js | title":["audits[bootup-time].title"],"core/audits/bootup-time.js | description":["audits[bootup-time].description"],"core/audits/bootup-time.js | columnTotal":["audits[bootup-time].details.headings[1].label"],"core/audits/bootup-time.js | columnScriptEval":["audits[bootup-time].details.headings[2].label"],"core/audits/bootup-time.js | columnScriptParse":["audits[bootup-time].details.headings[3].label"],"core/audits/uses-rel-preconnect.js | title":["audits[uses-rel-preconnect].title"],"core/audits/uses-rel-preconnect.js | description":["audits[uses-rel-preconnect].description"],"core/audits/uses-rel-preconnect.js | unusedWarning":[{"values":{"securityOrigin":"https://fonts.googleapis.com"},"path":"audits[uses-rel-preconnect].warnings[0]"},{"values":{"securityOrigin":"https://fonts.gstatic.com"},"path":"audits[uses-rel-preconnect].warnings[1]"}],"core/audits/font-display.js | title":["audits[font-display].title"],"core/audits/font-display.js | description":["audits[font-display].description"],"core/audits/network-rtt.js | title":["audits[network-rtt].title"],"core/audits/network-rtt.js | description":["audits[network-rtt].description"],"core/audits/network-server-latency.js | title":["audits[network-server-latency].title"],"core/audits/network-server-latency.js | description":["audits[network-server-latency].description"],"core/lib/i18n/i18n.js | columnResourceType":["audits[resource-summary].details.headings[0].label"],"core/lib/i18n/i18n.js | columnRequests":["audits[resource-summary].details.headings[1].label"],"core/lib/i18n/i18n.js | columnTransferSize":["audits[resource-summary].details.headings[2].label","audits[total-byte-weight].details.headings[1].label"],"core/lib/i18n/i18n.js | total":["audits[resource-summary].details.items[0].label"],"core/lib/i18n/i18n.js | documentResourceType":["audits[resource-summary].details.items[1].label"],"core/lib/i18n/i18n.js | scriptResourceType":["audits[resource-summary].details.items[2].label"],"core/lib/i18n/i18n.js | stylesheetResourceType":["audits[resource-summary].details.items[3].label"],"core/lib/i18n/i18n.js | imageResourceType":["audits[resource-summary].details.items[4].label"],"core/lib/i18n/i18n.js | mediaResourceType":["audits[resource-summary].details.items[5].label"],"core/lib/i18n/i18n.js | fontResourceType":["audits[resource-summary].details.items[6].label"],"core/lib/i18n/i18n.js | otherResourceType":["audits[resource-summary].details.items[7].label"],"core/lib/i18n/i18n.js | thirdPartyResourceType":["audits[resource-summary].details.items[8].label"],"core/audits/third-party-summary.js | title":["audits[third-party-summary].title"],"core/audits/third-party-summary.js | description":["audits[third-party-summary].description"],"core/audits/third-party-facades.js | title":["audits[third-party-facades].title"],"core/audits/third-party-facades.js | description":["audits[third-party-facades].description"],"core/audits/largest-contentful-paint-element.js | title":["audits[largest-contentful-paint-element].title"],"core/audits/largest-contentful-paint-element.js | description":["audits[largest-contentful-paint-element].description"],"core/lib/i18n/i18n.js | columnElement":["audits[largest-contentful-paint-element].details.items[0].headings[0].label","audits[dom-size].details.headings[1].label","audits[dom-size-insight].details.headings[1].label"],"core/audits/largest-contentful-paint-element.js | columnPhase":["audits[largest-contentful-paint-element].details.items[1].headings[0].label"],"core/audits/largest-contentful-paint-element.js | columnPercentOfLCP":["audits[largest-contentful-paint-element].details.items[1].headings[1].label"],"core/audits/largest-contentful-paint-element.js | columnTiming":["audits[largest-contentful-paint-element].details.items[1].headings[2].label"],"core/audits/largest-contentful-paint-element.js | itemTTFB":["audits[largest-contentful-paint-element].details.items[1].items[0].phase"],"core/audits/largest-contentful-paint-element.js | itemLoadDelay":["audits[largest-contentful-paint-element].details.items[1].items[1].phase"],"core/audits/largest-contentful-paint-element.js | itemLoadTime":["audits[largest-contentful-paint-element].details.items[1].items[2].phase"],"core/audits/largest-contentful-paint-element.js | itemRenderDelay":["audits[largest-contentful-paint-element].details.items[1].items[3].phase"],"core/audits/lcp-lazy-loaded.js | title":["audits[lcp-lazy-loaded].title"],"core/audits/lcp-lazy-loaded.js | description":["audits[lcp-lazy-loaded].description"],"core/audits/layout-shifts.js | title":["audits[layout-shifts].title"],"core/audits/layout-shifts.js | description":["audits[layout-shifts].description"],"core/audits/long-tasks.js | title":["audits[long-tasks].title"],"core/audits/long-tasks.js | description":["audits[long-tasks].description"],"core/audits/long-tasks.js | displayValue":[{"values":{"itemCount":1},"path":"audits[long-tasks].displayValue"}],"core/lib/i18n/i18n.js | columnStartTime":["audits[long-tasks].details.headings[1].label"],"core/lib/i18n/i18n.js | columnDuration":["audits[long-tasks].details.headings[2].label","audits[lcp-phases-insight].details.items[0].headings[1].label"],"core/audits/non-composited-animations.js | title":["audits[non-composited-animations].title"],"core/audits/non-composited-animations.js | description":["audits[non-composited-animations].description"],"core/audits/unsized-images.js | title":["audits[unsized-images].title"],"core/audits/unsized-images.js | description":["audits[unsized-images].description"],"core/audits/prioritize-lcp-image.js | title":["audits[prioritize-lcp-image].title"],"core/audits/prioritize-lcp-image.js | description":["audits[prioritize-lcp-image].description"],"core/audits/byte-efficiency/uses-long-cache-ttl.js | title":["audits[uses-long-cache-ttl].title"],"core/audits/byte-efficiency/uses-long-cache-ttl.js | description":["audits[uses-long-cache-ttl].description"],"core/audits/byte-efficiency/uses-long-cache-ttl.js | displayValue":[{"values":{"itemCount":0},"path":"audits[uses-long-cache-ttl].displayValue"}],"core/audits/byte-efficiency/total-byte-weight.js | title":["audits[total-byte-weight].title"],"core/audits/byte-efficiency/total-byte-weight.js | description":["audits[total-byte-weight].description"],"core/audits/byte-efficiency/total-byte-weight.js | displayValue":[{"values":{"totalBytes":26266},"path":"audits[total-byte-weight].displayValue"}],"core/audits/byte-efficiency/offscreen-images.js | title":["audits[offscreen-images].title"],"core/audits/byte-efficiency/offscreen-images.js | description":["audits[offscreen-images].description"],"core/audits/byte-efficiency/render-blocking-resources.js | title":["audits[render-blocking-resources].title"],"core/audits/byte-efficiency/render-blocking-resources.js | description":["audits[render-blocking-resources].description"],"core/audits/byte-efficiency/unminified-css.js | title":["audits[unminified-css].title"],"core/audits/byte-efficiency/unminified-css.js | description":["audits[unminified-css].description"],"core/audits/byte-efficiency/unminified-javascript.js | title":["audits[unminified-javascript].title"],"core/audits/byte-efficiency/unminified-javascript.js | description":["audits[unminified-javascript].description"],"core/audits/byte-efficiency/unused-css-rules.js | title":["audits[unused-css-rules].title"],"core/audits/byte-efficiency/unused-css-rules.js | description":["audits[unused-css-rules].description"],"core/audits/byte-efficiency/unused-javascript.js | title":["audits[unused-javascript].title"],"core/audits/byte-efficiency/unused-javascript.js | description":["audits[unused-javascript].description"],"core/audits/byte-efficiency/modern-image-formats.js | title":["audits[modern-image-formats].title"],"core/audits/byte-efficiency/modern-image-formats.js | description":["audits[modern-image-formats].description"],"core/audits/byte-efficiency/uses-optimized-images.js | title":["audits[uses-optimized-images].title"],"core/audits/byte-efficiency/uses-optimized-images.js | description":["audits[uses-optimized-images].description"],"core/audits/byte-efficiency/uses-text-compression.js | title":["audits[uses-text-compression].title"],"core/audits/byte-efficiency/uses-text-compression.js | description":["audits[uses-text-compression].description"],"core/audits/byte-efficiency/uses-responsive-images.js | title":["audits[uses-responsive-images].title"],"core/audits/byte-efficiency/uses-responsive-images.js | description":["audits[uses-responsive-images].description"],"core/audits/byte-efficiency/efficient-animated-content.js | title":["audits[efficient-animated-content].title"],"core/audits/byte-efficiency/efficient-animated-content.js | description":["audits[efficient-animated-content].description"],"core/audits/byte-efficiency/duplicated-javascript.js | title":["audits[duplicated-javascript].title"],"core/audits/byte-efficiency/duplicated-javascript.js | description":["audits[duplicated-javascript].description"],"core/audits/byte-efficiency/legacy-javascript.js | title":["audits[legacy-javascript].title"],"core/audits/byte-efficiency/legacy-javascript.js | description":["audits[legacy-javascript].description"],"core/audits/dobetterweb/dom-size.js | title":["audits[dom-size].title"],"core/audits/dobetterweb/dom-size.js | description":["audits[dom-size].description"],"core/audits/dobetterweb/dom-size.js | displayValue":[{"values":{"itemCount":251},"path":"audits[dom-size].displayValue"}],"core/audits/dobetterweb/dom-size.js | columnStatistic":["audits[dom-size].details.headings[0].label"],"core/audits/dobetterweb/dom-size.js | columnValue":["audits[dom-size].details.headings[2].label"],"core/audits/dobetterweb/dom-size.js | statisticDOMElements":["audits[dom-size].details.items[0].statistic"],"core/audits/dobetterweb/dom-size.js | statisticDOMDepth":["audits[dom-size].details.items[1].statistic"],"core/audits/dobetterweb/dom-size.js | statisticDOMWidth":["audits[dom-size].details.items[2].statistic"],"core/audits/dobetterweb/no-document-write.js | title":["audits[no-document-write].title"],"core/audits/dobetterweb/no-document-write.js | description":["audits[no-document-write].description"],"core/audits/dobetterweb/uses-http2.js | title":["audits[uses-http2].title"],"core/audits/dobetterweb/uses-http2.js | description":["audits[uses-http2].description"],"core/audits/dobetterweb/uses-passive-event-listeners.js | title":["audits[uses-passive-event-listeners].title"],"core/audits/dobetterweb/uses-passive-event-listeners.js | description":["audits[uses-passive-event-listeners].description"],"core/audits/bf-cache.js | title":["audits[bf-cache].title"],"core/audits/bf-cache.js | description":["audits[bf-cache].description"],"node_modules/@paulirish/trace_engine/models/trace/insights/Cache.js | title":["audits[cache-insight].title"],"node_modules/@paulirish/trace_engine/models/trace/insights/Cache.js | description":["audits[cache-insight].description"],"node_modules/@paulirish/trace_engine/models/trace/insights/CLSCulprits.js | title":["audits[cls-culprits-insight].title"],"node_modules/@paulirish/trace_engine/models/trace/insights/CLSCulprits.js | description":["audits[cls-culprits-insight].description"],"node_modules/@paulirish/trace_engine/models/trace/insights/DocumentLatency.js | title":["audits[document-latency-insight].title"],"node_modules/@paulirish/trace_engine/models/trace/insights/DocumentLatency.js | description":["audits[document-latency-insight].description"],"node_modules/@paulirish/trace_engine/models/trace/insights/DocumentLatency.js | passingRedirects":["audits[document-latency-insight].details.items.noRedirects.label"],"node_modules/@paulirish/trace_engine/models/trace/insights/DocumentLatency.js | passingServerResponseTime":["audits[document-latency-insight].details.items.serverResponseIsFast.label"],"node_modules/@paulirish/trace_engine/models/trace/insights/DocumentLatency.js | passingTextCompression":["audits[document-latency-insight].details.items.usesCompression.label"],"node_modules/@paulirish/trace_engine/models/trace/insights/DOMSize.js | title":["audits[dom-size-insight].title"],"node_modules/@paulirish/trace_engine/models/trace/insights/DOMSize.js | description":["audits[dom-size-insight].description"],"node_modules/@paulirish/trace_engine/models/trace/insights/DOMSize.js | statistic":["audits[dom-size-insight].details.headings[0].label"],"node_modules/@paulirish/trace_engine/models/trace/insights/DOMSize.js | value":["audits[dom-size-insight].details.headings[2].label"],"node_modules/@paulirish/trace_engine/models/trace/insights/DOMSize.js | totalElements":["audits[dom-size-insight].details.items[0].statistic"],"node_modules/@paulirish/trace_engine/models/trace/insights/DOMSize.js | maxChildren":["audits[dom-size-insight].details.items[1].statistic"],"node_modules/@paulirish/trace_engine/models/trace/insights/DOMSize.js | maxDOMDepth":["audits[dom-size-insight].details.items[2].statistic"],"node_modules/@paulirish/trace_engine/models/trace/insights/DuplicatedJavaScript.js | title":["audits[duplicated-javascript-insight].title"],"node_modules/@paulirish/trace_engine/models/trace/insights/DuplicatedJavaScript.js | description":["audits[duplicated-javascript-insight].description"],"node_modules/@paulirish/trace_engine/models/trace/insights/FontDisplay.js | title":["audits[font-display-insight].title"],"node_modules/@paulirish/trace_engine/models/trace/insights/FontDisplay.js | description":["audits[font-display-insight].description"],"node_modules/@paulirish/trace_engine/models/trace/insights/ForcedReflow.js | title":["audits[forced-reflow-insight].title"],"node_modules/@paulirish/trace_engine/models/trace/insights/ForcedReflow.js | description":["audits[forced-reflow-insight].description"],"node_modules/@paulirish/trace_engine/models/trace/insights/ImageDelivery.js | title":["audits[image-delivery-insight].title"],"node_modules/@paulirish/trace_engine/models/trace/insights/ImageDelivery.js | description":["audits[image-delivery-insight].description"],"node_modules/@paulirish/trace_engine/models/trace/insights/InteractionToNextPaint.js | title":["audits[interaction-to-next-paint-insight].title"],"node_modules/@paulirish/trace_engine/models/trace/insights/InteractionToNextPaint.js | description":["audits[interaction-to-next-paint-insight].description"],"node_modules/@paulirish/trace_engine/models/trace/insights/LCPDiscovery.js | title":["audits[lcp-discovery-insight].title"],"node_modules/@paulirish/trace_engine/models/trace/insights/LCPDiscovery.js | description":["audits[lcp-discovery-insight].description"],"node_modules/@paulirish/trace_engine/models/trace/insights/LCPPhases.js | title":["audits[lcp-phases-insight].title"],"node_modules/@paulirish/trace_engine/models/trace/insights/LCPPhases.js | description":["audits[lcp-phases-insight].description"],"node_modules/@paulirish/trace_engine/models/trace/insights/LCPPhases.js | phase":["audits[lcp-phases-insight].details.items[0].headings[0].label"],"node_modules/@paulirish/trace_engine/models/trace/insights/LCPPhases.js | timeToFirstByte":["audits[lcp-phases-insight].details.items[0].items[0].label"],"node_modules/@paulirish/trace_engine/models/trace/insights/LCPPhases.js | elementRenderDelay":["audits[lcp-phases-insight].details.items[0].items[1].label"],"node_modules/@paulirish/trace_engine/models/trace/insights/LegacyJavaScript.js | title":["audits[legacy-javascript-insight].title"],"node_modules/@paulirish/trace_engine/models/trace/insights/LegacyJavaScript.js | description":["audits[legacy-javascript-insight].description"],"node_modules/@paulirish/trace_engine/models/trace/insights/ModernHTTP.js | title":["audits[modern-http-insight].title"],"node_modules/@paulirish/trace_engine/models/trace/insights/ModernHTTP.js | description":["audits[modern-http-insight].description"],"node_modules/@paulirish/trace_engine/models/trace/insights/NetworkDependencyTree.js | title":["audits[network-dependency-tree-insight].title"],"node_modules/@paulirish/trace_engine/models/trace/insights/NetworkDependencyTree.js | description":["audits[network-dependency-tree-insight].description"],"node_modules/@paulirish/trace_engine/models/trace/insights/RenderBlocking.js | title":["audits[render-blocking-insight].title"],"node_modules/@paulirish/trace_engine/models/trace/insights/RenderBlocking.js | description":["audits[render-blocking-insight].description"],"node_modules/@paulirish/trace_engine/models/trace/insights/ThirdParties.js | title":["audits[third-parties-insight].title"],"node_modules/@paulirish/trace_engine/models/trace/insights/ThirdParties.js | description":["audits[third-parties-insight].description"],"node_modules/@paulirish/trace_engine/models/trace/insights/Viewport.js | title":["audits[viewport-insight].title"],"node_modules/@paulirish/trace_engine/models/trace/insights/Viewport.js | description":["audits[viewport-insight].description"],"core/config/default-config.js | performanceCategoryTitle":["categories.performance.title"],"core/config/default-config.js | metricGroupTitle":["categoryGroups.metrics.title"],"core/config/default-config.js | insightsGroupTitle":["categoryGroups.insights.title"],"core/config/default-config.js | insightsGroupDescription":["categoryGroups.insights.description"],"core/config/default-config.js | diagnosticsGroupTitle":["categoryGroups.diagnostics.title"],"core/config/default-config.js | diagnosticsGroupDescription":["categoryGroups.diagnostics.description"],"core/config/default-config.js | a11yBestPracticesGroupTitle":["categoryGroups[a11y-best-practices].title"],"core/config/default-config.js | a11yBestPracticesGroupDescription":["categoryGroups[a11y-best-practices].description"],"core/config/default-config.js | a11yColorContrastGroupTitle":["categoryGroups[a11y-color-contrast].title"],"core/config/default-config.js | a11yColorContrastGroupDescription":["categoryGroups[a11y-color-contrast].description"],"core/config/default-config.js | a11yNamesLabelsGroupTitle":["categoryGroups[a11y-names-labels].title"],"core/config/default-config.js | a11yNamesLabelsGroupDescription":["categoryGroups[a11y-names-labels].description"],"core/config/default-config.js | a11yNavigationGroupTitle":["categoryGroups[a11y-navigation].title"],"core/config/default-config.js | a11yNavigationGroupDescription":["categoryGroups[a11y-navigation].description"],"core/config/default-config.js | a11yAriaGroupTitle":["categoryGroups[a11y-aria].title"],"core/config/default-config.js | a11yAriaGroupDescription":["categoryGroups[a11y-aria].description"],"core/config/default-config.js | a11yLanguageGroupTitle":["categoryGroups[a11y-language].title"],"core/config/default-config.js | a11yLanguageGroupDescription":["categoryGroups[a11y-language].description"],"core/config/default-config.js | a11yAudioVideoGroupTitle":["categoryGroups[a11y-audio-video].title"],"core/config/default-config.js | a11yAudioVideoGroupDescription":["categoryGroups[a11y-audio-video].description"],"core/config/default-config.js | a11yTablesListsVideoGroupTitle":["categoryGroups[a11y-tables-lists].title"],"core/config/default-config.js | a11yTablesListsVideoGroupDescription":["categoryGroups[a11y-tables-lists].description"],"core/config/default-config.js | seoMobileGroupTitle":["categoryGroups[seo-mobile].title"],"core/config/default-config.js | seoMobileGroupDescription":["categoryGroups[seo-mobile].description"],"core/config/default-config.js | seoContentGroupTitle":["categoryGroups[seo-content].title"],"core/config/default-config.js | seoContentGroupDescription":["categoryGroups[seo-content].description"],"core/config/default-config.js | seoCrawlingGroupTitle":["categoryGroups[seo-crawl].title"],"core/config/default-config.js | seoCrawlingGroupDescription":["categoryGroups[seo-crawl].description"],"core/config/default-config.js | bestPracticesTrustSafetyGroupTitle":["categoryGroups[best-practices-trust-safety].title"],"core/config/default-config.js | bestPracticesUXGroupTitle":["categoryGroups[best-practices-ux].title"],"core/config/default-config.js | bestPracticesBrowserCompatGroupTitle":["categoryGroups[best-practices-browser-compat].title"],"core/config/default-config.js | bestPracticesGeneralGroupTitle":["categoryGroups[best-practices-general].title"]}}};</script>
  <script>"use strict";(()=>{var Ne=.8999999999999999,He=.5,Ue=.49999999999999994;function Oe(o){let e=Math.sign(o);o=Math.abs(o);let t=.254829592,n=-.284496736,r=1.421413741,i=-1.453152027,a=1.061405429,s=1/(1+.3275911*o),c=s*(t+s*(n+s*(r+s*(i+s*a))));return e*(1-c*Math.exp(-o*o))}function be({median:o,p10:e},t){if(o<=0)throw new Error("median must be greater than zero");if(e<=0)throw new Error("p10 must be greater than zero");if(e>=o)throw new Error("p10 must be less than the median");if(t<=0)return 1;let n=.9061938024368232,r=Math.max(Number.MIN_VALUE,t/o),i=Math.log(r),a=Math.max(Number.MIN_VALUE,e/o),l=-Math.log(a),s=i*n/l,c=(1-Oe(s))/2,d;return t<=e?d=Math.max(.9,Math.min(1,c)):t<=o?d=Math.max(He,Math.min(Ne,c)):d=Math.max(0,Math.min(Ue,c)),d}var O="\u2026",Ve="\xA0",ve=.9,Ge={PASS:{label:"pass",minScore:ve},AVERAGE:{label:"average",minScore:.5},FAIL:{label:"fail"},ERROR:{label:"error"}},Be=["com","co","gov","edu","ac","org","go","gob","or","net","in","ne","nic","gouv","web","spb","blog","jus","kiev","mil","wi","qc","ca","bel","on"],E=class o{static get RATINGS(){return Ge}static get PASS_THRESHOLD(){return ve}static get MS_DISPLAY_VALUE(){return`%10d${Ve}ms`}static getFinalDisplayedUrl(e){if(e.finalDisplayedUrl)return e.finalDisplayedUrl;if(e.finalUrl)return e.finalUrl;throw new Error("Could not determine final displayed URL")}static getMainDocumentUrl(e){return e.mainDocumentUrl||e.finalUrl}static getFullPageScreenshot(e){return e.fullPageScreenshot?e.fullPageScreenshot:e.audits["full-page-screenshot"]?.details}static getEntityFromUrl(e,t){return t&&t.find(r=>r.origins.find(i=>e.startsWith(i)))||o.getPseudoRootDomain(e)}static splitMarkdownCodeSpans(e){let t=[],n=e.split(/`(.*?)`/g);for(let r=0;r<n.length;r++){let i=n[r];if(!i)continue;let a=r%2!==0;t.push({isCode:a,text:i})}return t}static splitMarkdownLink(e){let t=[],n=e.split(/\[([^\]]+?)\]\((https?:\/\/.*?)\)/g);for(;n.length;){let[r,i,a]=n.splice(0,3);r&&t.push({isLink:!1,text:r}),i&&a&&t.push({isLink:!0,text:i,linkHref:a})}return t}static truncate(e,t,n="\u2026"){if(e.length<=t)return e;let i=new Intl.Segmenter(void 0,{granularity:"grapheme"}).segment(e)[Symbol.iterator](),a=0;for(let l=0;l<=t-n.length;l++){let s=i.next();if(s.done)return e;a=s.value.index}for(let l=0;l<n.length;l++)if(i.next().done)return e;return e.slice(0,a)+n}static getURLDisplayName(e,t){t=t||{numPathParts:void 0,preserveQuery:void 0,preserveHost:void 0};let n=t.numPathParts!==void 0?t.numPathParts:2,r=t.preserveQuery!==void 0?t.preserveQuery:!0,i=t.preserveHost||!1,a;if(e.protocol==="about:"||e.protocol==="data:")a=e.href;else{a=e.pathname;let s=a.split("/").filter(c=>c.length);n&&s.length>n&&(a=O+s.slice(-1*n).join("/")),i&&(a=`${e.host}/${a.replace(/^\//,"")}`),r&&(a=`${a}${e.search}`)}let l=64;if(e.protocol!=="data:"&&(a=a.slice(0,200),a=a.replace(/([a-f0-9]{7})[a-f0-9]{13}[a-f0-9]*/g,`$1${O}`),a=a.replace(/([a-zA-Z0-9-_]{9})(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])[a-zA-Z0-9-_]{10,}/g,`$1${O}`),a=a.replace(/(\d{3})\d{6,}/g,`$1${O}`),a=a.replace(/\u2026+/g,O),a.length>l&&a.includes("?")&&(a=a.replace(/\?([^=]*)(=)?.*/,`?$1$2${O}`),a.length>l&&(a=a.replace(/\?.*/,`?${O}`)))),a.length>l){let s=a.lastIndexOf(".");s>=0?a=a.slice(0,l-1-(a.length-s))+`${O}${a.slice(s)}`:a=a.slice(0,l-1)+O}return a}static getChromeExtensionOrigin(e){let t=new URL(e);return t.protocol+"//"+t.host}static parseURL(e){let t=new URL(e);return{file:o.getURLDisplayName(t),hostname:t.hostname,origin:t.protocol==="chrome-extension:"?o.getChromeExtensionOrigin(e):t.origin}}static createOrReturnURL(e){return e instanceof URL?e:new URL(e)}static getPseudoTld(e){let t=e.split(".").slice(-2);return Be.includes(t[0])?`.${t.join(".")}`:`.${t[t.length-1]}`}static getPseudoRootDomain(e){let t=o.createOrReturnURL(e).hostname,r=o.getPseudoTld(t).split(".");return t.split(".").slice(-r.length).join(".")}static filterRelevantLines(e,t,n){if(t.length===0)return e.slice(0,n*2+1);let r=3,i=new Set;return t=t.sort((a,l)=>(a.lineNumber||0)-(l.lineNumber||0)),t.forEach(({lineNumber:a})=>{let l=a-n,s=a+n;for(;l<1;)l++,s++;i.has(l-r-1)&&(l-=r);for(let c=l;c<=s;c++){let d=c;i.add(d)}}),e.filter(a=>i.has(a.lineNumber))}static computeLogNormalScore(e,t){let n=be(e,t);return n>.9&&(n+=.05*(n-.9)),Math.floor(n*100)/100}};function qe(o){let e=o.createFragment(),t=o.createElement("style");t.append(`
    .lh-3p-filter {
      color: var(--color-gray-600);
      float: right;
      padding: 6px var(--stackpack-padding-horizontal);
    }
    .lh-3p-filter-label, .lh-3p-filter-input {
      vertical-align: middle;
      user-select: none;
    }
    .lh-3p-filter-input:disabled + .lh-3p-ui-string {
      text-decoration: line-through;
    }
  `),e.append(t);let n=o.createElement("div","lh-3p-filter"),r=o.createElement("label","lh-3p-filter-label"),i=o.createElement("input","lh-3p-filter-input");i.setAttribute("type","checkbox"),i.setAttribute("checked","");let a=o.createElement("span","lh-3p-ui-string");a.append("Show 3rd party resources");let l=o.createElement("span","lh-3p-filter-count");return r.append(" ",i," ",a," (",l,") "),n.append(" ",r," "),e.append(n),e}function je(o){let e=o.createFragment(),t=o.createElement("div","lh-audit"),n=o.createElement("details","lh-expandable-details"),r=o.createElement("summary"),i=o.createElement("div","lh-audit__header lh-expandable-details__summary"),a=o.createElement("span","lh-audit__score-icon"),l=o.createElement("span","lh-audit__title-and-text"),s=o.createElement("span","lh-audit__title"),c=o.createElement("span","lh-audit__display-text");l.append(" ",s," ",c," ");let d=o.createElement("div","lh-chevron-container");i.append(" ",a," ",l," ",d," "),r.append(" ",i," ");let h=o.createElement("div","lh-audit__description"),p=o.createElement("div","lh-audit__stackpacks");return n.append(" ",r," ",h," ",p," "),t.append(" ",n," "),e.append(t),e}function We(o){let e=o.createFragment(),t=o.createElement("div","lh-category-header"),n=o.createElement("div","lh-score__gauge");n.setAttribute("role","heading"),n.setAttribute("aria-level","2");let r=o.createElement("div","lh-category-header__description");return t.append(" ",n," ",r," "),e.append(t),e}function Ke(o){let e=o.createFragment(),t=o.createElementNS("http://www.w3.org/2000/svg","svg","lh-chevron");t.setAttribute("viewBox","0 0 100 100");let n=o.createElementNS("http://www.w3.org/2000/svg","g","lh-chevron__lines"),r=o.createElementNS("http://www.w3.org/2000/svg","path","lh-chevron__line lh-chevron__line-left");r.setAttribute("d","M10 50h40");let i=o.createElementNS("http://www.w3.org/2000/svg","path","lh-chevron__line lh-chevron__line-right");return i.setAttribute("d","M90 50H50"),n.append(" ",r," ",i," "),t.append(" ",n," "),e.append(t),e}function Ze(o){let e=o.createFragment(),t=o.createElement("div","lh-audit-group"),n=o.createElement("details","lh-clump"),r=o.createElement("summary"),i=o.createElement("div","lh-audit-group__summary"),a=o.createElement("div","lh-audit-group__header"),l=o.createElement("span","lh-audit-group__title"),s=o.createElement("span","lh-audit-group__itemcount");a.append(" ",l," ",s," "," "," ");let c=o.createElement("div","lh-clump-toggle"),d=o.createElement("span","lh-clump-toggletext--show"),h=o.createElement("span","lh-clump-toggletext--hide");return c.append(" ",d," ",h," "),i.append(" ",a," ",c," "),r.append(" ",i," "),n.append(" ",r," "),t.append(" "," ",n," "),e.append(t),e}function Je(o){let e=o.createFragment(),t=o.createElement("div","lh-crc-container"),n=o.createElement("style");n.append(`
      .lh-crc .lh-tree-marker {
        width: 12px;
        height: 26px;
        display: block;
        float: left;
        background-position: top left;
      }
      .lh-crc .lh-horiz-down {
        background: url('data:image/svg+xml;utf8,<svg width="16" height="26" viewBox="0 0 16 26" xmlns="http://www.w3.org/2000/svg"><g fill="%23D8D8D8" fill-rule="evenodd"><path d="M16 12v2H-2v-2z"/><path d="M9 12v14H7V12z"/>\u003c/g>\u003c/svg>');
      }
      .lh-crc .lh-right {
        background: url('data:image/svg+xml;utf8,<svg width="16" height="26" viewBox="0 0 16 26" xmlns="http://www.w3.org/2000/svg"><path d="M16 12v2H0v-2z" fill="%23D8D8D8" fill-rule="evenodd"/>\u003c/svg>');
      }
      .lh-crc .lh-up-right {
        background: url('data:image/svg+xml;utf8,<svg width="16" height="26" viewBox="0 0 16 26" xmlns="http://www.w3.org/2000/svg"><path d="M7 0h2v14H7zm2 12h7v2H9z" fill="%23D8D8D8" fill-rule="evenodd"/>\u003c/svg>');
      }
      .lh-crc .lh-vert-right {
        background: url('data:image/svg+xml;utf8,<svg width="16" height="26" viewBox="0 0 16 26" xmlns="http://www.w3.org/2000/svg"><path d="M7 0h2v27H7zm2 12h7v2H9z" fill="%23D8D8D8" fill-rule="evenodd"/>\u003c/svg>');
      }
      .lh-crc .lh-vert {
        background: url('data:image/svg+xml;utf8,<svg width="16" height="26" viewBox="0 0 16 26" xmlns="http://www.w3.org/2000/svg"><path d="M7 0h2v26H7z" fill="%23D8D8D8" fill-rule="evenodd"/>\u003c/svg>');
      }
      .lh-crc .lh-crc-tree {
        font-size: 14px;
        width: 100%;
        overflow-x: auto;
      }
      .lh-crc .lh-crc-node {
        height: 26px;
        line-height: 26px;
        white-space: nowrap;
      }
      .lh-crc .lh-crc-node__longest {
        color: var(--color-average-secondary);
      }
      .lh-crc .lh-crc-node__tree-value {
        margin-left: 10px;
      }
      .lh-crc .lh-crc-node__tree-value div {
        display: inline;
      }
      .lh-crc .lh-crc-node__chain-duration {
        font-weight: 700;
      }
      .lh-crc .lh-crc-initial-nav {
        color: #595959;
        font-style: italic;
      }
      .lh-crc__summary-value {
        margin-bottom: 10px;
      }
    `);let r=o.createElement("div"),i=o.createElement("div","lh-crc__summary-value"),a=o.createElement("span","lh-crc__longest_duration_label"),l=o.createElement("b","lh-crc__longest_duration");i.append(" ",a," ",l," "),r.append(" ",i," ");let s=o.createElement("div","lh-crc"),c=o.createElement("div","lh-crc-initial-nav");return s.append(" ",c," "," "),t.append(" ",n," ",r," ",s," "),e.append(t),e}function Qe(o){let e=o.createFragment(),t=o.createElement("div","lh-crc-node"),n=o.createElement("span","lh-crc-node__tree-marker"),r=o.createElement("span","lh-crc-node__tree-value");return t.append(" ",n," ",r," "),e.append(t),e}function Ye(o){let e=o.createFragment(),t=o.createElement("div","lh-element-screenshot"),n=o.createElement("div","lh-element-screenshot__content"),r=o.createElement("div","lh-element-screenshot__image"),i=o.createElement("div","lh-element-screenshot__mask"),a=o.createElementNS("http://www.w3.org/2000/svg","svg");a.setAttribute("height","0"),a.setAttribute("width","0");let l=o.createElementNS("http://www.w3.org/2000/svg","defs"),s=o.createElementNS("http://www.w3.org/2000/svg","clipPath");s.setAttribute("clipPathUnits","objectBoundingBox"),l.append(" ",s," "," "),a.append(" ",l," "),i.append(" ",a," ");let c=o.createElement("div","lh-element-screenshot__element-marker");return r.append(" ",i," ",c," "),n.append(" ",r," "),t.append(" ",n," "),e.append(t),e}function Xe(o){let e=o.createFragment(),t=o.createElement("div","lh-exp-gauge-component"),n=o.createElement("div","lh-exp-gauge__wrapper");n.setAttribute("target","_blank");let r=o.createElement("div","lh-exp-gauge__svg-wrapper"),i=o.createElementNS("http://www.w3.org/2000/svg","svg","lh-exp-gauge"),a=o.createElementNS("http://www.w3.org/2000/svg","g","lh-exp-gauge__inner"),l=o.createElementNS("http://www.w3.org/2000/svg","circle","lh-exp-gauge__bg"),s=o.createElementNS("http://www.w3.org/2000/svg","circle","lh-exp-gauge__base lh-exp-gauge--faded"),c=o.createElementNS("http://www.w3.org/2000/svg","circle","lh-exp-gauge__arc"),d=o.createElementNS("http://www.w3.org/2000/svg","text","lh-exp-gauge__percentage");a.append(" ",l," ",s," ",c," ",d," ");let h=o.createElementNS("http://www.w3.org/2000/svg","g","lh-exp-gauge__outer"),p=o.createElementNS("http://www.w3.org/2000/svg","circle","lh-cover");h.append(" ",p," ");let g=o.createElementNS("http://www.w3.org/2000/svg","text","lh-exp-gauge__label");return g.setAttribute("text-anchor","middle"),g.setAttribute("x","0"),g.setAttribute("y","60"),i.append(" ",a," ",h," ",g," "),r.append(" ",i," "),n.append(" ",r," "),t.append(" ",n," "),e.append(t),e}function et(o){let e=o.createFragment(),t=o.createElement("style");t.append(`
    .lh-footer {
      padding: var(--footer-padding-vertical) calc(var(--default-padding) * 2);
      max-width: var(--report-content-max-width);
      margin: 0 auto;
    }
    .lh-footer .lh-generated {
      text-align: center;
    }
  `),e.append(t);let n=o.createElement("footer","lh-footer"),r=o.createElement("ul","lh-meta__items");r.append(" ");let i=o.createElement("div","lh-generated"),a=o.createElement("b");a.append("Lighthouse");let l=o.createElement("span","lh-footer__version"),s=o.createElement("a","lh-footer__version_issue");return s.setAttribute("href","https://github.com/GoogleChrome/Lighthouse/issues"),s.setAttribute("target","_blank"),s.setAttribute("rel","noopener"),s.append("File an issue"),i.append(" "," Generated by ",a," ",l," | ",s," "),n.append(" ",r," ",i," "),e.append(n),e}function tt(o){let e=o.createFragment(),t=o.createElement("a","lh-fraction__wrapper"),n=o.createElement("div","lh-fraction__content-wrapper"),r=o.createElement("div","lh-fraction__content"),i=o.createElement("div","lh-fraction__background");r.append(" ",i," "),n.append(" ",r," ");let a=o.createElement("div","lh-fraction__label");return t.append(" ",n," ",a," "),e.append(t),e}function nt(o){let e=o.createFragment(),t=o.createElement("a","lh-gauge__wrapper"),n=o.createElement("div","lh-gauge__svg-wrapper"),r=o.createElementNS("http://www.w3.org/2000/svg","svg","lh-gauge");r.setAttribute("viewBox","0 0 120 120");let i=o.createElementNS("http://www.w3.org/2000/svg","circle","lh-gauge-base");i.setAttribute("r","56"),i.setAttribute("cx","60"),i.setAttribute("cy","60"),i.setAttribute("stroke-width","8");let a=o.createElementNS("http://www.w3.org/2000/svg","circle","lh-gauge-arc");a.setAttribute("r","56"),a.setAttribute("cx","60"),a.setAttribute("cy","60"),a.setAttribute("stroke-width","8"),r.append(" ",i," ",a," "),n.append(" ",r," ");let l=o.createElement("div","lh-gauge__percentage"),s=o.createElement("div","lh-gauge__label");return t.append(" "," ",n," ",l," "," ",s," "),e.append(t),e}function rt(o){let e=o.createFragment(),t=o.createElement("style");t.append(`
    /* CSS Fireworks. Originally by Eddie Lin
       https://codepen.io/paulirish/pen/yEVMbP
    */
    .lh-pyro {
      display: none;
      z-index: 1;
      pointer-events: none;
    }
    .lh-score100 .lh-pyro {
      display: block;
    }
    .lh-score100 .lh-lighthouse stop:first-child {
      stop-color: hsla(200, 12%, 95%, 0);
    }
    .lh-score100 .lh-lighthouse stop:last-child {
      stop-color: hsla(65, 81%, 76%, 1);
    }

    .lh-pyro > .lh-pyro-before, .lh-pyro > .lh-pyro-after {
      position: absolute;
      width: 5px;
      height: 5px;
      border-radius: 2.5px;
      box-shadow: 0 0 #fff, 0 0 #fff, 0 0 #fff, 0 0 #fff, 0 0 #fff, 0 0 #fff, 0 0 #fff, 0 0 #fff, 0 0 #fff, 0 0 #fff, 0 0 #fff, 0 0 #fff, 0 0 #fff, 0 0 #fff, 0 0 #fff, 0 0 #fff, 0 0 #fff, 0 0 #fff, 0 0 #fff, 0 0 #fff, 0 0 #fff, 0 0 #fff, 0 0 #fff, 0 0 #fff, 0 0 #fff, 0 0 #fff, 0 0 #fff, 0 0 #fff, 0 0 #fff, 0 0 #fff, 0 0 #fff, 0 0 #fff, 0 0 #fff, 0 0 #fff, 0 0 #fff, 0 0 #fff, 0 0 #fff, 0 0 #fff, 0 0 #fff, 0 0 #fff, 0 0 #fff, 0 0 #fff, 0 0 #fff, 0 0 #fff, 0 0 #fff, 0 0 #fff, 0 0 #fff, 0 0 #fff, 0 0 #fff, 0 0 #fff, 0 0 #fff;
      animation: 1s bang ease-out infinite backwards,  1s gravity ease-in infinite backwards,  5s position linear infinite backwards;
      animation-delay: 1s, 1s, 1s;
    }

    .lh-pyro > .lh-pyro-after {
      animation-delay: 2.25s, 2.25s, 2.25s;
      animation-duration: 1.25s, 1.25s, 6.25s;
    }

    @keyframes bang {
      to {
        opacity: 1;
        box-shadow: -70px -115.67px #47ebbc, -28px -99.67px #eb47a4, 58px -31.67px #7eeb47, 13px -141.67px #eb47c5, -19px 6.33px #7347eb, -2px -74.67px #ebd247, 24px -151.67px #eb47e0, 57px -138.67px #b4eb47, -51px -104.67px #479eeb, 62px 8.33px #ebcf47, -93px 0.33px #d547eb, -16px -118.67px #47bfeb, 53px -84.67px #47eb83, 66px -57.67px #eb47bf, -93px -65.67px #91eb47, 30px -13.67px #86eb47, -2px -59.67px #83eb47, -44px 1.33px #eb47eb, 61px -58.67px #47eb73, 5px -22.67px #47e8eb, -66px -28.67px #ebe247, 42px -123.67px #eb5547, -75px 26.33px #7beb47, 15px -52.67px #a147eb, 36px -51.67px #eb8347, -38px -12.67px #eb5547, -46px -59.67px #47eb81, 78px -114.67px #eb47ba, 15px -156.67px #eb47bf, -36px 1.33px #eb4783, -72px -86.67px #eba147, 31px -46.67px #ebe247, -68px 29.33px #47e2eb, -55px 19.33px #ebe047, -56px 27.33px #4776eb, -13px -91.67px #eb5547, -47px -138.67px #47ebc7, -18px -96.67px #eb47ac, 11px -88.67px #4783eb, -67px -28.67px #47baeb, 53px 10.33px #ba47eb, 11px 19.33px #5247eb, -5px -11.67px #eb4791, -68px -4.67px #47eba7, 95px -37.67px #eb478b, -67px -162.67px #eb5d47, -54px -120.67px #eb6847, 49px -12.67px #ebe047, 88px 8.33px #47ebda, 97px 33.33px #eb8147, 6px -71.67px #ebbc47;
      }
    }
    @keyframes gravity {
      from {
        opacity: 1;
      }
      to {
        transform: translateY(80px);
        opacity: 0;
      }
    }
    @keyframes position {
      0%, 19.9% {
        margin-top: 4%;
        margin-left: 47%;
      }
      20%, 39.9% {
        margin-top: 7%;
        margin-left: 30%;
      }
      40%, 59.9% {
        margin-top: 6%;
        margin-left: 70%;
      }
      60%, 79.9% {
        margin-top: 3%;
        margin-left: 20%;
      }
      80%, 99.9% {
        margin-top: 3%;
        margin-left: 80%;
      }
    }
  `),e.append(t);let n=o.createElement("div","lh-header-container"),r=o.createElement("div","lh-scores-wrapper-placeholder");return n.append(" ",r," "),e.append(n),e}function ot(o){let e=o.createFragment(),t=o.createElement("div","lh-metric"),n=o.createElement("div","lh-metric__innerwrap"),r=o.createElement("div","lh-metric__icon"),i=o.createElement("span","lh-metric__title"),a=o.createElement("div","lh-metric__value"),l=o.createElement("div","lh-metric__description");return n.append(" ",r," ",i," ",a," ",l," "),t.append(" ",n," "),e.append(t),e}function it(o){let e=o.createFragment(),t=o.createElement("div","lh-scorescale"),n=o.createElement("span","lh-scorescale-range lh-scorescale-range--fail");n.append("0\u201349");let r=o.createElement("span","lh-scorescale-range lh-scorescale-range--average");r.append("50\u201389");let i=o.createElement("span","lh-scorescale-range lh-scorescale-range--pass");return i.append("90\u2013100"),t.append(" ",n," ",r," ",i," "),e.append(t),e}function at(o){let e=o.createFragment(),t=o.createElement("style");t.append(`
    .lh-scores-container {
      display: flex;
      flex-direction: column;
      padding: var(--default-padding) 0;
      position: relative;
      width: 100%;
    }

    .lh-sticky-header {
      --gauge-circle-size: var(--gauge-circle-size-sm);
      --plugin-badge-size: 16px;
      --plugin-icon-size: 75%;
      --gauge-wrapper-width: 60px;
      --gauge-percentage-font-size: 13px;
      position: fixed;
      left: 0;
      right: 0;
      top: var(--topbar-height);
      font-weight: 500;
      display: none;
      justify-content: center;
      background-color: var(--sticky-header-background-color);
      border-bottom: 1px solid var(--color-gray-200);
      padding-top: var(--score-container-padding);
      padding-bottom: 4px;
      z-index: 2;
      pointer-events: none;
    }

    .lh-devtools .lh-sticky-header {
      /* The report within DevTools is placed in a container with overflow, which changes the placement of this header unless we change \`position\` to \`sticky.\` */
      position: sticky;
    }

    .lh-sticky-header--visible {
      display: grid;
      grid-auto-flow: column;
      pointer-events: auto;
    }

    /* Disable the gauge arc animation for the sticky header, so toggling display: none
       does not play the animation. */
    .lh-sticky-header .lh-gauge-arc {
      animation: none;
    }

    .lh-sticky-header .lh-gauge__label,
    .lh-sticky-header .lh-fraction__label {
      display: none;
    }

    .lh-highlighter {
      width: var(--gauge-wrapper-width);
      height: 1px;
      background-color: var(--highlighter-background-color);
      /* Position at bottom of first gauge in sticky header. */
      position: absolute;
      grid-column: 1;
      bottom: -1px;
      left: 0px;
      right: 0px;
    }
  `),e.append(t);let n=o.createElement("div","lh-scores-wrapper"),r=o.createElement("div","lh-scores-container"),i=o.createElement("div","lh-pyro"),a=o.createElement("div","lh-pyro-before"),l=o.createElement("div","lh-pyro-after");return i.append(" ",a," ",l," "),r.append(" ",i," "),n.append(" ",r," "),e.append(n),e}function lt(o){let e=o.createFragment(),t=o.createElement("div","lh-snippet"),n=o.createElement("style");return n.append(`
          :root {
            --snippet-highlight-light: #fbf1f2;
            --snippet-highlight-dark: #ffd6d8;
          }

         .lh-snippet__header {
          position: relative;
          overflow: hidden;
          padding: 10px;
          border-bottom: none;
          color: var(--snippet-color);
          background-color: var(--snippet-background-color);
          border: 1px solid var(--report-border-color-secondary);
        }
        .lh-snippet__title {
          font-weight: bold;
          float: left;
        }
        .lh-snippet__node {
          float: left;
          margin-left: 4px;
        }
        .lh-snippet__toggle-expand {
          padding: 1px 7px;
          margin-top: -1px;
          margin-right: -7px;
          float: right;
          background: transparent;
          border: none;
          cursor: pointer;
          font-size: 14px;
          color: #0c50c7;
        }

        .lh-snippet__snippet {
          overflow: auto;
          border: 1px solid var(--report-border-color-secondary);
        }
        /* Container needed so that all children grow to the width of the scroll container */
        .lh-snippet__snippet-inner {
          display: inline-block;
          min-width: 100%;
        }

        .lh-snippet:not(.lh-snippet--expanded) .lh-snippet__show-if-expanded {
          display: none;
        }
        .lh-snippet.lh-snippet--expanded .lh-snippet__show-if-collapsed {
          display: none;
        }

        .lh-snippet__line {
          background: white;
          white-space: pre;
          display: flex;
        }
        .lh-snippet__line:not(.lh-snippet__line--message):first-child {
          padding-top: 4px;
        }
        .lh-snippet__line:not(.lh-snippet__line--message):last-child {
          padding-bottom: 4px;
        }
        .lh-snippet__line--content-highlighted {
          background: var(--snippet-highlight-dark);
        }
        .lh-snippet__line--message {
          background: var(--snippet-highlight-light);
        }
        .lh-snippet__line--message .lh-snippet__line-number {
          padding-top: 10px;
          padding-bottom: 10px;
        }
        .lh-snippet__line--message code {
          padding: 10px;
          padding-left: 5px;
          color: var(--color-fail);
          font-family: var(--report-font-family);
        }
        .lh-snippet__line--message code {
          white-space: normal;
        }
        .lh-snippet__line-icon {
          padding-top: 10px;
          display: none;
        }
        .lh-snippet__line--message .lh-snippet__line-icon {
          display: block;
        }
        .lh-snippet__line-icon:before {
          content: "";
          display: inline-block;
          vertical-align: middle;
          margin-right: 4px;
          width: var(--score-icon-size);
          height: var(--score-icon-size);
          background-image: var(--fail-icon-url);
        }
        .lh-snippet__line-number {
          flex-shrink: 0;
          width: 40px;
          text-align: right;
          font-family: monospace;
          padding-right: 5px;
          margin-right: 5px;
          color: var(--color-gray-600);
          user-select: none;
        }
    `),t.append(" ",n," "),e.append(t),e}function st(o){let e=o.createFragment(),t=o.createElement("div","lh-snippet__snippet"),n=o.createElement("div","lh-snippet__snippet-inner");return t.append(" ",n," "),e.append(t),e}function ct(o){let e=o.createFragment(),t=o.createElement("div","lh-snippet__header"),n=o.createElement("div","lh-snippet__title"),r=o.createElement("div","lh-snippet__node"),i=o.createElement("button","lh-snippet__toggle-expand"),a=o.createElement("span","lh-snippet__btn-label-collapse lh-snippet__show-if-expanded"),l=o.createElement("span","lh-snippet__btn-label-expand lh-snippet__show-if-collapsed");return i.append(" ",a," ",l," "),t.append(" ",n," ",r," ",i," "),e.append(t),e}function dt(o){let e=o.createFragment(),t=o.createElement("div","lh-snippet__line"),n=o.createElement("div","lh-snippet__line-number"),r=o.createElement("div","lh-snippet__line-icon"),i=o.createElement("code");return t.append(" ",n," ",r," ",i," "),e.append(t),e}function ht(o){let e=o.createFragment(),t=o.createElement("style");return t.append(`/**
 * @license
 * Copyright 2017 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

/*
  Naming convention:

  If a variable is used for a specific component: --{component}-{property name}-{modifier}

  Both {component} and {property name} should be kebab-case. If the target is the entire page,
  use 'report' for the component. The property name should not be abbreviated. Use the
  property name the variable is intended for - if it's used for multiple, a common descriptor
  is fine (ex: 'size' for a variable applied to 'width' and 'height'). If a variable is shared
  across multiple components, either create more variables or just drop the "{component}-"
  part of the name. Append any modifiers at the end (ex: 'big', 'dark').

  For colors: --color-{hue}-{intensity}

  {intensity} is the Material Design tag - 700, A700, etc.
*/
.lh-vars {
  /* Palette using Material Design Colors
   * https://www.materialui.co/colors */
  --color-amber-50: #FFF8E1;
  --color-blue-200: #90CAF9;
  --color-blue-900: #0D47A1;
  --color-blue-A700: #2962FF;
  --color-blue-primary: #06f;
  --color-cyan-500: #00BCD4;
  --color-gray-100: #F5F5F5;
  --color-gray-300: #CFCFCF;
  --color-gray-200: #E0E0E0;
  --color-gray-400: #BDBDBD;
  --color-gray-50: #FAFAFA;
  --color-gray-500: #9E9E9E;
  --color-gray-600: #757575;
  --color-gray-700: #616161;
  --color-gray-800: #424242;
  --color-gray-900: #212121;
  --color-gray: #000000;
  --color-green-700: #080;
  --color-green: #0c6;
  --color-lime-400: #D3E156;
  --color-orange-50: #FFF3E0;
  --color-orange-700: #C33300;
  --color-orange: #fa3;
  --color-red-700: #c00;
  --color-red: #f33;
  --color-teal-600: #00897B;
  --color-white: #FFFFFF;

  /* Context-specific colors */
  --color-average-secondary: var(--color-orange-700);
  --color-average: var(--color-orange);
  --color-fail-secondary: var(--color-red-700);
  --color-fail: var(--color-red);
  --color-hover: var(--color-gray-50);
  --color-informative: var(--color-blue-900);
  --color-pass-secondary: var(--color-green-700);
  --color-pass: var(--color-green);
  --color-not-applicable: var(--color-gray-600);

  /* Component variables */
  --audit-description-padding-left: calc(var(--score-icon-size) + var(--score-icon-margin-left) + var(--score-icon-margin-right));
  --audit-explanation-line-height: 16px;
  --audit-group-margin-bottom: calc(var(--default-padding) * 6);
  --audit-group-padding-vertical: 8px;
  --audit-margin-horizontal: 5px;
  --audit-padding-vertical: 8px;
  --category-padding: calc(var(--default-padding) * 6) var(--edge-gap-padding) calc(var(--default-padding) * 4);
  --chevron-line-stroke: var(--color-gray-600);
  --chevron-size: 12px;
  --default-padding: 8px;
  --edge-gap-padding: calc(var(--default-padding) * 4);
  --env-item-background-color: var(--color-gray-100);
  --env-item-font-size: 28px;
  --env-item-line-height: 36px;
  --env-item-padding: 10px 0px;
  --env-name-min-width: 220px;
  --footer-padding-vertical: 16px;
  --gauge-circle-size-big: 96px;
  --gauge-circle-size: 48px;
  --gauge-circle-size-sm: 32px;
  --gauge-label-font-size-big: 18px;
  --gauge-label-font-size: var(--report-font-size-secondary);
  --gauge-label-line-height-big: 24px;
  --gauge-label-line-height: var(--report-line-height-secondary);
  --gauge-percentage-font-size-big: 38px;
  --gauge-percentage-font-size: var(--report-font-size-secondary);
  --gauge-wrapper-width: 120px;
  --header-line-height: 24px;
  --highlighter-background-color: var(--report-text-color);
  --icon-square-size: calc(var(--score-icon-size) * 0.88);
  --image-preview-size: 48px;
  --link-color: var(--color-blue-primary);
  --locale-selector-background-color: var(--color-white);
  --metric-toggle-lines-fill: #7F7F7F;
  --metric-value-font-size: calc(var(--report-font-size) * 1.8);
  --metrics-toggle-background-color: var(--color-gray-200);
  --plugin-badge-background-color: var(--color-white);
  --plugin-badge-size-big: calc(var(--gauge-circle-size-big) / 2.7);
  --plugin-badge-size: calc(var(--gauge-circle-size) / 2.7);
  --plugin-icon-size: 65%;
  --report-background-color: #fff;
  --report-border-color-secondary: #ebebeb;
  --report-font-family-monospace: monospace, 'Roboto Mono', 'Menlo', 'dejavu sans mono', 'Consolas', 'Lucida Console';
  --report-font-family: system-ui, Roboto, Helvetica, Arial, sans-serif;
  --report-font-size: 14px;
  --report-font-size-secondary: 12px;
  --report-icon-size: var(--score-icon-background-size);
  --report-line-height: 24px;
  --report-line-height-secondary: 20px;
  --report-monospace-font-size: calc(var(--report-font-size) * 0.85);
  --report-text-color-secondary: var(--color-gray-800);
  --report-text-color: var(--color-gray-900);
  --report-content-max-width: calc(60 * var(--report-font-size)); /* defaults to 840px */
  --report-content-min-width: 360px;
  --report-content-max-width-minus-edge-gap: calc(var(--report-content-max-width) - var(--edge-gap-padding) * 2);
  --score-container-padding: 8px;
  --score-icon-background-size: 24px;
  --score-icon-margin-left: 6px;
  --score-icon-margin-right: 14px;
  --score-icon-margin: 0 var(--score-icon-margin-right) 0 var(--score-icon-margin-left);
  --score-icon-size: 12px;
  --score-icon-size-big: 16px;
  --screenshot-overlay-background: rgba(0, 0, 0, 0.3);
  --section-padding-vertical: calc(var(--default-padding) * 6);
  --snippet-background-color: var(--color-gray-50);
  --snippet-color: #0938C2;
  --stackpack-padding-horizontal: 10px;
  --sticky-header-background-color: var(--report-background-color);
  --sticky-header-buffer: var(--topbar-height);
  --sticky-header-height: calc(var(--gauge-circle-size-sm) + var(--score-container-padding) * 2 + 1em);
  --table-group-header-background-color: #EEF1F4;
  --table-group-header-text-color: var(--color-gray-700);
  --table-higlight-background-color: #F5F7FA;
  --tools-icon-color: var(--color-gray-600);
  --topbar-background-color: var(--color-white);
  --topbar-height: 32px;
  --topbar-logo-size: 24px;
  --topbar-padding: 0 8px;
  --toplevel-warning-background-color: hsla(30, 100%, 75%, 10%);
  --toplevel-warning-message-text-color: var(--color-average-secondary);
  --toplevel-warning-padding: 18px;
  --toplevel-warning-text-color: var(--report-text-color);

  /* SVGs */
  --plugin-icon-url-dark: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" viewBox="0 0 24 24" fill="%23FFFFFF"><path d="M0 0h24v24H0z" fill="none"/><path d="M20.5 11H19V7c0-1.1-.9-2-2-2h-4V3.5C13 2.12 11.88 1 10.5 1S8 2.12 8 3.5V5H4c-1.1 0-1.99.9-1.99 2v3.8H3.5c1.49 0 2.7 1.21 2.7 2.7s-1.21 2.7-2.7 2.7H2V20c0 1.1.9 2 2 2h3.8v-1.5c0-1.49 1.21-2.7 2.7-2.7 1.49 0 2.7 1.21 2.7 2.7V22H17c1.1 0 2-.9 2-2v-4h1.5c1.38 0 2.5-1.12 2.5-2.5S21.88 11 20.5 11z"/>\u003c/svg>');
  --plugin-icon-url: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" viewBox="0 0 24 24" fill="%23757575"><path d="M0 0h24v24H0z" fill="none"/><path d="M20.5 11H19V7c0-1.1-.9-2-2-2h-4V3.5C13 2.12 11.88 1 10.5 1S8 2.12 8 3.5V5H4c-1.1 0-1.99.9-1.99 2v3.8H3.5c1.49 0 2.7 1.21 2.7 2.7s-1.21 2.7-2.7 2.7H2V20c0 1.1.9 2 2 2h3.8v-1.5c0-1.49 1.21-2.7 2.7-2.7 1.49 0 2.7 1.21 2.7 2.7V22H17c1.1 0 2-.9 2-2v-4h1.5c1.38 0 2.5-1.12 2.5-2.5S21.88 11 20.5 11z"/>\u003c/svg>');

  --pass-icon-url: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 48 48"><title>check\u003c/title><path fill="%23178239" d="M24 4C12.95 4 4 12.95 4 24c0 11.04 8.95 20 20 20 11.04 0 20-8.96 20-20 0-11.05-8.96-20-20-20zm-4 30L10 24l2.83-2.83L20 28.34l15.17-15.17L38 16 20 34z"/>\u003c/svg>');
  --average-icon-url: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 48 48"><title>info\u003c/title><path fill="%23E67700" d="M24 4C12.95 4 4 12.95 4 24s8.95 20 20 20 20-8.95 20-20S35.05 4 24 4zm2 30h-4V22h4v12zm0-16h-4v-4h4v4z"/>\u003c/svg>');
  --fail-icon-url: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 48 48"><title>warn\u003c/title><path fill="%23C7221F" d="M2 42h44L24 4 2 42zm24-6h-4v-4h4v4zm0-8h-4v-8h4v8z"/>\u003c/svg>');
  --error-icon-url: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 3 15"><title>error\u003c/title><path d="M0 15H 3V 12H 0V" fill="%23FF4E42"/><path d="M0 9H 3V 0H 0V" fill="%23FF4E42"/>\u003c/svg>');

  --swap-locale-icon-url: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 0 24 24" width="24px" fill="#000000"><path d="M0 0h24v24H0V0z" fill="none"/><path d="M12.87 15.07l-2.54-2.51.03-.03c1.74-1.94 2.98-4.17 3.71-6.53H17V4h-7V2H8v2H1v1.99h11.17C11.5 7.92 10.44 9.75 9 11.35 8.07 10.32 7.3 9.19 6.69 8h-2c.73 1.63 1.73 3.17 2.98 4.56l-5.09 5.02L4 19l5-5 3.11 3.11.76-2.04zM18.5 10h-2L12 22h2l1.12-3h4.75L21 22h2l-4.5-12zm-2.62 7l1.62-4.33L19.12 17h-3.24z"/>\u003c/svg>');
}

@media not print {
  .lh-dark {
    /* Pallete */
    --color-gray-200: var(--color-gray-800);
    --color-gray-300: #616161;
    --color-gray-400: var(--color-gray-600);
    --color-gray-700: var(--color-gray-400);
    --color-gray-50: #757575;
    --color-gray-600: var(--color-gray-500);
    --color-green-700: var(--color-green);
    --color-orange-700: var(--color-orange);
    --color-red-700: var(--color-red);
    --color-teal-600: var(--color-cyan-500);

    /* Context-specific colors */
    --color-hover: rgba(0, 0, 0, 0.2);
    --color-informative: var(--color-blue-200);

    /* Component variables */
    --env-item-background-color: #393535;
    --link-color: var(--color-blue-200);
    --locale-selector-background-color: var(--color-gray-200);
    --plugin-badge-background-color: var(--color-gray-800);
    --report-background-color: var(--color-gray-900);
    --report-border-color-secondary: var(--color-gray-200);
    --report-text-color-secondary: var(--color-gray-400);
    --report-text-color: var(--color-gray-100);
    --snippet-color: var(--color-cyan-500);
    --topbar-background-color: var(--color-gray);
    --toplevel-warning-background-color: hsl(33deg 14% 18%);
    --toplevel-warning-message-text-color: var(--color-orange-700);
    --toplevel-warning-text-color: var(--color-gray-100);
    --table-group-header-background-color: rgba(186, 196, 206, 0.15);
    --table-group-header-text-color: var(--color-gray-100);
    --table-higlight-background-color: rgba(186, 196, 206, 0.09);

    /* SVGs */
    --plugin-icon-url: var(--plugin-icon-url-dark);
  }
}

/**
* This media query is a temporary fallback for browsers that do not support \`@container query\`.
* TODO: remove this media query when \`@container query\` is fully supported by browsers
* See https://github.com/GoogleChrome/lighthouse/pull/16332
*/
@media only screen and (max-width: 480px) {
  .lh-vars {
    --audit-group-margin-bottom: 20px;
    --edge-gap-padding: var(--default-padding);
    --env-name-min-width: 120px;
    --gauge-circle-size-big: 96px;
    --gauge-circle-size: 72px;
    --gauge-label-font-size-big: 22px;
    --gauge-label-font-size: 14px;
    --gauge-label-line-height-big: 26px;
    --gauge-label-line-height: 20px;
    --gauge-percentage-font-size-big: 34px;
    --gauge-percentage-font-size: 26px;
    --gauge-wrapper-width: 112px;
    --header-padding: 16px 0 16px 0;
    --image-preview-size: 24px;
    --plugin-icon-size: 75%;
    --report-font-size: 14px;
    --report-line-height: 20px;
    --score-icon-margin-left: 2px;
    --score-icon-size: 10px;
    --topbar-height: 28px;
    --topbar-logo-size: 20px;
  }
}

@container lh-container (max-width: 480px) {
  .lh-vars {
    --audit-group-margin-bottom: 20px;
    --edge-gap-padding: var(--default-padding);
    --env-name-min-width: 120px;
    --gauge-circle-size-big: 96px;
    --gauge-circle-size: 72px;
    --gauge-label-font-size-big: 22px;
    --gauge-label-font-size: 14px;
    --gauge-label-line-height-big: 26px;
    --gauge-label-line-height: 20px;
    --gauge-percentage-font-size-big: 34px;
    --gauge-percentage-font-size: 26px;
    --gauge-wrapper-width: 112px;
    --header-padding: 16px 0 16px 0;
    --image-preview-size: 24px;
    --plugin-icon-size: 75%;
    --report-font-size: 14px;
    --report-line-height: 20px;
    --score-icon-margin-left: 2px;
    --score-icon-size: 10px;
    --topbar-height: 28px;
    --topbar-logo-size: 20px;
  }
}

.lh-vars.lh-devtools {
  --audit-explanation-line-height: 14px;
  --audit-group-margin-bottom: 20px;
  --audit-group-padding-vertical: 12px;
  --audit-padding-vertical: 4px;
  --category-padding: 12px;
  --default-padding: 12px;
  --env-name-min-width: 120px;
  --footer-padding-vertical: 8px;
  --gauge-circle-size-big: 72px;
  --gauge-circle-size: 64px;
  --gauge-label-font-size-big: 22px;
  --gauge-label-font-size: 14px;
  --gauge-label-line-height-big: 26px;
  --gauge-label-line-height: 20px;
  --gauge-percentage-font-size-big: 34px;
  --gauge-percentage-font-size: 26px;
  --gauge-wrapper-width: 97px;
  --header-line-height: 20px;
  --header-padding: 16px 0 16px 0;
  --screenshot-overlay-background: transparent;
  --plugin-icon-size: 75%;
  --report-font-size: 12px;
  --report-line-height: 20px;
  --score-icon-margin-left: 2px;
  --score-icon-size: 10px;
  --section-padding-vertical: 8px;
}

.lh-container:has(.lh-sticky-header) {
  --sticky-header-buffer: calc(var(--topbar-height) + var(--sticky-header-height));
}

.lh-container:not(.lh-topbar + .lh-container) {
  --topbar-height: 0;
  --sticky-header-height: 0;
  --sticky-header-buffer: 0;
}

.lh-max-viewport {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  width: 100%;
}

.lh-devtools.lh-root {
  height: 100%;
}
.lh-devtools.lh-root img {
  /* Override devtools default 'min-width: 0' so svg without size in a flexbox isn't collapsed. */
  min-width: auto;
}
.lh-devtools .lh-container {
  overflow-y: scroll;
  height: calc(100% - var(--topbar-height));
  /** The .lh-container is the scroll parent in DevTools so we exclude the topbar from the sticky header buffer. */
  --sticky-header-buffer: 0;
}
.lh-devtools .lh-container:has(.lh-sticky-header) {
  /** The .lh-container is the scroll parent in DevTools so we exclude the topbar from the sticky header buffer. */
  --sticky-header-buffer: var(--sticky-header-height);
}
@media print {
  .lh-devtools .lh-container {
    overflow: unset;
  }
}
.lh-devtools .lh-sticky-header {
  /* This is normally the height of the topbar, but we want it to stick to the top of our scroll container .lh-container\` */
  top: 0;
}
.lh-devtools .lh-element-screenshot__overlay {
  position: absolute;
}

@keyframes fadeIn {
  0% { opacity: 0;}
  100% { opacity: 0.6;}
}

.lh-root *, .lh-root *::before, .lh-root *::after {
  box-sizing: border-box;
}

.lh-root {
  font-family: var(--report-font-family);
  font-size: var(--report-font-size);
  margin: 0;
  line-height: var(--report-line-height);
  background: var(--report-background-color);
  color: var(--report-text-color);
}

.lh-root :focus-visible {
    outline: -webkit-focus-ring-color auto 3px;
}
.lh-root summary:focus {
    outline: none;
    box-shadow: 0 0 0 1px hsl(217, 89%, 61%);
}

.lh-root [hidden] {
  display: none !important;
}

.lh-root pre {
  margin: 0;
}

.lh-root pre,
.lh-root code {
  font-family: var(--report-font-family-monospace);
}

.lh-root details > summary {
  cursor: pointer;
}

.lh-hidden {
  display: none !important;
}

.lh-container {
  /*
  Text wrapping in the report is so much FUN!
  We have a \`word-break: break-word;\` globally here to prevent a few common scenarios, namely
  long non-breakable text (usually URLs) found in:
    1. The footer
    2. .lh-node (outerHTML)
    3. .lh-code

  With that sorted, the next challenge is appropriate column sizing and text wrapping inside our
  .lh-details tables. Even more fun.
    * We don't want table headers ("Est Savings (ms)") to wrap or their column values, but
      we'd be happy for the URL column to wrap if the URLs are particularly long.
    * We want the narrow columns to remain narrow, providing the most column width for URL
    * We don't want the table to extend past 100% width.
    * Long URLs in the URL column can wrap. Util.getURLDisplayName maxes them out at 64 characters,
      but they do not get any overflow:ellipsis treatment.
  */
  word-break: break-word;

  container-name: lh-container;
  container-type: inline-size;
}

.lh-audit-group a,
.lh-category-header__description a,
.lh-audit__description a,
.lh-warnings a,
.lh-footer a,
.lh-table-column--link a {
  color: var(--link-color);
}

.lh-audit__description, .lh-audit__stackpack {
  --inner-audit-padding-right: var(--stackpack-padding-horizontal);
  padding-left: var(--audit-description-padding-left);
  padding-right: var(--inner-audit-padding-right);
  padding-top: 8px;
  padding-bottom: 8px;
}

.lh-details {
  margin-top: var(--default-padding);
  margin-bottom: var(--default-padding);
  margin-left: var(--audit-description-padding-left);
}

.lh-audit__stackpack {
  display: flex;
  align-items: center;
}

.lh-audit__stackpack__img {
  max-width: 30px;
  margin-right: var(--default-padding)
}

/* Report header */

.lh-report-icon {
  display: flex;
  align-items: center;
  padding: 10px 12px;
  cursor: pointer;
}
.lh-report-icon[disabled] {
  opacity: 0.3;
  pointer-events: none;
}

.lh-report-icon::before {
  content: "";
  margin: 4px;
  background-repeat: no-repeat;
  width: var(--report-icon-size);
  height: var(--report-icon-size);
  opacity: 0.7;
  display: inline-block;
  vertical-align: middle;
}
.lh-report-icon:hover::before {
  opacity: 1;
}
.lh-dark .lh-report-icon::before {
  filter: invert(1);
}
.lh-report-icon--print::before {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"><path d="M19 8H5c-1.66 0-3 1.34-3 3v6h4v4h12v-4h4v-6c0-1.66-1.34-3-3-3zm-3 11H8v-5h8v5zm3-7c-.55 0-1-.45-1-1s.45-1 1-1 1 .45 1 1-.45 1-1 1zm-1-9H6v4h12V3z"/><path fill="none" d="M0 0h24v24H0z"/>\u003c/svg>');
}
.lh-report-icon--copy::before {
  background-image: url('data:image/svg+xml;utf8,<svg height="24" viewBox="0 0 24 24" width="24" xmlns="http://www.w3.org/2000/svg"><path d="M0 0h24v24H0z" fill="none"/><path d="M16 1H4c-1.1 0-2 .9-2 2v14h2V3h12V1zm3 4H8c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h11c1.1 0 2-.9 2-2V7c0-1.1-.9-2-2-2zm0 16H8V7h11v14z"/>\u003c/svg>');
}
.lh-report-icon--open::before {
  background-image: url('data:image/svg+xml;utf8,<svg height="24" viewBox="0 0 24 24" width="24" xmlns="http://www.w3.org/2000/svg"><path d="M0 0h24v24H0z" fill="none"/><path d="M19 4H5c-1.11 0-2 .9-2 2v12c0 1.1.89 2 2 2h4v-2H5V8h14v10h-4v2h4c1.1 0 2-.9 2-2V6c0-1.1-.89-2-2-2zm-7 6l-4 4h3v6h2v-6h3l-4-4z"/>\u003c/svg>');
}
.lh-report-icon--download::before {
  background-image: url('data:image/svg+xml;utf8,<svg height="24" viewBox="0 0 24 24" width="24" xmlns="http://www.w3.org/2000/svg"><path d="M19 9h-4V3H9v6H5l7 7 7-7zM5 18v2h14v-2H5z"/><path d="M0 0h24v24H0z" fill="none"/>\u003c/svg>');
}
.lh-report-icon--dark::before {
  background-image:url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" height="24" viewBox="0 0 100 125"><path d="M50 23.587c-16.27 0-22.799 12.574-22.799 21.417 0 12.917 10.117 22.451 12.436 32.471h20.726c2.32-10.02 12.436-19.554 12.436-32.471 0-8.843-6.528-21.417-22.799-21.417zM39.637 87.161c0 3.001 1.18 4.181 4.181 4.181h.426l.41 1.231C45.278 94.449 46.042 95 48.019 95h3.963c1.978 0 2.74-.551 3.365-2.427l.409-1.231h.427c3.002 0 4.18-1.18 4.18-4.181V80.91H39.637v6.251zM50 18.265c1.26 0 2.072-.814 2.072-2.073v-9.12C52.072 5.813 51.26 5 50 5c-1.259 0-2.072.813-2.072 2.073v9.12c0 1.259.813 2.072 2.072 2.072zM68.313 23.727c.994.774 2.135.634 2.91-.357l5.614-7.187c.776-.992.636-2.135-.356-2.909-.992-.776-2.135-.636-2.91.357l-5.613 7.186c-.778.993-.636 2.135.355 2.91zM91.157 36.373c-.306-1.222-1.291-1.815-2.513-1.51l-8.85 2.207c-1.222.305-1.814 1.29-1.51 2.512.305 1.223 1.291 1.814 2.513 1.51l8.849-2.206c1.223-.305 1.816-1.291 1.511-2.513zM86.757 60.48l-8.331-3.709c-1.15-.512-2.225-.099-2.736 1.052-.512 1.151-.1 2.224 1.051 2.737l8.33 3.707c1.15.514 2.225.101 2.736-1.05.513-1.149.1-2.223-1.05-2.737zM28.779 23.37c.775.992 1.917 1.131 2.909.357.992-.776 1.132-1.917.357-2.91l-5.615-7.186c-.775-.992-1.917-1.132-2.909-.357s-1.131 1.917-.356 2.909l5.614 7.187zM21.715 39.583c.305-1.223-.288-2.208-1.51-2.513l-8.849-2.207c-1.222-.303-2.208.289-2.513 1.511-.303 1.222.288 2.207 1.511 2.512l8.848 2.206c1.222.304 2.208-.287 2.513-1.509zM21.575 56.771l-8.331 3.711c-1.151.511-1.563 1.586-1.05 2.735.511 1.151 1.586 1.563 2.736 1.052l8.331-3.711c1.151-.511 1.563-1.586 1.05-2.735-.512-1.15-1.585-1.562-2.736-1.052z"/>\u003c/svg>');
}
.lh-report-icon--treemap::before {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 0 24 24" width="24px" fill="black"><path d="M3 5v14h19V5H3zm2 2h15v4H5V7zm0 10v-4h4v4H5zm6 0v-4h9v4h-9z"/>\u003c/svg>');
}

.lh-report-icon--date::before {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M7 11h2v2H7v-2zm14-5v14a2 2 0 01-2 2H5a2 2 0 01-2-2V6c0-1.1.9-2 2-2h1V2h2v2h8V2h2v2h1a2 2 0 012 2zM5 8h14V6H5v2zm14 12V10H5v10h14zm-4-7h2v-2h-2v2zm-4 0h2v-2h-2v2z"/>\u003c/svg>');
}
.lh-report-icon--devices::before {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M4 6h18V4H4a2 2 0 00-2 2v11H0v3h14v-3H4V6zm19 2h-6a1 1 0 00-1 1v10c0 .6.5 1 1 1h6c.6 0 1-.5 1-1V9c0-.6-.5-1-1-1zm-1 9h-4v-7h4v7z"/>\u003c/svg>');
}
.lh-report-icon--world::before {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 2a10 10 0 1 0 0 20 10 10 0 0 0 0-20zm7 6h-3c-.3-1.3-.8-2.5-1.4-3.6A8 8 0 0 1 18.9 8zm-7-4a14 14 0 0 1 2 4h-4a14 14 0 0 1 2-4zM4.3 14a8.2 8.2 0 0 1 0-4h3.3a16.5 16.5 0 0 0 0 4H4.3zm.8 2h3a14 14 0 0 0 1.3 3.6A8 8 0 0 1 5.1 16zm3-8H5a8 8 0 0 1 4.3-3.6L8 8zM12 20a14 14 0 0 1-2-4h4a14 14 0 0 1-2 4zm2.3-6H9.7a14.7 14.7 0 0 1 0-4h4.6a14.6 14.6 0 0 1 0 4zm.3 5.6c.6-1.2 1-2.4 1.4-3.6h3a8 8 0 0 1-4.4 3.6zm1.8-5.6a16.5 16.5 0 0 0 0-4h3.3a8.2 8.2 0 0 1 0 4h-3.3z"/>\u003c/svg>');
}
.lh-report-icon--stopwatch::before {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M15 1H9v2h6V1zm-4 13h2V8h-2v6zm8.1-6.6L20.5 6l-1.4-1.4L17.7 6A9 9 0 0 0 3 13a9 9 0 1 0 16-5.6zm-7 12.6a7 7 0 1 1 0-14 7 7 0 0 1 0 14z"/>\u003c/svg>');
}
.lh-report-icon--networkspeed::before {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M15.9 5c-.2 0-.3 0-.4.2v.2L10.1 17a2 2 0 0 0-.2 1 2 2 0 0 0 4 .4l2.4-12.9c0-.3-.2-.5-.5-.5zM1 9l2 2c2.9-2.9 6.8-4 10.5-3.6l1.2-2.7C10 3.8 4.7 5.3 1 9zm20 2 2-2a15.4 15.4 0 0 0-5.6-3.6L17 8.2c1.5.7 2.9 1.6 4.1 2.8zm-4 4 2-2a9.9 9.9 0 0 0-2.7-1.9l-.5 3 1.2.9zM5 13l2 2a7.1 7.1 0 0 1 4-2l1.3-2.9C9.7 10.1 7 11 5 13z"/>\u003c/svg>');
}
.lh-report-icon--samples-one::before {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><circle cx="7" cy="14" r="3"/><path d="M7 18a4 4 0 1 1 0-8 4 4 0 0 1 0 8zm0-6c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm4-2a4 4 0 1 1 0-8 4 4 0 0 1 0 8zm0-6c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm5.6 17.6a4 4 0 1 1 0-8 4 4 0 0 1 0 8zm0-6c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2z"/>\u003c/svg>');
}
.lh-report-icon--samples-many::before {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M7 18a4 4 0 1 1 0-8 4 4 0 0 1 0 8zm0-6c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm4-2a4 4 0 1 1 0-8 4 4 0 0 1 0 8zm0-6c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm5.6 17.6a4 4 0 1 1 0-8 4 4 0 0 1 0 8zm0-6c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2z"/><circle cx="7" cy="14" r="3"/><circle cx="11" cy="6" r="3"/>\u003c/svg>');
}
.lh-report-icon--chrome::before {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="-50 -50 562 562"><path d="M256 25.6v25.6a204 204 0 0 1 144.8 60 204 204 0 0 1 60 144.8 204 204 0 0 1-60 144.8 204 204 0 0 1-144.8 60 204 204 0 0 1-144.8-60 204 204 0 0 1-60-144.8 204 204 0 0 1 60-144.8 204 204 0 0 1 144.8-60V0a256 256 0 1 0 0 512 256 256 0 0 0 0-512v25.6z"/><path d="M256 179.2v25.6a51.3 51.3 0 0 1 0 102.4 51.3 51.3 0 0 1 0-102.4v-51.2a102.3 102.3 0 1 0-.1 204.7 102.3 102.3 0 0 0 .1-204.7v25.6z"/><path d="M256 204.8h217.6a25.6 25.6 0 0 0 0-51.2H256a25.6 25.6 0 0 0 0 51.2m44.3 76.8L191.5 470.1a25.6 25.6 0 1 0 44.4 25.6l108.8-188.5a25.6 25.6 0 1 0-44.4-25.6m-88.6 0L102.9 93.2a25.7 25.7 0 0 0-35-9.4 25.7 25.7 0 0 0-9.4 35l108.8 188.5a25.7 25.7 0 0 0 35 9.4 25.9 25.9 0 0 0 9.4-35.1"/>\u003c/svg>');
}
.lh-report-icon--external::before {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg"><path d="M3.15 11.9a1.01 1.01 0 0 1-.743-.307 1.01 1.01 0 0 1-.306-.743v-7.7c0-.292.102-.54.306-.744a1.01 1.01 0 0 1 .744-.306H7v1.05H3.15v7.7h7.7V7h1.05v3.85c0 .291-.103.54-.307.743a1.01 1.01 0 0 1-.743.307h-7.7Zm2.494-2.8-.743-.744 5.206-5.206H8.401V2.1h3.5v3.5h-1.05V3.893L5.644 9.1Z"/>\u003c/svg>');
}
.lh-report-icon--experiment::before {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="none"><path d="M4.50002 17C3.86136 17 3.40302 16.7187 3.12502 16.156C2.84702 15.5933 2.90936 15.069 3.31202 14.583L7.50002 9.5V4.5H6.75002C6.54202 4.5 6.36502 4.427 6.21902 4.281C6.07302 4.135 6.00002 3.958 6.00002 3.75C6.00002 3.542 6.07302 3.365 6.21902 3.219C6.36502 3.073 6.54202 3 6.75002 3H13.25C13.458 3 13.635 3.073 13.781 3.219C13.927 3.365 14 3.542 14 3.75C14 3.958 13.927 4.135 13.781 4.281C13.635 4.427 13.458 4.5 13.25 4.5H12.5V9.5L16.688 14.583C17.0767 15.069 17.132 15.5933 16.854 16.156C16.5767 16.7187 16.1254 17 15.5 17H4.50002ZM4.50002 15.5H15.5L11 10V4.5H9.00002V10L4.50002 15.5Z" fill="black"/>\u003c/svg>');
}

/** These are still icons, but w/o the auto-color invert / opacity / etc. that come with .lh-report-icon */

.lh-report-plain-icon {
  display: flex;
  align-items: center;
}
.lh-report-plain-icon::before {
  content: "";
  background-repeat: no-repeat;
  width: var(--report-icon-size);
  height: var(--report-icon-size);
  display: inline-block;
  margin-right: 5px;
}

.lh-report-plain-icon--checklist-pass::before {
  --icon-url: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"><path d="M8.938 13L13.896 8.062L12.833 7L8.938 10.875L7.167 9.125L6.104 10.188L8.938 13ZM10 18C8.90267 18 7.868 17.7917 6.896 17.375C5.924 16.9583 5.07333 16.3853 4.344 15.656C3.61467 14.9267 3.04167 14.076 2.625 13.104C2.20833 12.132 2 11.0973 2 10C2 8.88867 2.20833 7.85033 2.625 6.885C3.04167 5.92033 3.61467 5.07333 4.344 4.344C5.07333 3.61467 5.924 3.04167 6.896 2.625C7.868 2.20833 8.90267 2 10 2C11.1113 2 12.1497 2.20833 13.115 2.625C14.0797 3.04167 14.9267 3.61467 15.656 4.344C16.3853 5.07333 16.9583 5.92033 17.375 6.885C17.7917 7.85033 18 8.88867 18 10C18 11.0973 17.7917 12.132 17.375 13.104C16.9583 14.076 16.3853 14.9267 15.656 15.656C14.9267 16.3853 14.0797 16.9583 13.115 17.375C12.1497 17.7917 11.1113 18 10 18ZM10 16.5C11.8053 16.5 13.34 15.868 14.604 14.604C15.868 13.34 16.5 11.8053 16.5 10C16.5 8.19467 15.868 6.66 14.604 5.396C13.34 4.132 11.8053 3.5 10 3.5C8.19467 3.5 6.66 4.132 5.396 5.396C4.132 6.66 3.5 8.19467 3.5 10C3.5 11.8053 4.132 13.34 5.396 14.604C6.66 15.868 8.19467 16.5 10 16.5Z" fill="black"/>\u003c/svg>');
  background-color: var(--color-pass);
  mask: var(--icon-url) center / contain no-repeat;
}
.lh-report-plain-icon--checklist-fail::before {
  --icon-url: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"><path fill-rule="evenodd" clip-rule="evenodd" d="M17.5 10C17.5 14.1421 14.1421 17.5 10 17.5C5.85786 17.5 2.5 14.1421 2.5 10C2.5 5.85786 5.85786 2.5 10 2.5C14.1421 2.5 17.5 5.85786 17.5 10ZM16 10C16 13.3137 13.3137 16 10 16C8.6135 16 7.33683 15.5297 6.32083 14.7399L14.7399 6.32083C15.5297 7.33683 16 8.6135 16 10ZM5.26016 13.6793L13.6793 5.26016C12.6633 4.47033 11.3866 4 10 4C6.68629 4 4 6.68629 4 10C4 11.3866 4.47033 12.6633 5.26016 13.6793Z" fill="black"/>\u003c/svg>');
  background-color: var(--color-fail);
  mask: var(--icon-url) center / contain no-repeat;
}

.lh-buttons {
  display: flex;
  flex-wrap: wrap;
  margin: var(--default-padding) 0;
}
.lh-button {
  height: 32px;
  border: 1px solid var(--report-border-color-secondary);
  border-radius: 3px;
  color: var(--link-color);
  background-color: var(--report-background-color);
  margin: 5px;
}

.lh-button:first-of-type {
  margin-left: 0;
}

/* Node */
.lh-node {
  display: flow-root;
}

.lh-node__snippet {
  font-family: var(--report-font-family-monospace);
  color: var(--snippet-color);
  font-size: var(--report-monospace-font-size);
  line-height: 20px;
}

.lh-checklist {
  list-style: none;
  padding: 0;
}

.lh-checklist-item {
  margin: 10px 0 10px 0;
}

/* Score */

.lh-audit__score-icon {
  width: var(--score-icon-size);
  height: var(--score-icon-size);
  margin: var(--score-icon-margin);
}

.lh-audit--pass .lh-audit__display-text {
  color: var(--color-pass-secondary);
}
.lh-audit--pass .lh-audit__score-icon,
.lh-scorescale-range--pass::before {
  border-radius: 100%;
  background: var(--color-pass);
}

.lh-audit--average .lh-audit__display-text {
  color: var(--color-average-secondary);
}
.lh-audit--average .lh-audit__score-icon,
.lh-scorescale-range--average::before {
  background: var(--color-average);
  width: var(--icon-square-size);
  height: var(--icon-square-size);
}

.lh-audit--fail .lh-audit__display-text {
  color: var(--color-fail-secondary);
}
.lh-audit--fail .lh-audit__score-icon,
.lh-audit--error .lh-audit__score-icon,
.lh-scorescale-range--fail::before {
  border-left: calc(var(--score-icon-size) / 2) solid transparent;
  border-right: calc(var(--score-icon-size) / 2) solid transparent;
  border-bottom: var(--score-icon-size) solid var(--color-fail);
}

.lh-audit--error .lh-audit__score-icon,
.lh-metric--error .lh-metric__icon {
  background-image: var(--error-icon-url);
  background-repeat: no-repeat;
  background-position: center;
  border: none;
}

.lh-gauge__wrapper--fail .lh-gauge--error {
  background-image: var(--error-icon-url);
  background-repeat: no-repeat;
  background-position: center;
  transform: scale(0.5);
  top: var(--score-container-padding);
}

.lh-audit--manual .lh-audit__display-text,
.lh-audit--notapplicable .lh-audit__display-text {
  color: var(--color-gray-600);
}
.lh-audit--manual .lh-audit__score-icon,
.lh-audit--notapplicable .lh-audit__score-icon {
  border: calc(0.2 * var(--score-icon-size)) solid var(--color-gray-400);
  border-radius: 100%;
  background: none;
}

.lh-audit--informative .lh-audit__display-text {
  color: var(--color-gray-600);
}

.lh-audit--informative .lh-audit__score-icon {
  border: calc(0.2 * var(--score-icon-size)) solid var(--color-gray-400);
  border-radius: 100%;
}

.lh-audit__description,
.lh-audit__stackpack {
  color: var(--report-text-color-secondary);
}
.lh-audit__adorn {
  border: 1px solid var(--color-gray-500);
  border-radius: 3px;
  margin: 0 3px;
  padding: 0 2px;
  line-height: 1.1;
  display: inline-block;
  font-size: 90%;
  color: var(--report-text-color-secondary);
}

.lh-category-header__description  {
  text-align: center;
  color: var(--color-gray-700);
  margin: 0px auto;
  max-width: 400px;
}


.lh-audit__display-text,
.lh-chevron-container {
  margin: 0 var(--audit-margin-horizontal);
}
.lh-chevron-container {
  margin-right: 0;
}

.lh-audit__title-and-text {
  flex: 1;
}

.lh-audit__title-and-text code {
  color: var(--snippet-color);
  font-size: var(--report-monospace-font-size);
}

/* Prepend display text with em dash separator. */
.lh-audit__display-text:not(:empty):before {
  content: '\u2014';
  margin-right: var(--audit-margin-horizontal);
}

/* Expandable Details (Audit Groups, Audits) */
.lh-audit__header {
  display: flex;
  align-items: center;
  padding: var(--default-padding);
}


.lh-metricfilter {
  display: grid;
  justify-content: end;
  align-items: center;
  grid-auto-flow: column;
  gap: 4px;
  color: var(--color-gray-700);
}

.lh-metricfilter__radio {
  /*
   * Instead of hiding, position offscreen so it's still accessible to screen readers
   * https://bugs.chromium.org/p/chromium/issues/detail?id=1439785
   */
  position: fixed;
  left: -9999px;
}
.lh-metricfilter input[type='radio']:focus-visible + label {
  outline: -webkit-focus-ring-color auto 1px;
}

.lh-metricfilter__label {
  display: inline-flex;
  padding: 0 4px;
  height: 16px;
  text-decoration: underline;
  align-items: center;
  cursor: pointer;
  font-size: 90%;
}

.lh-metricfilter__label--active {
  background: var(--color-blue-primary);
  color: var(--color-white);
  border-radius: 3px;
  text-decoration: none;
}
/* Give the 'All' choice a more muted display */
.lh-metricfilter__label--active[for="metric-All"] {
  background-color: var(--color-blue-200) !important;
  color: black !important;
}

.lh-metricfilter__text {
  margin-right: 8px;
}

/* If audits are filtered, hide the itemcount for Passed Audits\u2026 */
.lh-category--filtered .lh-audit-group .lh-audit-group__itemcount {
  display: none;
}


.lh-audit__header:hover {
  background-color: var(--color-hover);
}

/* We want to hide the browser's default arrow marker on summary elements. Admittedly, it's complicated. */
.lh-root details > summary {
  /* Blink 89+ and Firefox will hide the arrow when display is changed from (new) default of \`list-item\` to block.  https://chromestatus.com/feature/6730096436051968*/
  display: block;
}
/* Safari and Blink <=88 require using the -webkit-details-marker selector */
.lh-root details > summary::-webkit-details-marker {
  display: none;
}

/* Perf Metric */

.lh-metrics-container {
  display: grid;
  grid-auto-rows: 1fr;
  grid-template-columns: 1fr 1fr;
  grid-column-gap: var(--report-line-height);
  margin-bottom: var(--default-padding);
}

.lh-metric {
  border-top: 1px solid var(--report-border-color-secondary);
}

.lh-category:not(.lh--hoisted-meta) .lh-metric:nth-last-child(-n+2) {
  border-bottom: 1px solid var(--report-border-color-secondary);
}

.lh-metric__innerwrap {
  display: grid;
  /**
   * Icon -- Metric Name
   *      -- Metric Value
   */
  grid-template-columns: calc(var(--score-icon-size) + var(--score-icon-margin-left) + var(--score-icon-margin-right)) 1fr;
  align-items: center;
  padding: var(--default-padding);
}

.lh-metric__details {
  order: -1;
}

.lh-metric__title {
  flex: 1;
}

.lh-calclink {
  padding-left: calc(1ex / 3);
}

.lh-metric__description {
  display: none;
  grid-column-start: 2;
  grid-column-end: 4;
  color: var(--report-text-color-secondary);
}

.lh-metric__value {
  font-size: var(--metric-value-font-size);
  margin: calc(var(--default-padding) / 2) 0;
  white-space: nowrap; /* No wrapping between metric value and the icon */
  grid-column-start: 2;
}

/**
* This media query is a temporary fallback for browsers that do not support \`@container query\`.
* TODO: remove this media query when \`@container query\` is fully supported by browsers
* See https://github.com/GoogleChrome/lighthouse/pull/16332
*/
@media screen and (max-width: 535px) {
  .lh-metrics-container {
    display: block;
  }

  .lh-metric {
    border-bottom: none !important;
  }
  .lh-category:not(.lh--hoisted-meta) .lh-metric:nth-last-child(1) {
    border-bottom: 1px solid var(--report-border-color-secondary) !important;
  }

  /* Change the grid to 3 columns for narrow viewport. */
  .lh-metric__innerwrap {
  /**
   * Icon -- Metric Name -- Metric Value
   */
    grid-template-columns: calc(var(--score-icon-size) + var(--score-icon-margin-left) + var(--score-icon-margin-right)) 2fr 1fr;
  }
  .lh-metric__value {
    justify-self: end;
    grid-column-start: unset;
  }
}

@container lh-container (max-width: 535px) {
  .lh-metrics-container {
    display: block;
  }

  .lh-metric {
    border-bottom: none !important;
  }
  .lh-category:not(.lh--hoisted-meta) .lh-metric:nth-last-child(1) {
    border-bottom: 1px solid var(--report-border-color-secondary) !important;
  }

  /* Change the grid to 3 columns for narrow viewport. */
  .lh-metric__innerwrap {
  /**
   * Icon -- Metric Name -- Metric Value
   */
    grid-template-columns: calc(var(--score-icon-size) + var(--score-icon-margin-left) + var(--score-icon-margin-right)) 2fr 1fr;
  }
  .lh-metric__value {
    justify-self: end;
    grid-column-start: unset;
  }
}

/* No-JS toggle switch */
/* Keep this selector sync'd w/ \`magicSelector\` in report-ui-features-test.js */
 .lh-metrics-toggle__input:checked ~ .lh-metrics-container .lh-metric__description {
  display: block;
}

/* TODO get rid of the SVGS and clean up these some more */
.lh-metrics-toggle__input {
  opacity: 0;
  position: absolute;
  right: 0;
  top: 0px;
}

.lh-metrics-toggle__input + div > label > .lh-metrics-toggle__labeltext--hide,
.lh-metrics-toggle__input:checked + div > label > .lh-metrics-toggle__labeltext--show {
  display: none;
}
.lh-metrics-toggle__input:checked + div > label > .lh-metrics-toggle__labeltext--hide {
  display: inline;
}
.lh-metrics-toggle__input:focus + div > label {
  outline: -webkit-focus-ring-color auto 3px;
}

.lh-metrics-toggle__label {
  cursor: pointer;
  font-size: var(--report-font-size-secondary);
  line-height: var(--report-line-height-secondary);
  color: var(--color-gray-700);
}

/* Pushes the metric description toggle button to the right. */
.lh-audit-group--metrics .lh-audit-group__header {
  display: flex;
  justify-content: space-between;
}

.lh-metric__icon,
.lh-scorescale-range::before {
  content: '';
  width: var(--score-icon-size);
  height: var(--score-icon-size);
  display: inline-block;
  margin: var(--score-icon-margin);
}

.lh-metric--pass .lh-metric__value {
  color: var(--color-pass-secondary);
}
.lh-metric--pass .lh-metric__icon {
  border-radius: 100%;
  background: var(--color-pass);
}

.lh-metric--average .lh-metric__value {
  color: var(--color-average-secondary);
}
.lh-metric--average .lh-metric__icon {
  background: var(--color-average);
  width: var(--icon-square-size);
  height: var(--icon-square-size);
}

.lh-metric--fail .lh-metric__value {
  color: var(--color-fail-secondary);
}
.lh-metric--fail .lh-metric__icon {
  border-left: calc(var(--score-icon-size) / 2) solid transparent;
  border-right: calc(var(--score-icon-size) / 2) solid transparent;
  border-bottom: var(--score-icon-size) solid var(--color-fail);
}

.lh-metric--error .lh-metric__value,
.lh-metric--error .lh-metric__description {
  color: var(--color-fail-secondary);
}

/* Filmstrip */

.lh-filmstrip-container {
  /* smaller gap between metrics and filmstrip */
  margin: -8px auto 0 auto;
}

.lh-filmstrip {
  display: flex;
  justify-content: space-between;
  justify-items: center;
  margin-bottom: var(--default-padding);
  width: 100%;
}

.lh-filmstrip__frame {
  overflow: hidden;
  line-height: 0;
}

.lh-filmstrip__thumbnail {
  border: 1px solid var(--report-border-color-secondary);
  max-height: 150px;
  max-width: 120px;
}

/* Toggle Insights banner */
.lh-perf-insights-toggle {
  margin: calc(var(--default-padding) * 2) 0 var(--default-padding);
  display: flex;
  gap: var(--default-padding);
  align-items: center;
  background-color: rgba(30, 164, 70, 0.08);

  padding: var(--toplevel-warning-padding);
  border-radius: 8px;
}

.lh-perf-insights-toggle button {
  cursor: pointer;
  margin: 0;
  flex: 1;
}

.lh-perf-toggle-text {
  align-items: center;
  flex: 5;
}
.lh-dark .lh-perf-toggle-text {
  color: rgba(30, 164, 70, 1);
}

.lh-perf-toggle-text a {
  color: var(--link-color);
}

.lh-perf-insights-icon svg {
  margin: 4px;
  background-repeat: no-repeat;
  width: var(--report-icon-size);
  height: var(--report-icon-size);
  opacity: 0.7;
  display: inline-block;
  vertical-align: middle;
}
/* Audit */

.lh-audit {
  border-bottom: 1px solid var(--report-border-color-secondary);
}

/* Apply border-top to just the first audit. */
.lh-audit {
  border-top: 1px solid var(--report-border-color-secondary);
}
.lh-audit ~ .lh-audit {
  border-top: none;
}


.lh-audit--error .lh-audit__display-text {
  color: var(--color-fail-secondary);
}

/* Audit Group */

.lh-audit-group {
  margin-bottom: var(--audit-group-margin-bottom);
  position: relative;
}
.lh-audit-group--metrics {
  margin-bottom: calc(var(--audit-group-margin-bottom) / 2);
}

.lh-audit-group--metrics .lh-audit-group__summary {
  margin-top: 0;
  margin-bottom: 0;
}

.lh-audit-group__summary {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.lh-audit-group__header .lh-chevron {
  margin-top: calc((var(--report-line-height) - 5px) / 2);
}

.lh-audit-group__header {
  letter-spacing: 0.8px;
  padding: var(--default-padding);
  padding-left: 0;
}

.lh-audit-group__header, .lh-audit-group__summary {
  font-size: var(--report-font-size-secondary);
  line-height: var(--report-line-height-secondary);
  color: var(--color-gray-700);
}

.lh-audit-group__title {
  text-transform: uppercase;
  font-weight: 500;
}

.lh-audit-group__itemcount {
  color: var(--color-gray-600);
}

.lh-audit-group__footer {
  color: var(--color-gray-600);
  display: block;
  margin-top: var(--default-padding);
}

.lh-details,
.lh-category-header__description,
.lh-audit-group__footer {
  font-size: var(--report-font-size-secondary);
  line-height: var(--report-line-height-secondary);
}

.lh-audit-explanation {
  margin: var(--audit-padding-vertical) 0 calc(var(--audit-padding-vertical) / 2) var(--audit-margin-horizontal);
  line-height: var(--audit-explanation-line-height);
  display: inline-block;
}

.lh-audit--fail .lh-audit-explanation {
  color: var(--color-fail-secondary);
}

/* Report */
.lh-list {
  margin-right: calc(var(--default-padding) * 2);
}
.lh-list > :not(:last-child) {
  margin-bottom: calc(var(--default-padding) * 2);
  border-bottom: 1px solid #A8C7FA;
}

.lh-header-container {
  display: block;
  margin: 0 auto;
  position: relative;
  word-wrap: break-word;
}

.lh-header-container .lh-scores-wrapper {
  border-bottom: 1px solid var(--color-gray-200);
}


.lh-report {
  min-width: var(--report-content-min-width);
}

.lh-exception {
  font-size: large;
}

.lh-code {
  white-space: normal;
  margin-top: 0;
  font-size: var(--report-monospace-font-size);
}

.lh-warnings {
  --item-margin: calc(var(--report-line-height) / 6);
  color: var(--color-average-secondary);
  margin: var(--audit-padding-vertical) 0;
  padding: var(--default-padding)
    var(--default-padding)
    var(--default-padding)
    calc(var(--audit-description-padding-left));
  background-color: var(--toplevel-warning-background-color);
}
.lh-warnings span {
  font-weight: bold;
}

.lh-warnings--toplevel {
  --item-margin: calc(var(--header-line-height) / 4);
  color: var(--toplevel-warning-text-color);
  margin-left: auto;
  margin-right: auto;
  max-width: var(--report-content-max-width-minus-edge-gap);
  padding: var(--toplevel-warning-padding);
  border-radius: 8px;
}

.lh-warnings__msg {
  color: var(--toplevel-warning-message-text-color);
  margin: 0;
}

.lh-warnings ul {
  margin: 0;
}
.lh-warnings li {
  margin: var(--item-margin) 0;
}
.lh-warnings li:last-of-type {
  margin-bottom: 0;
}

.lh-scores-header {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
}
.lh-scores-header__solo {
  padding: 0;
  border: 0;
}

/* Gauge */

.lh-gauge__wrapper--pass {
  color: var(--color-pass-secondary);
  fill: var(--color-pass);
  stroke: var(--color-pass);
}

.lh-gauge__wrapper--average {
  color: var(--color-average-secondary);
  fill: var(--color-average);
  stroke: var(--color-average);
}

.lh-gauge__wrapper--fail {
  color: var(--color-fail-secondary);
  fill: var(--color-fail);
  stroke: var(--color-fail);
}

.lh-gauge__wrapper--not-applicable {
  color: var(--color-not-applicable);
  fill: var(--color-not-applicable);
  stroke: var(--color-not-applicable);
}

.lh-fraction__wrapper .lh-fraction__content::before {
  content: '';
  height: var(--score-icon-size);
  width: var(--score-icon-size);
  margin: var(--score-icon-margin);
  display: inline-block;
}
.lh-fraction__wrapper--pass .lh-fraction__content {
  color: var(--color-pass-secondary);
}
.lh-fraction__wrapper--pass .lh-fraction__background {
  background-color: var(--color-pass);
}
.lh-fraction__wrapper--pass .lh-fraction__content::before {
  background-color: var(--color-pass);
  border-radius: 50%;
}
.lh-fraction__wrapper--average .lh-fraction__content {
  color: var(--color-average-secondary);
}
.lh-fraction__wrapper--average .lh-fraction__background,
.lh-fraction__wrapper--average .lh-fraction__content::before {
  background-color: var(--color-average);
}
.lh-fraction__wrapper--fail .lh-fraction__content {
  color: var(--color-fail);
}
.lh-fraction__wrapper--fail .lh-fraction__background {
  background-color: var(--color-fail);
}
.lh-fraction__wrapper--fail .lh-fraction__content::before {
  border-left: calc(var(--score-icon-size) / 2) solid transparent;
  border-right: calc(var(--score-icon-size) / 2) solid transparent;
  border-bottom: var(--score-icon-size) solid var(--color-fail);
}
.lh-fraction__wrapper--null .lh-fraction__content {
  color: var(--color-gray-700);
}
.lh-fraction__wrapper--null .lh-fraction__background {
  background-color: var(--color-gray-700);
}
.lh-fraction__wrapper--null .lh-fraction__content::before {
  border-radius: 50%;
  border: calc(0.2 * var(--score-icon-size)) solid var(--color-gray-700);
}

.lh-fraction__background {
  position: absolute;
  height: 100%;
  width: 100%;
  border-radius: calc(var(--gauge-circle-size) / 2);
  opacity: 0.1;
  z-index: -1;
}

.lh-fraction__content-wrapper {
  height: var(--gauge-circle-size);
  display: flex;
  align-items: center;
}

.lh-fraction__content {
  display: flex;
  position: relative;
  align-items: center;
  justify-content: center;
  font-size: calc(0.3 * var(--gauge-circle-size));
  line-height: calc(0.4 * var(--gauge-circle-size));
  width: max-content;
  min-width: calc(1.5 * var(--gauge-circle-size));
  padding: calc(0.1 * var(--gauge-circle-size)) calc(0.2 * var(--gauge-circle-size));
  --score-icon-size: calc(0.21 * var(--gauge-circle-size));
  --score-icon-margin: 0 calc(0.15 * var(--gauge-circle-size)) 0 0;
}

.lh-gauge {
  stroke-linecap: round;
  width: var(--gauge-circle-size);
  height: var(--gauge-circle-size);
}

.lh-category .lh-gauge {
  --gauge-circle-size: var(--gauge-circle-size-big);
}

.lh-gauge-base {
  opacity: 0.1;
}

.lh-gauge-arc {
  fill: none;
  transform-origin: 50% 50%;
  animation: load-gauge var(--transition-length) ease both;
  animation-delay: 250ms;
}

.lh-gauge__svg-wrapper {
  position: relative;
  height: var(--gauge-circle-size);
}
.lh-category .lh-gauge__svg-wrapper,
.lh-category .lh-fraction__wrapper {
  --gauge-circle-size: var(--gauge-circle-size-big);
}

/* The plugin badge overlay */
.lh-gauge__wrapper--plugin .lh-gauge__svg-wrapper::before {
  width: var(--plugin-badge-size);
  height: var(--plugin-badge-size);
  background-color: var(--plugin-badge-background-color);
  background-image: var(--plugin-icon-url);
  background-repeat: no-repeat;
  background-size: var(--plugin-icon-size);
  background-position: 58% 50%;
  content: "";
  position: absolute;
  right: -6px;
  bottom: 0px;
  display: block;
  z-index: 100;
  box-shadow: 0 0 4px rgba(0,0,0,.2);
  border-radius: 25%;
}
.lh-category .lh-gauge__wrapper--plugin .lh-gauge__svg-wrapper::before {
  width: var(--plugin-badge-size-big);
  height: var(--plugin-badge-size-big);
}

@keyframes load-gauge {
  from { stroke-dasharray: 0 352; }
}

.lh-gauge__percentage {
  width: 100%;
  height: var(--gauge-circle-size);
  line-height: var(--gauge-circle-size);
  position: absolute;
  font-family: var(--report-font-family-monospace);
  font-size: calc(var(--gauge-circle-size) * 0.34 + 1.3px);
  text-align: center;
  top: var(--score-container-padding);
}

.lh-category .lh-gauge__percentage {
  --gauge-circle-size: var(--gauge-circle-size-big);
  --gauge-percentage-font-size: var(--gauge-percentage-font-size-big);
}

.lh-gauge__wrapper,
.lh-fraction__wrapper {
  position: relative;
  display: flex;
  align-items: center;
  flex-direction: column;
  text-decoration: none;
  padding: var(--score-container-padding);

  --transition-length: 1s;

  /* Contain the layout style paint & layers during animation*/
  contain: content;
  will-change: opacity; /* Only using for layer promotion */
}

.lh-gauge__label,
.lh-fraction__label {
  font-size: var(--gauge-label-font-size);
  font-weight: 500;
  line-height: var(--gauge-label-line-height);
  margin-top: 10px;
  text-align: center;
  color: var(--report-text-color);
  word-break: keep-all;
}

/* TODO(#8185) use more BEM (.lh-gauge__label--big) instead of relying on descendant selector */
.lh-category .lh-gauge__label,
.lh-category .lh-fraction__label {
  --gauge-label-font-size: var(--gauge-label-font-size-big);
  --gauge-label-line-height: var(--gauge-label-line-height-big);
  margin-top: 14px;
}

.lh-scores-header .lh-gauge__wrapper,
.lh-scores-header .lh-fraction__wrapper,
.lh-sticky-header .lh-gauge__wrapper,
.lh-sticky-header .lh-fraction__wrapper {
  width: var(--gauge-wrapper-width);
}

.lh-scorescale {
  display: inline-flex;

  gap: calc(var(--default-padding) * 4);
  margin: 16px auto 0 auto;
  font-size: var(--report-font-size-secondary);
  color: var(--color-gray-700);

}

.lh-scorescale-range {
  display: flex;
  align-items: center;
  font-family: var(--report-font-family-monospace);
  white-space: nowrap;
}

.lh-category-header__finalscreenshot .lh-scorescale {
  border: 0;
  display: flex;
  justify-content: center;
}

.lh-category-header__finalscreenshot .lh-scorescale-range {
  font-family: unset;
  font-size: 12px;
}

.lh-scorescale-wrap {
  display: contents;
}

/* Hide category score gauages if it's a single category report */
.lh-header--solo-category .lh-scores-wrapper {
  display: none;
}


.lh-categories {
  width: 100%;
}

.lh-category {
  padding: var(--category-padding);
  max-width: var(--report-content-max-width);
  margin: 0 auto;

  scroll-margin-top: calc(var(--sticky-header-buffer) - 1em);
}

.lh-category-wrapper {
  border-bottom: 1px solid var(--color-gray-200);
}
.lh-category-wrapper:last-of-type {
  border-bottom: 0;
}

.lh-category-header {
  margin-bottom: var(--section-padding-vertical);
}

.lh-category-header .lh-score__gauge {
  max-width: 400px;
  width: auto;
  margin: 0px auto;
}

.lh-category-header__finalscreenshot {
  display: grid;
  grid-template: none / 1fr 1px 1fr;
  justify-items: center;
  align-items: center;
  gap: var(--report-line-height);
  min-height: 288px;
  margin-bottom: var(--default-padding);
}

.lh-final-ss-image {
  /* constrain the size of the image to not be too large */
  max-height: calc(var(--gauge-circle-size-big) * 2.8);
  max-width: calc(var(--gauge-circle-size-big) * 3.5);
  border: 1px solid var(--color-gray-200);
  padding: 4px;
  border-radius: 3px;
  display: block;
}

.lh-category-headercol--separator {
  background: var(--color-gray-200);
  width: 1px;
  height: var(--gauge-circle-size-big);
}

/**
* This media query is a temporary fallback for browsers that do not support \`@container query\`.
* TODO: remove this media query when \`@container query\` is fully supported by browsers
* See https://github.com/GoogleChrome/lighthouse/pull/16332
*/
@media screen and (max-width: 780px) {
  .lh-category-header__finalscreenshot {
    grid-template: 1fr 1fr / none
  }
  .lh-category-headercol--separator {
    display: none;
  }
}

@container lh-container (max-width: 780px) {
  .lh-category-header__finalscreenshot {
    grid-template: 1fr 1fr / none
  }
  .lh-category-headercol--separator {
    display: none;
  }
}

/**
* This media query is a temporary fallback for browsers that do not support \`@container query\`.
* TODO: remove this media query when \`@container query\` is fully supported by browsers
* See https://github.com/GoogleChrome/lighthouse/pull/16332
*/
@media screen and (max-width: 964px) {
  .lh-report {
    margin-left: 0;
    width: 100%;
  }
}

/* 964 fits the min-width of the filmstrip */
@container lh-container (max-width: 964px) {
  .lh-report {
    margin-left: 0;
    width: 100%;
  }
}

@media print {
  body {
    -webkit-print-color-adjust: exact; /* print background colors */
  }
  .lh-container {
    display: block;
  }
  .lh-report {
    margin-left: 0;
    padding-top: 0;
  }
  .lh-categories {
    margin-top: 0;
  }
}

.lh-table {
  position: relative;
  border-collapse: separate;
  border-spacing: 0;
  /* Can't assign padding to table, so shorten the width instead. */
  width: calc(100% - var(--audit-description-padding-left) - var(--stackpack-padding-horizontal));
  border: 1px solid var(--report-border-color-secondary);
}

.lh-table thead th {
  position: sticky;
  top: var(--sticky-header-buffer);
  z-index: 1;
  background-color: var(--report-background-color);
  border-bottom: 1px solid var(--report-border-color-secondary);
  font-weight: normal;
  color: var(--color-gray-600);
  /* See text-wrapping comment on .lh-container. */
  word-break: normal;
}

.lh-row--group {
  background-color: var(--table-group-header-background-color);
}

.lh-row--group td {
  font-weight: bold;
  font-size: 1.05em;
  color: var(--table-group-header-text-color);
}

.lh-row--group td:first-child {
  display: block;
  min-width: max-content;
  font-weight: normal;
}

.lh-row--group .lh-text {
  color: inherit;
  text-decoration: none;
  display: inline-block;
}

.lh-row--group a.lh-link:hover {
  text-decoration: underline;
}

.lh-row--group .lh-audit__adorn {
  text-transform: capitalize;
  font-weight: normal;
  padding: 2px 3px 1px 3px;
}

.lh-row--group .lh-audit__adorn1p {
  color: var(--link-color);
  border-color: var(--link-color);
}

.lh-row--group .lh-report-icon--external::before {
  content: "";
  background-repeat: no-repeat;
  width: 14px;
  height: 16px;
  opacity: 0.7;
  display: inline-block;
  vertical-align: middle;
}

.lh-row--group .lh-report-icon--external {
  visibility: hidden;
}

.lh-row--group:hover .lh-report-icon--external {
  visibility: visible;
}

.lh-dark .lh-report-icon--external::before {
  filter: invert(1);
}

/** Manages indentation of two-level and three-level nested adjacent rows */

.lh-row--group ~ [data-entity]:not(.lh-row--group) td:first-child {
  padding-left: 20px;
}

.lh-row--group ~ [data-entity]:not(.lh-row--group) ~ .lh-sub-item-row td:first-child {
  margin-left: 20px;
  padding-left: 10px;
  border-left: 1px solid #A8C7FA;
  display: block;
}

.lh-row--even {
  background-color: var(--table-group-header-background-color);
}
.lh-row--hidden {
  display: none;
}

.lh-table th,
.lh-table td {
  padding: var(--default-padding);
}

.lh-table tr {
  vertical-align: middle;
}

.lh-table tr:hover {
  background-color: var(--table-higlight-background-color);
}

/* Looks unnecessary, but mostly for keeping the <th>s left-aligned */
.lh-table-column--text,
.lh-table-column--source-location,
.lh-table-column--url,
/* .lh-table-column--thumbnail, */
/* .lh-table-column--empty,*/
.lh-table-column--code,
.lh-table-column--node {
  text-align: left;
}

.lh-table-column--code {
  min-width: 100px;
}

.lh-table-column--bytes,
.lh-table-column--timespanMs,
.lh-table-column--ms,
.lh-table-column--numeric {
  text-align: right;
  word-break: normal;
}



.lh-table .lh-table-column--thumbnail {
  width: var(--image-preview-size);
}

.lh-table-column--url {
  min-width: 250px;
}

.lh-table-column--text {
  min-width: 80px;
}

/* Keep columns narrow if they follow the URL column */
/* 12% was determined to be a decent narrow width, but wide enough for column headings */
.lh-table-column--url + th.lh-table-column--bytes,
.lh-table-column--url + .lh-table-column--bytes + th.lh-table-column--bytes,
.lh-table-column--url + .lh-table-column--ms,
.lh-table-column--url + .lh-table-column--ms + th.lh-table-column--bytes,
.lh-table-column--url + .lh-table-column--bytes + th.lh-table-column--timespanMs {
  width: 12%;
}

/** Tweak styling for tables in insight audits. */
.lh-audit[id$="-insight"] .lh-table {
  border: none;
}

.lh-audit[id$="-insight"] .lh-table thead th {
  font-weight: bold;
  color: unset;
}

.lh-audit[id$="-insight"] .lh-table th,
.lh-audit[id$="-insight"] .lh-table td {
  padding: calc(var(--default-padding) / 2);
}

.lh-audit[id$="-insight"] .lh-table .lh-row--even,
.lh-audit[id$="-insight"] .lh-table tr:not(.lh-row--group):hover {
  background-color: unset;
}

.lh-text__url-host {
  display: inline;
}

.lh-text__url-host {
  margin-left: calc(var(--report-font-size) / 2);
  opacity: 0.6;
  font-size: 90%
}

.lh-thumbnail {
  object-fit: cover;
  width: var(--image-preview-size);
  height: var(--image-preview-size);
  display: block;
}

.lh-unknown pre {
  overflow: scroll;
  border: solid 1px var(--color-gray-200);
}

.lh-text__url > a {
  color: inherit;
  text-decoration: none;
}

.lh-text__url > a:hover {
  text-decoration: underline dotted #999;
}

.lh-sub-item-row {
  margin-left: 20px;
  margin-bottom: 0;
  color: var(--color-gray-700);
}

.lh-sub-item-row td {
  padding-top: 4px;
  padding-bottom: 4px;
  padding-left: 20px;
}

.lh-sub-item-row .lh-element-screenshot {
  zoom: 0.6;
}

/* Chevron
   https://codepen.io/paulirish/pen/LmzEmK
 */
.lh-chevron {
  --chevron-angle: 42deg;
  /* Edge doesn't support transform: rotate(calc(...)), so we define it here */
  --chevron-angle-right: -42deg;
  width: var(--chevron-size);
  height: var(--chevron-size);
  margin-top: calc((var(--report-line-height) - 12px) / 2);
}

.lh-chevron__lines {
  transition: transform 0.4s;
  transform: translateY(var(--report-line-height));
}
.lh-chevron__line {
 stroke: var(--chevron-line-stroke);
 stroke-width: var(--chevron-size);
 stroke-linecap: square;
 transform-origin: 50%;
 transform: rotate(var(--chevron-angle));
 transition: transform 300ms, stroke 300ms;
}

.lh-expandable-details .lh-chevron__line-right,
.lh-expandable-details[open] .lh-chevron__line-left {
 transform: rotate(var(--chevron-angle-right));
}

.lh-expandable-details[open] .lh-chevron__line-right {
  transform: rotate(var(--chevron-angle));
}


.lh-expandable-details[open]  .lh-chevron__lines {
 transform: translateY(calc(var(--chevron-size) * -1));
}

.lh-expandable-details[open] {
  animation: 300ms openDetails forwards;
  padding-bottom: var(--default-padding);
}

@keyframes openDetails {
  from {
    outline: 1px solid var(--report-background-color);
  }
  to {
   outline: 1px solid;
   box-shadow: 0 2px 4px rgba(0, 0, 0, .24);
  }
}

/**
* This media query is a temporary fallback for browsers that do not support \`@container query\`.
* TODO: remove this media query when \`@container query\` is fully supported by browsers
* See https://github.com/GoogleChrome/lighthouse/pull/16332
*/
@media screen and (max-width: 780px) {
  /* no black outline if we're not confident the entire table can be displayed within bounds */
  .lh-expandable-details[open] {
    animation: none;
  }
}

@container lh-container (max-width: 780px) {
  /* no black outline if we're not confident the entire table can be displayed within bounds */
  .lh-expandable-details[open] {
    animation: none;
  }
}

.lh-expandable-details[open] summary, details.lh-clump > summary {
  border-bottom: 1px solid var(--report-border-color-secondary);
}
details.lh-clump[open] > summary {
  border-bottom-width: 0;
}



details .lh-clump-toggletext--hide,
details[open] .lh-clump-toggletext--show { display: none; }
details[open] .lh-clump-toggletext--hide { display: block;}


/* Tooltip */
.lh-tooltip-boundary {
  position: relative;
}

.lh-tooltip {
  position: absolute;
  display: none; /* Don't retain these layers when not needed */
  opacity: 0;
  background: #ffffff;
  white-space: pre-line; /* Render newlines in the text */
  min-width: 246px;
  max-width: 275px;
  padding: 15px;
  border-radius: 5px;
  text-align: initial;
  line-height: 1.4;
}

/**
* This media query is a temporary fallback for browsers that do not support \`@container query\`.
* TODO: remove this media query when \`@container query\` is fully supported by browsers
* See https://github.com/GoogleChrome/lighthouse/pull/16332
*/
@media screen and (max-width: 535px) {
  .lh-tooltip {
    min-width: 45vw;
    padding: 3vw;
  }
}

/* shrink tooltips to not be cutoff on left edge of narrow container
   45vw is chosen to be ~= width of the left column of metrics
*/
@container lh-container (max-width: 535px) {
  .lh-tooltip {
    min-width: 45vw;
    padding: 3vw;
  }
}

.lh-tooltip-boundary:hover .lh-tooltip {
  display: block;
  animation: fadeInTooltip 250ms;
  animation-fill-mode: forwards;
  animation-delay: 850ms;
  bottom: 100%;
  z-index: 1;
  will-change: opacity;
  right: 0;
  pointer-events: none;
}

.lh-tooltip::before {
  content: "";
  border: solid transparent;
  border-bottom-color: #fff;
  border-width: 10px;
  position: absolute;
  bottom: -20px;
  right: 6px;
  transform: rotate(180deg);
  pointer-events: none;
}

@keyframes fadeInTooltip {
  0% { opacity: 0; }
  75% { opacity: 1; }
  100% { opacity: 1;  filter: drop-shadow(1px 0px 1px #aaa) drop-shadow(0px 2px 4px hsla(206, 6%, 25%, 0.15)); pointer-events: auto; }
}

/* Element screenshot */
.lh-element-screenshot {
  float: left;
  margin-right: 20px;
}
.lh-element-screenshot__content {
  overflow: hidden;
  min-width: 110px;
  display: flex;
  justify-content: center;
  background-color: var(--report-background-color);
}
.lh-element-screenshot__image {
  position: relative;
  /* Set by ElementScreenshotRenderer.installFullPageScreenshotCssVariable */
  background-image: var(--element-screenshot-url);
  outline: 2px solid #777;
  background-color: white;
  background-repeat: no-repeat;
}
.lh-element-screenshot__mask {
  position: absolute;
  background: #555;
  opacity: 0.8;
}
.lh-element-screenshot__element-marker {
  position: absolute;
  outline: 2px solid var(--color-lime-400);
}
.lh-element-screenshot__overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 2000; /* .lh-topbar is 1000 */
  background: var(--screenshot-overlay-background);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: zoom-out;
}

.lh-element-screenshot__overlay .lh-element-screenshot {
  margin-right: 0; /* clearing margin used in thumbnail case */
  outline: 1px solid var(--color-gray-700);
}

.lh-screenshot-overlay--enabled .lh-element-screenshot {
  cursor: zoom-out;
}
.lh-screenshot-overlay--enabled .lh-node .lh-element-screenshot {
  cursor: zoom-in;
}


.lh-meta__items {
  --meta-icon-size: calc(var(--report-icon-size) * 0.667);
  padding: var(--default-padding);
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  background-color: var(--env-item-background-color);
  border-radius: 3px;
  margin: 0 0 var(--default-padding) 0;
  font-size: 12px;
  column-gap: var(--default-padding);
  color: var(--color-gray-700);
}

.lh-meta__item {
  display: block;
  list-style-type: none;
  position: relative;
  padding: 0 0 0 calc(var(--meta-icon-size) + var(--default-padding) * 2);
  cursor: unset; /* disable pointer cursor from report-icon */
}

.lh-meta__item.lh-tooltip-boundary {
  text-decoration: dotted underline var(--color-gray-500);
  cursor: help;
}

.lh-meta__item.lh-report-icon::before {
  position: absolute;
  left: var(--default-padding);
  width: var(--meta-icon-size);
  height: var(--meta-icon-size);
}

.lh-meta__item.lh-report-icon:hover::before {
  opacity: 0.7;
}

.lh-meta__item .lh-tooltip {
  color: var(--color-gray-800);
}

.lh-meta__item .lh-tooltip::before {
  right: auto; /* Set the tooltip arrow to the leftside */
  left: 6px;
}

/**
* This media query is a temporary fallback for browsers that do not support \`@container query\`.
* TODO: remove this media query when \`@container query\` is fully supported by browsers
* See https://github.com/GoogleChrome/lighthouse/pull/16332
*/
@media screen and (max-width: 640px) {
  .lh-meta__items {
    grid-template-columns: 1fr 1fr;
  }
}

/* Change the grid for narrow container */
@container lh-container (max-width: 640px) {
  .lh-meta__items {
    grid-template-columns: 1fr 1fr;
  }
}

/**
* This media query is a temporary fallback for browsers that do not support \`@container query\`.
* TODO: remove this media query when \`@container query\` is fully supported by browsers
* See https://github.com/GoogleChrome/lighthouse/pull/16332
*/
@media screen and (max-width: 535px) {
  .lh-meta__items {
    display: block;
  }
}

@container lh-container (max-width: 535px) {
  .lh-meta__items {
    display: block;
  }
}

/* Explodey gauge */

.lh-exp-gauge-component {
  margin-bottom: 10px;
}

.lh-exp-gauge-component circle {
  stroke: currentcolor;
  r: var(--radius);
}

.lh-exp-gauge-component text {
  font-size: calc(var(--radius) * 0.2);
}

.lh-exp-gauge-component .lh-exp-gauge {
  margin: 0 auto;
  width: 225px;
  stroke-width: var(--stroke-width);
  stroke-linecap: round;

  /* for better rendering perf */
  contain: strict;
  height: 225px;
  will-change: transform;
}
.lh-exp-gauge-component .lh-exp-gauge--faded {
  opacity: 0.1;
}
.lh-exp-gauge-component .lh-exp-gauge__wrapper {
  font-family: var(--report-font-family-monospace);
  text-align: center;
  text-decoration: none;
  transition: .3s;
}
.lh-exp-gauge-component .lh-exp-gauge__wrapper--pass {
  color: var(--color-pass);
}
.lh-exp-gauge-component .lh-exp-gauge__wrapper--average {
  color: var(--color-average);
}
.lh-exp-gauge-component .lh-exp-gauge__wrapper--fail {
  color: var(--color-fail);
}
.lh-exp-gauge-component .state--expanded {
  transition: color .3s;
}
.lh-exp-gauge-component .state--highlight {
  color: var(--color-highlight);
}
.lh-exp-gauge-component .lh-exp-gauge__svg-wrapper {
  display: flex;
  flex-direction: column-reverse;
}

.lh-exp-gauge-component .lh-exp-gauge__label {
  fill: var(--report-text-color);
  font-family: var(--report-font-family);
  font-size: 12px;
}

.lh-exp-gauge-component .lh-exp-gauge__cutout {
  opacity: .999;
  transition: opacity .3s;
}
.lh-exp-gauge-component .state--highlight .lh-exp-gauge__cutout {
  opacity: 0;
}

.lh-exp-gauge-component .lh-exp-gauge__inner {
  color: inherit;
}
.lh-exp-gauge-component .lh-exp-gauge__base {
  fill: currentcolor;
}


.lh-exp-gauge-component .lh-exp-gauge__arc {
  fill: none;
  transition: opacity .3s;
}
.lh-exp-gauge-component .lh-exp-gauge__arc--metric {
  color: var(--metric-color);
  stroke-dashoffset: var(--metric-offset);
  opacity: 0.3;
}
.lh-exp-gauge-component .lh-exp-gauge-hovertarget {
  color: currentcolor;
  opacity: 0.001;
  stroke-linecap: butt;
  stroke-width: 24;
  /* hack. move the hover target out of the center. ideally i tweak the r instead but that rquires considerably more math. */
  transform: scale(1.15);
}
.lh-exp-gauge-component .lh-exp-gauge__arc--metric.lh-exp-gauge--miniarc {
  opacity: 0;
  stroke-dasharray: 0 calc(var(--circle-meas) * var(--radius));
  transition: 0s .005s;
}
.lh-exp-gauge-component .state--expanded .lh-exp-gauge__arc--metric.lh-exp-gauge--miniarc {
  opacity: .999;
  stroke-dasharray: var(--metric-array);
  transition: 0.3s; /*  calc(.005s + var(--i)*.05s); entrace animation */
}
.lh-exp-gauge-component .state--expanded .lh-exp-gauge__inner .lh-exp-gauge__arc {
  opacity: 0;
}


.lh-exp-gauge-component .lh-exp-gauge__percentage {
  text-anchor: middle;
  dominant-baseline: middle;
  opacity: .999;
  font-size: calc(var(--radius) * 0.625);
  transition: opacity .3s ease-in;
}
.lh-exp-gauge-component .state--highlight .lh-exp-gauge__percentage {
  opacity: 0;
}

.lh-exp-gauge-component .lh-exp-gauge__wrapper--fail .lh-exp-gauge__percentage {
  fill: var(--color-fail);
}
.lh-exp-gauge-component .lh-exp-gauge__wrapper--average .lh-exp-gauge__percentage {
  fill: var(--color-average);
}
.lh-exp-gauge-component .lh-exp-gauge__wrapper--pass .lh-exp-gauge__percentage {
  fill: var(--color-pass);
}

.lh-exp-gauge-component .lh-cover {
  fill: none;
  opacity: .001;
  pointer-events: none;
}
.lh-exp-gauge-component .state--expanded .lh-cover {
  pointer-events: auto;
}

.lh-exp-gauge-component .metric {
  transform: scale(var(--scale-initial));
  opacity: 0;
  transition: transform .1s .2s ease-out,  opacity .3s ease-out;
  pointer-events: none;
}
.lh-exp-gauge-component .metric text {
  pointer-events: none;
}
.lh-exp-gauge-component .metric__value {
  fill: currentcolor;
  opacity: 0;
  transition: opacity 0.2s;
}
.lh-exp-gauge-component .state--expanded .metric {
  transform: scale(1);
  opacity: .999;
  transition: transform .3s ease-out,  opacity .3s ease-in,  stroke-width .1s ease-out;
  transition-delay: calc(var(--i)*.05s);
  pointer-events: auto;
}
.lh-exp-gauge-component .state--highlight .metric {
  opacity: .3;
}
.lh-exp-gauge-component .state--highlight .metric--highlight {
  opacity: .999;
  stroke-width: calc(1.5*var(--stroke-width));
}
.lh-exp-gauge-component .state--highlight .metric--highlight .metric__value {
  opacity: 0.999;
}


/*
 the initial first load peek
*/
.lh-exp-gauge-component .lh-exp-gauge__bg {  /* needed for the use zindex stacking w/ transparency */
  fill: var(--report-background-color);
  stroke: var(--report-background-color);
}
.lh-exp-gauge-component .state--peek .metric {
  transition-delay: 0ms;
  animation: peek var(--peek-dur) cubic-bezier(0.46, 0.03, 0.52, 0.96);
  animation-fill-mode: forwards;
}
.lh-exp-gauge-component .state--peek .lh-exp-gauge__inner .lh-exp-gauge__arc {
  opacity: 1;
}
.lh-exp-gauge-component .state--peek .lh-exp-gauge__arc.lh-exp-gauge--faded {
  opacity: 0.3; /* just a tad stronger cuz its fighting with a big solid arg */
}
/* do i need to set expanded and override this? */
.lh-exp-gauge-component .state--peek .lh-exp-gauge__arc--metric.lh-exp-gauge--miniarc {
  transition: opacity 0.3s;
}
.lh-exp-gauge-component .state--peek {
  color: unset;
}
.lh-exp-gauge-component .state--peek .metric__label {
  display: none;
}

.lh-exp-gauge-component .metric__label {
  fill: var(--report-text-color);
}

@keyframes peek {
  /* biggest it should go is 0.92. smallest is 0.8 */
  0% {
    transform: scale(0.8);
    opacity: 0.8;
  }

  50% {
    transform: scale(0.92);
    opacity: 1;
  }

  100% {
    transform: scale(0.8);
    opacity: 0.8;
  }
}

.lh-exp-gauge-component .wrapper {
  width: 620px;
}

/*# sourceURL=report-styles.css */
`),e.append(t),e}function pt(o){let e=o.createFragment(),t=o.createElement("style");t.append(`
    .lh-topbar {
      position: sticky;
      top: 0;
      left: 0;
      right: 0;
      z-index: 1000;
      display: flex;
      align-items: center;
      height: var(--topbar-height);
      padding: var(--topbar-padding);
      font-size: var(--report-font-size-secondary);
      background-color: var(--topbar-background-color);
      border-bottom: 1px solid var(--color-gray-200);
    }

    .lh-topbar__logo {
      width: var(--topbar-logo-size);
      height: var(--topbar-logo-size);
      user-select: none;
      flex: none;
    }

    .lh-topbar__url {
      margin: var(--topbar-padding);
      text-decoration: none;
      color: var(--report-text-color);
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
    }

    .lh-tools {
      display: flex;
      align-items: center;
      margin-left: auto;
      will-change: transform;
      min-width: var(--report-icon-size);
    }
    .lh-tools__button {
      width: var(--report-icon-size);
      min-width: 24px;
      height: var(--report-icon-size);
      cursor: pointer;
      margin-right: 5px;
      /* This is actually a button element, but we want to style it like a transparent div. */
      display: flex;
      background: none;
      color: inherit;
      border: none;
      padding: 0;
      font: inherit;
      outline: inherit;
    }
    .lh-tools__button svg {
      fill: var(--tools-icon-color);
    }
    .lh-dark .lh-tools__button svg {
      filter: invert(1);
    }
    .lh-tools__button.lh-active + .lh-tools__dropdown {
      opacity: 1;
      clip: rect(-1px, 194px, 270px, -3px);
      visibility: visible;
    }
    .lh-tools__dropdown {
      position: absolute;
      background-color: var(--report-background-color);
      border: 1px solid var(--report-border-color);
      border-radius: 3px;
      padding: calc(var(--default-padding) / 2) 0;
      cursor: pointer;
      top: 36px;
      right: 0;
      box-shadow: 1px 1px 3px #ccc;
      min-width: 125px;
      clip: rect(0, 164px, 0, 0);
      visibility: hidden;
      opacity: 0;
      transition: all 200ms cubic-bezier(0,0,0.2,1);
    }
    .lh-tools__dropdown a {
      color: currentColor;
      text-decoration: none;
      white-space: nowrap;
      padding: 0 6px;
      line-height: 2;
    }
    .lh-tools__dropdown a:hover,
    .lh-tools__dropdown a:focus {
      background-color: var(--color-gray-200);
      outline: none;
    }
    /* save-gist option hidden in report. */
    .lh-tools__dropdown a[data-action='save-gist'] {
      display: none;
    }

    .lh-locale-selector {
      width: 100%;
      color: var(--report-text-color);
      background-color: var(--locale-selector-background-color);
      padding: 2px;
    }
    .lh-tools-locale {
      display: flex;
      align-items: center;
      flex-direction: row-reverse;
    }
    .lh-tools-locale__selector-wrapper {
      transition: opacity 0.15s;
      opacity: 0;
      max-width: 200px;
    }
    .lh-button.lh-tool-locale__button {
      height: var(--topbar-height);
      color: var(--tools-icon-color);
      padding: calc(var(--default-padding) / 2);
    }
    .lh-tool-locale__button.lh-active + .lh-tools-locale__selector-wrapper {
      opacity: 1;
      clip: rect(-1px, 194px, 242px, -3px);
      visibility: visible;
      margin: 0 4px;
    }

    /**
    * This media query is a temporary fallback for browsers that do not support \`@container query\`.
    * TODO: remove this media query when \`@container query\` is fully supported by browsers
    * See https://github.com/GoogleChrome/lighthouse/pull/16332
    */
    @media screen and (max-width: 964px) {
      .lh-tools__dropdown {
        right: 0;
        left: initial;
      }
    }

    @container lh-container (max-width: 964px) {
      .lh-tools__dropdown {
        right: 0;
        left: initial;
      }
    }

    @media print {
      .lh-topbar {
        position: static;
        margin-left: 0;
      }

      .lh-tools__dropdown {
        display: none;
      }
    }
  `),e.append(t);let n=o.createElement("div","lh-topbar"),r=o.createElementNS("http://www.w3.org/2000/svg","svg","lh-topbar__logo");r.setAttribute("role","img"),r.setAttribute("title","Lighthouse logo"),r.setAttribute("fill","none"),r.setAttribute("xmlns","http://www.w3.org/2000/svg"),r.setAttribute("viewBox","0 0 48 48");let i=o.createElementNS("http://www.w3.org/2000/svg","path");i.setAttribute("d","m14 7 10-7 10 7v10h5v7h-5l5 24H9l5-24H9v-7h5V7Z"),i.setAttribute("fill","#F63");let a=o.createElementNS("http://www.w3.org/2000/svg","path");a.setAttribute("d","M31.561 24H14l-1.689 8.105L31.561 24ZM18.983 48H9l1.022-4.907L35.723 32.27l1.663 7.98L18.983 48Z"),a.setAttribute("fill","#FFA385");let l=o.createElementNS("http://www.w3.org/2000/svg","path");l.setAttribute("fill","#FF3"),l.setAttribute("d","M20.5 10h7v7h-7z"),r.append(" ",i," ",a," ",l," ");let s=o.createElement("a","lh-topbar__url");s.setAttribute("href",""),s.setAttribute("target","_blank"),s.setAttribute("rel","noopener");let c=o.createElement("div","lh-tools"),d=o.createElement("div","lh-tools-locale lh-hidden"),h=o.createElement("button","lh-button lh-tool-locale__button");h.setAttribute("id","lh-button__swap-locales"),h.setAttribute("title","Show Language Picker"),h.setAttribute("aria-label","Toggle language picker"),h.setAttribute("aria-haspopup","menu"),h.setAttribute("aria-expanded","false"),h.setAttribute("aria-controls","lh-tools-locale__selector-wrapper");let p=o.createElementNS("http://www.w3.org/2000/svg","svg");p.setAttribute("width","20px"),p.setAttribute("height","20px"),p.setAttribute("viewBox","0 0 24 24"),p.setAttribute("fill","currentColor");let g=o.createElementNS("http://www.w3.org/2000/svg","path");g.setAttribute("d","M0 0h24v24H0V0z"),g.setAttribute("fill","none");let v=o.createElementNS("http://www.w3.org/2000/svg","path");v.setAttribute("d","M12.87 15.07l-2.54-2.51.03-.03c1.74-1.94 2.98-4.17 3.71-6.53H17V4h-7V2H8v2H1v1.99h11.17C11.5 7.92 10.44 9.75 9 11.35 8.07 10.32 7.3 9.19 6.69 8h-2c.73 1.63 1.73 3.17 2.98 4.56l-5.09 5.02L4 19l5-5 3.11 3.11.76-2.04zM18.5 10h-2L12 22h2l1.12-3h4.75L21 22h2l-4.5-12zm-2.62 7l1.62-4.33L19.12 17h-3.24z"),p.append(g,v),h.append(" ",p," ");let _=o.createElement("div","lh-tools-locale__selector-wrapper");_.setAttribute("id","lh-tools-locale__selector-wrapper"),_.setAttribute("role","menu"),_.setAttribute("aria-labelledby","lh-button__swap-locales"),_.setAttribute("aria-hidden","true"),_.append(" "," "),d.append(" ",h," ",_," ");let m=o.createElement("button","lh-tools__button");m.setAttribute("id","lh-tools-button"),m.setAttribute("title","Tools menu"),m.setAttribute("aria-label","Toggle report tools menu"),m.setAttribute("aria-haspopup","menu"),m.setAttribute("aria-expanded","false"),m.setAttribute("aria-controls","lh-tools-dropdown");let w=o.createElementNS("http://www.w3.org/2000/svg","svg");w.setAttribute("width","100%"),w.setAttribute("height","100%"),w.setAttribute("viewBox","0 0 24 24");let f=o.createElementNS("http://www.w3.org/2000/svg","path");f.setAttribute("d","M0 0h24v24H0z"),f.setAttribute("fill","none");let b=o.createElementNS("http://www.w3.org/2000/svg","path");b.setAttribute("d","M12 8c1.1 0 2-.9 2-2s-.9-2-2-2-2 .9-2 2 .9 2 2 2zm0 2c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm0 6c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2z"),w.append(" ",f," ",b," "),m.append(" ",w," ");let y=o.createElement("div","lh-tools__dropdown");y.setAttribute("id","lh-tools-dropdown"),y.setAttribute("role","menu"),y.setAttribute("aria-labelledby","lh-tools-button");let S=o.createElement("a","lh-report-icon lh-report-icon--print");S.setAttribute("role","menuitem"),S.setAttribute("tabindex","-1"),S.setAttribute("href","#"),S.setAttribute("data-i18n","dropdownPrintSummary"),S.setAttribute("data-action","print-summary");let C=o.createElement("a","lh-report-icon lh-report-icon--print");C.setAttribute("role","menuitem"),C.setAttribute("tabindex","-1"),C.setAttribute("href","#"),C.setAttribute("data-i18n","dropdownPrintExpanded"),C.setAttribute("data-action","print-expanded");let L=o.createElement("a","lh-report-icon lh-report-icon--copy");L.setAttribute("role","menuitem"),L.setAttribute("tabindex","-1"),L.setAttribute("href","#"),L.setAttribute("data-i18n","dropdownCopyJSON"),L.setAttribute("data-action","copy");let z=o.createElement("a","lh-report-icon lh-report-icon--download lh-hidden");z.setAttribute("role","menuitem"),z.setAttribute("tabindex","-1"),z.setAttribute("href","#"),z.setAttribute("data-i18n","dropdownSaveHTML"),z.setAttribute("data-action","save-html");let F=o.createElement("a","lh-report-icon lh-report-icon--download");F.setAttribute("role","menuitem"),F.setAttribute("tabindex","-1"),F.setAttribute("href","#"),F.setAttribute("data-i18n","dropdownSaveJSON"),F.setAttribute("data-action","save-json");let R=o.createElement("a","lh-report-icon lh-report-icon--open");R.setAttribute("role","menuitem"),R.setAttribute("tabindex","-1"),R.setAttribute("href","#"),R.setAttribute("data-i18n","dropdownViewer"),R.setAttribute("data-action","open-viewer");let N=o.createElement("a","lh-report-icon lh-report-icon--open");N.setAttribute("role","menuitem"),N.setAttribute("tabindex","-1"),N.setAttribute("href","#"),N.setAttribute("data-i18n","dropdownSaveGist"),N.setAttribute("data-action","save-gist");let I=o.createElement("a","lh-report-icon lh-report-icon--open lh-hidden");I.setAttribute("role","menuitem"),I.setAttribute("tabindex","-1"),I.setAttribute("href","#"),I.setAttribute("data-i18n","dropdownViewUnthrottledTrace"),I.setAttribute("data-action","view-unthrottled-trace");let H=o.createElement("a","lh-report-icon lh-report-icon--dark");return H.setAttribute("role","menuitem"),H.setAttribute("tabindex","-1"),H.setAttribute("href","#"),H.setAttribute("data-i18n","dropdownDarkTheme"),H.setAttribute("data-action","toggle-dark"),y.append(" ",S," ",C," ",L," "," ",z," ",F," ",R," ",N," "," ",I," ",H," "),c.append(" ",d," ",m," ",y," "),n.append(" "," ",r," ",s," ",c," "),e.append(n),e}function gt(o){let e=o.createFragment(),t=o.createElement("div","lh-warnings lh-warnings--toplevel"),n=o.createElement("p","lh-warnings__msg"),r=o.createElement("ul");return t.append(" ",n," ",r," "),e.append(t),e}function _e(o,e){switch(e){case"3pFilter":return qe(o);case"audit":return je(o);case"categoryHeader":return We(o);case"chevron":return Ke(o);case"clump":return Ze(o);case"crc":return Je(o);case"crcChain":return Qe(o);case"elementScreenshot":return Ye(o);case"explodeyGauge":return Xe(o);case"footer":return et(o);case"fraction":return tt(o);case"gauge":return nt(o);case"heading":return rt(o);case"metric":return ot(o);case"scorescale":return it(o);case"scoresWrapper":return at(o);case"snippet":return lt(o);case"snippetContent":return st(o);case"snippetHeader":return ct(o);case"snippetLine":return dt(o);case"styles":return ht(o);case"topbar":return pt(o);case"warningsToplevel":return gt(o)}throw new Error("unexpected component: "+e)}var ee=class{constructor(e,t){this._document=e,this._lighthouseChannel="unknown",this._componentCache=new Map,this.rootEl=t,this._swappableSections=new WeakMap}createElement(e,t){let n=this._document.createElement(e);if(t)for(let r of t.split(/\s+/))r&&n.classList.add(r);return n}createElementNS(e,t,n){let r=this._document.createElementNS(e,t);if(n)for(let i of n.split(/\s+/))i&&r.classList.add(i);return r}createSVGElement(e,t){return this._document.createElementNS("http://www.w3.org/2000/svg",e,t)}createFragment(){return this._document.createDocumentFragment()}createTextNode(e){return this._document.createTextNode(e)}createChildOf(e,t,n){let r=this.createElement(t,n);return e.append(r),r}createComponent(e){let t=this._componentCache.get(e);if(t){let r=t.cloneNode(!0);return this.findAll("style",r).forEach(i=>i.remove()),r}return t=_e(this,e),this._componentCache.set(e,t),t.cloneNode(!0)}clearComponentCache(){this._componentCache.clear()}convertMarkdownLinkSnippets(e,t={}){let n=this.createElement("span");for(let r of E.splitMarkdownLink(e)){let i=r.text.includes("`")?this.convertMarkdownCodeSnippets(r.text):r.text;if(!r.isLink){n.append(i);continue}let a=new URL(r.linkHref);(["https://developers.google.com","https://web.dev","https://developer.chrome.com"].includes(a.origin)||t.alwaysAppendUtmSource)&&(a.searchParams.set("utm_source","lighthouse"),a.searchParams.set("utm_medium",this._lighthouseChannel));let s=this.createElement("a");s.rel="noopener",s.target="_blank",s.append(i),this.safelySetHref(s,a.href),n.append(s)}return n}safelySetHref(e,t){if(t=t||"",t.startsWith("#")){e.href=t;return}let n=["https:","http:"],r;try{r=new URL(t)}catch{}r&&n.includes(r.protocol)&&(e.href=r.href)}safelySetBlobHref(e,t){if(t.type!=="text/html"&&t.type!=="application/json")throw new Error("Unsupported blob type");let n=URL.createObjectURL(t);e.href=n}convertMarkdownCodeSnippets(e){let t=this.createElement("span");for(let n of E.splitMarkdownCodeSpans(e))if(n.isCode){let r=this.createElement("code");r.textContent=n.text,t.append(r)}else t.append(this._document.createTextNode(n.text));return t}setLighthouseChannel(e){this._lighthouseChannel=e}document(){return this._document}isDevTools(){return!!this._document.querySelector(".lh-devtools")}find(e,t=this.rootEl??this._document){let n=this.maybeFind(e,t);if(n===null)throw new Error(`query ${e} not found`);return n}maybeFind(e,t=this.rootEl??this._document){return t.querySelector(e)}findAll(e,t){return Array.from(t.querySelectorAll(e))}fireEventOn(e,t=this._document,n){let r=new CustomEvent(e,n?{detail:n}:void 0);t.dispatchEvent(r)}saveFile(e,t){let n=this.createElement("a");n.download=t,this.safelySetBlobHref(n,e),this._document.body.append(n),n.click(),this._document.body.removeChild(n),setTimeout(()=>URL.revokeObjectURL(n.href),500)}registerSwappableSections(e,t){this._swappableSections.set(e,t),this._swappableSections.set(t,e)}swapSectionIfPossible(e){let t=this._swappableSections.get(e);if(!t)return;let n=e.parentNode;if(!n)return;let r=e.querySelectorAll("style");t.append(...r),n.insertBefore(t,e),e.remove()}};var we=0,u=class o{static i18n=null;static strings={};static reportJson=null;static apply(e){o.strings={...ye,...e.providedStrings},o.i18n=e.i18n,o.reportJson=e.reportJson}static getUniqueSuffix(){return we++}static resetUniqueSuffix(){we=0}};var xe="data:image/jpeg;base64,";function ke(o){o.configSettings.locale||(o.configSettings.locale="en"),o.configSettings.formFactor||(o.configSettings.formFactor=o.configSettings.emulatedFormFactor),o.finalDisplayedUrl=E.getFinalDisplayedUrl(o),o.mainDocumentUrl=E.getMainDocumentUrl(o);for(let n of Object.values(o.audits))if((n.scoreDisplayMode==="not_applicable"||n.scoreDisplayMode==="not-applicable")&&(n.scoreDisplayMode="notApplicable"),n.scoreDisplayMode==="informative"&&(n.score=1),n.details){if((n.details.type===void 0||n.details.type==="diagnostic")&&(n.details.type="debugdata"),n.details.type==="filmstrip")for(let r of n.details.items)r.data.startsWith(xe)||(r.data=xe+r.data);if(n.details.type==="table")for(let r of n.details.headings){let{itemType:i,text:a}=r;i!==void 0&&(r.valueType=i,delete r.itemType),a!==void 0&&(r.label=a,delete r.text);let l=r.subItemsHeading?.itemType;r.subItemsHeading&&l!==void 0&&(r.subItemsHeading.valueType=l,delete r.subItemsHeading.itemType)}if(n.id==="third-party-summary"&&(n.details.type==="opportunity"||n.details.type==="table")){let{headings:r,items:i}=n.details;if(r[0].valueType==="link"){r[0].valueType="text";for(let a of i)typeof a.entity=="object"&&a.entity.type==="link"&&(a.entity=a.entity.text);n.details.isEntityGrouped=!0}}}let[e]=o.lighthouseVersion.split(".").map(Number),t=o.categories.performance;if(t){if(e<9){o.categoryGroups||(o.categoryGroups={}),o.categoryGroups.hidden={title:""};for(let n of t.auditRefs)n.group?n.group==="load-opportunities"&&(n.group="diagnostics"):n.group="hidden"}else if(e<12)for(let n of t.auditRefs)n.group||(n.group="diagnostics")}if(e<12&&t){let n=new Map;for(let r of t.auditRefs){let i=r.relevantAudits;if(!(!i||!r.acronym))for(let a of i){let l=n.get(a)||[];l.push(r.acronym),n.set(a,l)}}for(let[r,i]of n){if(!i.length)continue;let a=o.audits[r];if(a&&!a.metricSavings){a.metricSavings={};for(let l of i)a.metricSavings[l]=0}}}if(o.environment||(o.environment={benchmarkIndex:0,networkUserAgent:o.userAgent,hostUserAgent:o.userAgent}),o.configSettings.screenEmulation||(o.configSettings.screenEmulation={width:-1,height:-1,deviceScaleFactor:-1,mobile:/mobile/i.test(o.environment.hostUserAgent),disabled:!1}),o.i18n||(o.i18n={}),o.audits["full-page-screenshot"]){let n=o.audits["full-page-screenshot"].details;n?o.fullPageScreenshot={screenshot:n.screenshot,nodes:n.nodes}:o.fullPageScreenshot=null,delete o.audits["full-page-screenshot"]}}var $=E.RATINGS,k=class o{static prepareReportResult(e){let t=JSON.parse(JSON.stringify(e));ke(t);for(let r of Object.values(t.audits))r.details&&(r.details.type==="opportunity"||r.details.type==="table")&&!r.details.isEntityGrouped&&t.entities&&o.classifyEntities(t.entities,r.details);if(typeof t.categories!="object")throw new Error("No categories provided.");let n=new Map;for(let r of Object.values(t.categories))r.auditRefs.forEach(i=>{i.acronym&&n.set(i.acronym,i)}),r.auditRefs.forEach(i=>{let a=t.audits[i.id];i.result=a;let l=Object.keys(i.result.metricSavings||{});if(l.length){i.relevantMetrics=[];for(let s of l){let c=n.get(s);c&&i.relevantMetrics.push(c)}}if(t.stackPacks){let s=[i.id,...i.result.replacesAudits??[]];t.stackPacks.forEach(c=>{let d=s.find(h=>c.descriptions[h]);d&&c.descriptions[d]&&(i.stackPacks=i.stackPacks||[],i.stackPacks.push({title:c.title,iconDataURL:c.iconDataURL,description:c.descriptions[d]}))})}});return t}static getUrlLocatorFn(e){let t=e.find(r=>r.valueType==="url")?.key;if(t&&typeof t=="string")return r=>{let i=r[t];if(typeof i=="string")return i};let n=e.find(r=>r.valueType==="source-location")?.key;if(n)return r=>{let i=r[n];if(typeof i=="object"&&i.type==="source-location")return i.url}}static classifyEntities(e,t){let{items:n,headings:r}=t;if(!n.length||n.some(a=>a.entity))return;let i=o.getUrlLocatorFn(r);if(i)for(let a of n){let l=i(a);if(!l)continue;let s="";try{s=E.parseURL(l).origin}catch{}if(!s)continue;let c=e.find(d=>d.origins.includes(s));c&&(a.entity=c.name)}}static getTableItemSortComparator(e){return(t,n)=>{for(let r of e){let i=t[r],a=n[r];if((typeof i!=typeof a||!["number","string"].includes(typeof i))&&console.warn(`Warning: Attempting to sort unsupported value type: ${r}.`),typeof i=="number"&&typeof a=="number"&&i!==a)return a-i;if(typeof i=="string"&&typeof a=="string"&&i!==a)return i.localeCompare(a)}return 0}}static getEmulationDescriptions(e){let t,n,r,i=e.throttling,a=u.i18n,l=u.strings;switch(e.throttlingMethod){case"provided":r=n=t=l.throttlingProvided;break;case"devtools":{let{cpuSlowdownMultiplier:p,requestLatencyMs:g}=i;t=`${a.formatNumber(p)}x slowdown (DevTools)`,n=`${a.formatMilliseconds(g)} HTTP RTT, ${a.formatKbps(i.downloadThroughputKbps)} down, ${a.formatKbps(i.uploadThroughputKbps)} up (DevTools)`,r=g===150*3.75&&i.downloadThroughputKbps===1.6*1024*.9&&i.uploadThroughputKbps===750*.9?l.runtimeSlow4g:l.runtimeCustom;break}case"simulate":{let{cpuSlowdownMultiplier:p,rttMs:g,throughputKbps:v}=i;t=`${a.formatNumber(p)}x slowdown (Simulated)`,n=`${a.formatMilliseconds(g)} TCP RTT, ${a.formatKbps(v)} throughput (Simulated)`,r=g===150&&v===1.6*1024?l.runtimeSlow4g:l.runtimeCustom;break}default:r=t=n=l.runtimeUnknown}let s=e.channel==="devtools"?!1:e.screenEmulation.disabled,c=e.channel==="devtools"?e.formFactor==="mobile":e.screenEmulation.mobile,d=l.runtimeMobileEmulation;s?d=l.runtimeNoEmulation:c||(d=l.runtimeDesktopEmulation);let h=s?void 0:`${e.screenEmulation.width}x${e.screenEmulation.height}, DPR ${e.screenEmulation.deviceScaleFactor}`;return{deviceEmulation:d,screenEmulation:h,cpuThrottling:t,networkThrottling:n,summary:r}}static showAsPassed(e){switch(e.scoreDisplayMode){case"manual":case"notApplicable":return!0;case"error":case"informative":return!1;case"numeric":case"binary":default:return Number(e.score)>=$.PASS.minScore}}static calculateRating(e,t){if(t==="manual"||t==="notApplicable")return $.PASS.label;if(t==="error")return $.ERROR.label;if(e===null)return $.FAIL.label;let n=$.FAIL.label;return e>=$.PASS.minScore?n=$.PASS.label:e>=$.AVERAGE.minScore&&(n=$.AVERAGE.label),n}static calculateCategoryFraction(e){let t=0,n=0,r=0,i=0;for(let a of e.auditRefs){let l=o.showAsPassed(a.result);if(!(a.group==="hidden"||a.result.scoreDisplayMode==="manual"||a.result.scoreDisplayMode==="notApplicable")){if(a.result.scoreDisplayMode==="informative"){l||++r;continue}++t,i+=a.weight,l&&n++}}return{numPassed:n,numPassableAudits:t,numInformative:r,totalWeight:i}}static isPluginCategory(e){return e.startsWith("lighthouse-plugin-")}static shouldDisplayAsFraction(e){return e==="timespan"||e==="snapshot"}},ye={varianceDisclaimer:"Values are estimated and may vary. The [performance score is calculated](https://developer.chrome.com/docs/lighthouse/performance/performance-scoring/) directly from these metrics.",calculatorLink:"See calculator.",showRelevantAudits:"Show audits relevant to:",opportunityResourceColumnLabel:"Opportunity",opportunitySavingsColumnLabel:"Estimated Savings",errorMissingAuditInfo:"Report error: no audit information",errorLabel:"Error!",warningHeader:"Warnings: ",warningAuditsGroupTitle:"Passed audits but with warnings",passedAuditsGroupTitle:"Passed audits",notApplicableAuditsGroupTitle:"Not applicable",manualAuditsGroupTitle:"Additional items to manually check",toplevelWarningsMessage:"There were issues affecting this run of Lighthouse:",crcInitialNavigation:"Initial Navigation",crcLongestDurationLabel:"Maximum critical path latency:",snippetExpandButtonLabel:"Expand snippet",snippetCollapseButtonLabel:"Collapse snippet",lsPerformanceCategoryDescription:"[Lighthouse](https://developers.google.com/web/tools/lighthouse/) analysis of the current page on an emulated mobile network. Values are estimated and may vary.",labDataTitle:"Lab Data",thirdPartyResourcesLabel:"Show 3rd-party resources",viewTreemapLabel:"View Treemap",viewTraceLabel:"View Trace",dropdownPrintSummary:"Print Summary",dropdownPrintExpanded:"Print Expanded",dropdownCopyJSON:"Copy JSON",dropdownSaveHTML:"Save as HTML",dropdownSaveJSON:"Save as JSON",dropdownViewer:"Open in Viewer",dropdownSaveGist:"Save as Gist",dropdownDarkTheme:"Toggle Dark Theme",dropdownViewUnthrottledTrace:"View Unthrottled Trace",runtimeSettingsDevice:"Device",runtimeSettingsNetworkThrottling:"Network throttling",runtimeSettingsCPUThrottling:"CPU throttling",runtimeSettingsUANetwork:"User agent (network)",runtimeSettingsBenchmark:"Unthrottled CPU/Memory Power",runtimeSettingsAxeVersion:"Axe version",runtimeSettingsScreenEmulation:"Screen emulation",footerIssue:"File an issue",runtimeNoEmulation:"No emulation",runtimeMobileEmulation:"Emulated Moto G Power",runtimeDesktopEmulation:"Emulated Desktop",runtimeUnknown:"Unknown",runtimeSingleLoad:"Single page session",runtimeAnalysisWindow:"Initial page load",runtimeAnalysisWindowTimespan:"User interactions timespan",runtimeAnalysisWindowSnapshot:"Point-in-time snapshot",runtimeSingleLoadTooltip:"This data is taken from a single page session, as opposed to field data summarizing many sessions.",throttlingProvided:"Provided by environment",show:"Show",hide:"Hide",expandView:"Expand view",collapseView:"Collapse view",runtimeSlow4g:"Slow 4G throttling",runtimeCustom:"Custom throttling",firstPartyChipLabel:"1st party",openInANewTabTooltip:"Open in a new tab",unattributable:"Unattributable",insightsNotice:"Later this year, insights will replace performance audits. [Learn more and provide feedback here](https://github.com/GoogleChrome/lighthouse/discussions/16462).",tryInsights:"Try insights",goBackToAudits:"Go back to audits"};var G=class{constructor(e,t){this.dom=e,this.detailsRenderer=t}get _clumpTitles(){return{warning:u.strings.warningAuditsGroupTitle,manual:u.strings.manualAuditsGroupTitle,passed:u.strings.passedAuditsGroupTitle,notApplicable:u.strings.notApplicableAuditsGroupTitle}}renderAudit(e){let t=u.strings,n=this.dom.createComponent("audit"),r=this.dom.find("div.lh-audit",n);r.id=e.result.id;let i=e.result.scoreDisplayMode;e.result.displayValue&&(this.dom.find(".lh-audit__display-text",r).textContent=e.result.displayValue);let a=this.dom.find(".lh-audit__title",r);a.append(this.dom.convertMarkdownCodeSnippets(e.result.title));let l=this.dom.find(".lh-audit__description",r);l.append(this.dom.convertMarkdownLinkSnippets(e.result.description));for(let p of e.relevantMetrics||[]){let g=this.dom.createChildOf(l,"span","lh-audit__adorn");g.title=`Relevant to ${p.result.title}`,g.textContent=p.acronym||p.id}e.stackPacks&&e.stackPacks.forEach(p=>{let g=this.dom.createElement("img","lh-audit__stackpack__img");g.src=p.iconDataURL,g.alt=p.title;let v=this.dom.convertMarkdownLinkSnippets(p.description,{alwaysAppendUtmSource:!0}),_=this.dom.createElement("div","lh-audit__stackpack");_.append(g,v),this.dom.find(".lh-audit__stackpacks",r).append(_)});let s=this.dom.find("details",r);if(e.result.details){let p=this.detailsRenderer.render(e.result.details);p&&(p.classList.add("lh-details"),s.append(p))}if(this.dom.find(".lh-chevron-container",r).append(this._createChevron()),this._setRatingClass(r,e.result.score,i),e.result.scoreDisplayMode==="error"){r.classList.add("lh-audit--error");let p=this.dom.find(".lh-audit__display-text",r);p.textContent=t.errorLabel,p.classList.add("lh-tooltip-boundary");let g=this.dom.createChildOf(p,"div","lh-tooltip lh-tooltip--error");g.textContent=e.result.errorMessage||t.errorMissingAuditInfo}else if(e.result.explanation){let p=this.dom.createChildOf(a,"div","lh-audit-explanation");p.textContent=e.result.explanation}let c=e.result.warnings;if(!c||c.length===0)return r;let d=this.dom.find("summary",s),h=this.dom.createChildOf(d,"div","lh-warnings");if(this.dom.createChildOf(h,"span").textContent=t.warningHeader,c.length===1)h.append(this.dom.createTextNode(c.join("")));else{let p=this.dom.createChildOf(h,"ul");for(let g of c){let v=this.dom.createChildOf(p,"li");v.textContent=g}}return r}injectFinalScreenshot(e,t,n){let r=t["final-screenshot"];if(!r||r.scoreDisplayMode==="error"||!r.details||r.details.type!=="screenshot")return null;let i=this.dom.createElement("img","lh-final-ss-image"),a=r.details.data;i.src=a,i.alt=r.title;let l=this.dom.find(".lh-category .lh-category-header",e),s=this.dom.createElement("div","lh-category-headercol"),c=this.dom.createElement("div","lh-category-headercol lh-category-headercol--separator"),d=this.dom.createElement("div","lh-category-headercol");s.append(...l.childNodes),s.append(n),d.append(i),l.append(s,c,d),l.classList.add("lh-category-header__finalscreenshot")}_createChevron(){let e=this.dom.createComponent("chevron");return this.dom.find("svg.lh-chevron",e)}_setRatingClass(e,t,n){let r=k.calculateRating(t,n);return e.classList.add(`lh-audit--${n.toLowerCase()}`),n!=="informative"&&e.classList.add(`lh-audit--${r}`),e}renderCategoryHeader(e,t,n){let r=this.dom.createComponent("categoryHeader"),i=this.dom.find(".lh-score__gauge",r),a=this.renderCategoryScore(e,t,n);if(i.append(a),e.description){let l=this.dom.convertMarkdownLinkSnippets(e.description);this.dom.find(".lh-category-header__description",r).append(l)}return r}renderAuditGroup(e){let t=this.dom.createElement("div","lh-audit-group"),n=this.dom.createElement("div","lh-audit-group__header");this.dom.createChildOf(n,"span","lh-audit-group__title").textContent=e.title,t.append(n);let r=null;return e.description&&(r=this.dom.convertMarkdownLinkSnippets(e.description),r.classList.add("lh-audit-group__description","lh-audit-group__footer"),t.append(r)),[t,r]}_renderGroupedAudits(e,t){let n=new Map,r="NotAGroup";n.set(r,[]);for(let a of e){let l=a.group||r,s=n.get(l)||[];s.push(a),n.set(l,s)}let i=[];for(let[a,l]of n){if(a===r){for(let h of l)i.push(this.renderAudit(h));continue}let s=t[a],[c,d]=this.renderAuditGroup(s);for(let h of l)c.insertBefore(this.renderAudit(h),d);c.classList.add(`lh-audit-group--${a}`),i.push(c)}return i}renderUnexpandableClump(e,t){let n=this.dom.createElement("div");return this._renderGroupedAudits(e,t).forEach(i=>n.append(i)),n}renderClump(e,{auditRefsOrEls:t,description:n,openByDefault:r}){let i=this.dom.createComponent("clump"),a=this.dom.find(".lh-clump",i);r&&a.setAttribute("open","");let l=this.dom.find(".lh-audit-group__header",a),s=this._clumpTitles[e];this.dom.find(".lh-audit-group__title",l).textContent=s;let c=this.dom.find(".lh-audit-group__itemcount",a);c.textContent=`(${t.length})`;let d=t.map(p=>p instanceof HTMLElement?p:this.renderAudit(p));a.append(...d);let h=this.dom.find(".lh-audit-group",i);if(n){let p=this.dom.convertMarkdownLinkSnippets(n);p.classList.add("lh-audit-group__description","lh-audit-group__footer"),h.append(p)}return this.dom.find(".lh-clump-toggletext--show",h).textContent=u.strings.show,this.dom.find(".lh-clump-toggletext--hide",h).textContent=u.strings.hide,a.classList.add(`lh-clump--${e.toLowerCase()}`),h}renderCategoryScore(e,t,n){let r;if(n&&k.shouldDisplayAsFraction(n.gatherMode)?r=this.renderCategoryFraction(e):r=this.renderScoreGauge(e,t),n?.omitLabel&&this.dom.find(".lh-gauge__label,.lh-fraction__label",r).remove(),n?.onPageAnchorRendered){let i=this.dom.find("a",r);n.onPageAnchorRendered(i)}return r}renderScoreGauge(e,t){let n=this.dom.createComponent("gauge"),r=this.dom.find("a.lh-gauge__wrapper",n);k.isPluginCategory(e.id)&&r.classList.add("lh-gauge__wrapper--plugin");let i=Number(e.score),a=this.dom.find(".lh-gauge",n),l=this.dom.find("circle.lh-gauge-arc",a);l&&this._setGaugeArc(l,i);let s=Math.round(i*100),c=this.dom.find("div.lh-gauge__percentage",n);return c.textContent=s.toString(),e.score===null&&(c.classList.add("lh-gauge--error"),c.textContent="",c.title=u.strings.errorLabel),e.auditRefs.length===0||this.hasApplicableAudits(e)?r.classList.add(`lh-gauge__wrapper--${k.calculateRating(e.score)}`):(r.classList.add("lh-gauge__wrapper--not-applicable"),c.textContent="-",c.title=u.strings.notApplicableAuditsGroupTitle),this.dom.find(".lh-gauge__label",n).textContent=e.title,n}renderCategoryFraction(e){let t=this.dom.createComponent("fraction"),n=this.dom.find("a.lh-fraction__wrapper",t),{numPassed:r,numPassableAudits:i,totalWeight:a}=k.calculateCategoryFraction(e),l=r/i,s=this.dom.find(".lh-fraction__content",t),c=this.dom.createElement("span");c.textContent=`${r}/${i}`,s.append(c);let d=k.calculateRating(l);return a===0&&(d="null"),n.classList.add(`lh-fraction__wrapper--${d}`),this.dom.find(".lh-fraction__label",t).textContent=e.title,t}hasApplicableAudits(e){return e.auditRefs.some(t=>t.result.scoreDisplayMode!=="notApplicable")}_setGaugeArc(e,t){let n=2*Math.PI*Number(e.getAttribute("r")),r=Number(e.getAttribute("stroke-width")),i=.25*r/n;e.style.transform=`rotate(${-90+i*360}deg)`;let a=t*n-r/2;t===0&&(e.style.opacity="0"),t===1&&(a=n),e.style.strokeDasharray=`${Math.max(a,0)} ${n}`}_auditHasWarning(e){return!!e.result.warnings?.length}_getClumpIdForAuditRef(e){let t=e.result.scoreDisplayMode;return t==="manual"||t==="notApplicable"?t:k.showAsPassed(e.result)?this._auditHasWarning(e)?"warning":"passed":"failed"}render(e,t={},n){let r=this.dom.createElement("div","lh-category");r.id=e.id,r.append(this.renderCategoryHeader(e,t,n));let i=new Map;i.set("failed",[]),i.set("warning",[]),i.set("manual",[]),i.set("passed",[]),i.set("notApplicable",[]);for(let l of e.auditRefs){if(l.group==="hidden")continue;let s=this._getClumpIdForAuditRef(l),c=i.get(s);c.push(l),i.set(s,c)}for(let l of i.values())l.sort((s,c)=>c.weight-s.weight);let a=i.get("failed")?.length;for(let[l,s]of i){if(s.length===0)continue;if(l==="failed"){let p=this.renderUnexpandableClump(s,t);p.classList.add("lh-clump--failed"),r.append(p);continue}let c=l==="manual"?e.manualDescription:void 0,d=l==="warning"||l==="manual"&&a===0,h=this.renderClump(l,{auditRefsOrEls:s,description:c,openByDefault:d});r.append(h)}return r}};var Y=class{static createSegment(e,t,n,r){let i=e[t],a=Object.keys(e),l=a.indexOf(t)===a.length-1,s=!!i.children&&Object.keys(i.children).length>0,c=Array.isArray(n)?n.slice(0):[];return typeof r<"u"&&c.push(!r),{node:i,isLastChild:l,hasChildren:s,treeMarkers:c}}static createChainNode(e,t,n){let r=e.createComponent("crcChain"),i,a,l,s,c;"request"in t.node?(a=t.node.request.transferSize,l=t.node.request.url,i=(t.node.request.endTime-t.node.request.startTime)*1e3,s=!1):(a=t.node.transferSize,l=t.node.url,i=t.node.navStartToEndTime,s=!0,c=t.node.isLongest);let d=e.find(".lh-crc-node",r);d.setAttribute("title",l),c&&d.classList.add("lh-crc-node__longest");let h=e.find(".lh-crc-node__tree-marker",r);t.treeMarkers.forEach(m=>{let w=m?"lh-tree-marker lh-vert":"lh-tree-marker";h.append(e.createElement("span",w),e.createElement("span","lh-tree-marker"))});let p=t.isLastChild?"lh-tree-marker lh-up-right":"lh-tree-marker lh-vert-right",g=t.hasChildren?"lh-tree-marker lh-horiz-down":"lh-tree-marker lh-right";h.append(e.createElement("span",p),e.createElement("span","lh-tree-marker lh-right"),e.createElement("span",g));let v=n.renderTextURL(l),_=e.find(".lh-crc-node__tree-value",r);if(_.append(v),!t.hasChildren||s){let m=e.createElement("span","lh-crc-node__chain-duration");m.textContent=" - "+u.i18n.formatMilliseconds(i)+", ";let w=e.createElement("span","lh-crc-node__chain-size");w.textContent=u.i18n.formatBytesToKiB(a,.01),_.append(m,w)}return r}static buildTree(e,t,n,r){if(n.append(Q.createChainNode(e,t,r)),t.node.children)for(let i of Object.keys(t.node.children)){let a=Q.createSegment(t.node.children,i,t.treeMarkers,t.isLastChild);Q.buildTree(e,a,n,r)}}static render(e,t,n){let r=e.createComponent("crc"),i=e.find(".lh-crc",r);e.find(".lh-crc-initial-nav",r).textContent=u.strings.crcInitialNavigation,e.find(".lh-crc__longest_duration_label",r).textContent=u.strings.crcLongestDurationLabel,e.find(".lh-crc__longest_duration",r).textContent=u.i18n.formatMilliseconds(t.longestChain.duration);let a=t.chains;for(let l of Object.keys(a)){let s=Q.createSegment(a,l);Q.buildTree(e,s,i,n)}return e.find(".lh-crc-container",r)}},Q=Y;function ut(o,e){return e.left<=o.width&&0<=e.right&&e.top<=o.height&&0<=e.bottom}function Ee(o,e,t){return o<e?e:o>t?t:o}function mt(o){return{x:o.left+o.width/2,y:o.top+o.height/2}}var V=class o{static getScreenshotPositions(e,t,n){let r=mt(e),i=Ee(r.x-t.width/2,0,n.width-t.width),a=Ee(r.y-t.height/2,0,n.height-t.height);return{screenshot:{left:i,top:a},clip:{left:e.left-i,top:e.top-a}}}static renderClipPathInScreenshot(e,t,n,r,i){let a=e.find("clipPath",t),l=`clip-${u.getUniqueSuffix()}`;a.id=l,t.style.clipPath=`url(#${l})`;let s=n.top/i.height,c=s+r.height/i.height,d=n.left/i.width,h=d+r.width/i.width,p=[`0,0             1,0            1,${s}          0,${s}`,`0,${c}     1,${c}    1,1               0,1`,`0,${s}        ${d},${s} ${d},${c} 0,${c}`,`${h},${s} 1,${s}       1,${c}       ${h},${c}`];for(let g of p){let v=e.createElementNS("http://www.w3.org/2000/svg","polygon");v.setAttribute("points",g),a.append(v)}}static installFullPageScreenshot(e,t){e.style.setProperty("--element-screenshot-url",`url('${t.data}')`)}static installOverlayFeature(e){let{dom:t,rootEl:n,overlayContainerEl:r,fullPageScreenshot:i}=e,a="lh-screenshot-overlay--enabled";n.classList.contains(a)||(n.classList.add(a),n.addEventListener("click",l=>{let s=l.target;if(!s)return;let c=s.closest(".lh-node > .lh-element-screenshot");if(!c)return;let d=t.createElement("div","lh-element-screenshot__overlay");r.append(d);let h={width:d.clientWidth*.95,height:d.clientHeight*.8},p={width:Number(c.dataset.rectWidth),height:Number(c.dataset.rectHeight),left:Number(c.dataset.rectLeft),right:Number(c.dataset.rectLeft)+Number(c.dataset.rectWidth),top:Number(c.dataset.rectTop),bottom:Number(c.dataset.rectTop)+Number(c.dataset.rectHeight)},g=o.render(t,i.screenshot,p,h);if(!g){d.remove();return}d.append(g),d.addEventListener("click",()=>d.remove())}))}static _computeZoomFactor(e,t){let r={x:t.width/e.width,y:t.height/e.height},i=.75*Math.min(r.x,r.y);return Math.min(1,i)}static render(e,t,n,r){if(!ut(t,n))return null;let i=e.createComponent("elementScreenshot"),a=e.find("div.lh-element-screenshot",i);a.dataset.rectWidth=n.width.toString(),a.dataset.rectHeight=n.height.toString(),a.dataset.rectLeft=n.left.toString(),a.dataset.rectTop=n.top.toString();let l=this._computeZoomFactor(n,r),s={width:r.width/l,height:r.height/l};s.width=Math.min(t.width,s.width),s.height=Math.min(t.height,s.height);let c={width:s.width*l,height:s.height*l},d=o.getScreenshotPositions(n,s,{width:t.width,height:t.height}),h=e.find("div.lh-element-screenshot__image",a);h.style.width=c.width+"px",h.style.height=c.height+"px",h.style.backgroundPositionY=-(d.screenshot.top*l)+"px",h.style.backgroundPositionX=-(d.screenshot.left*l)+"px",h.style.backgroundSize=`${t.width*l}px ${t.height*l}px`;let p=e.find("div.lh-element-screenshot__element-marker",a);p.style.width=n.width*l+"px",p.style.height=n.height*l+"px",p.style.left=d.clip.left*l+"px",p.style.top=d.clip.top*l+"px";let g=e.find("div.lh-element-screenshot__mask",a);return g.style.width=c.width+"px",g.style.height=c.height+"px",o.renderClipPathInScreenshot(e,g,d.clip,n,s),a}};var ft=["http://","https://","data:"],bt=["bytes","numeric","ms","timespanMs"],X=class{constructor(e,t={}){this._dom=e,this._fullPageScreenshot=t.fullPageScreenshot,this._entities=t.entities}render(e){switch(e.type){case"filmstrip":return this._renderFilmstrip(e);case"list":return this._renderList(e);case"checklist":return this._renderChecklist(e);case"table":case"opportunity":return this._renderTable(e);case"network-tree":case"criticalrequestchain":return Y.render(this._dom,e,this);case"screenshot":case"debugdata":case"treemap-data":return null;default:return this._renderUnknown(e.type,e)}}_renderBytes(e){let t=u.i18n.formatBytesToKiB(e.value,e.granularity||.1),n=this._renderText(t);return n.title=u.i18n.formatBytes(e.value),n}_renderMilliseconds(e){let t;return e.displayUnit==="duration"?t=u.i18n.formatDuration(e.value):t=u.i18n.formatMilliseconds(e.value,e.granularity||10),this._renderText(t)}renderTextURL(e){let t=e,n,r,i;try{let l=E.parseURL(t);n=l.file==="/"?l.origin:l.file,r=l.file==="/"||l.hostname===""?"":`(${l.hostname})`,i=t}catch{n=t}let a=this._dom.createElement("div","lh-text__url");if(a.append(this._renderLink({text:n,url:t})),r){let l=this._renderText(r);l.classList.add("lh-text__url-host"),a.append(l)}return i&&(a.title=t,a.dataset.url=t),a}_renderLink(e){let t=this._dom.createElement("a");if(this._dom.safelySetHref(t,e.url),!t.href){let n=this._renderText(e.text);return n.classList.add("lh-link"),n}return t.rel="noopener",t.target="_blank",t.textContent=e.text,t.classList.add("lh-link"),t}_renderText(e){let t=this._dom.createElement("div","lh-text");return t.textContent=e,t}_renderNumeric(e){let t=u.i18n.formatNumber(e.value,e.granularity||.1),n=this._dom.createElement("div","lh-numeric");return n.textContent=t,n}_renderThumbnail(e){let t=this._dom.createElement("img","lh-thumbnail"),n=e;return t.src=n,t.title=n,t.alt="",t}_renderUnknown(e,t){console.error(`Unknown details type: ${e}`,t);let n=this._dom.createElement("details","lh-unknown");return this._dom.createChildOf(n,"summary").textContent=`We don't know how to render audit details of type \`${e}\`. The Lighthouse version that collected this data is likely newer than the Lighthouse version of the report renderer. Expand for the raw JSON.`,this._dom.createChildOf(n,"pre").textContent=JSON.stringify(t,null,2),n}_renderTableValue(e,t){if(e==null)return null;if(typeof e=="object")switch(e.type){case"code":return this._renderCode(e.value);case"link":return this._renderLink(e);case"node":return this.renderNode(e);case"numeric":return this._renderNumeric(e);case"text":return this._renderText(e.value);case"source-location":return this.renderSourceLocation(e);case"url":return this.renderTextURL(e.value);default:return this._renderUnknown(e.type,e)}switch(t.valueType){case"bytes":{let n=Number(e);return this._renderBytes({value:n,granularity:t.granularity})}case"code":{let n=String(e);return this._renderCode(n)}case"ms":{let n={value:Number(e),granularity:t.granularity,displayUnit:t.displayUnit};return this._renderMilliseconds(n)}case"numeric":{let n=Number(e);return this._renderNumeric({value:n,granularity:t.granularity})}case"text":{let n=String(e);return this._renderText(n)}case"thumbnail":{let n=String(e);return this._renderThumbnail(n)}case"timespanMs":{let n=Number(e);return this._renderMilliseconds({value:n})}case"url":{let n=String(e);return ft.some(r=>n.startsWith(r))?this.renderTextURL(n):this._renderCode(n)}default:return this._renderUnknown(t.valueType,e)}}_getDerivedSubItemsHeading(e){return e.subItemsHeading?{key:e.subItemsHeading.key||"",valueType:e.subItemsHeading.valueType||e.valueType,granularity:e.subItemsHeading.granularity||e.granularity,displayUnit:e.subItemsHeading.displayUnit||e.displayUnit,label:""}:null}_renderTableRow(e,t){let n=this._dom.createElement("tr");for(let r of t){if(!r||!r.key){this._dom.createChildOf(n,"td","lh-table-column--empty");continue}let i=e[r.key],a;if(i!=null&&(a=this._renderTableValue(i,r)),a){let l=`lh-table-column--${r.valueType}`;this._dom.createChildOf(n,"td",l).append(a)}else this._dom.createChildOf(n,"td","lh-table-column--empty")}return n}_renderTableRowsFromItem(e,t){let n=this._dom.createFragment();if(n.append(this._renderTableRow(e,t)),!e.subItems)return n;let r=t.map(this._getDerivedSubItemsHeading);if(!r.some(Boolean))return n;for(let i of e.subItems.items){let a=this._renderTableRow(i,r);a.classList.add("lh-sub-item-row"),n.append(a)}return n}_adornEntityGroupRow(e){let t=e.dataset.entity;if(!t)return;let n=this._entities?.find(i=>i.name===t);if(!n)return;let r=this._dom.find("td",e);if(n.category){let i=this._dom.createElement("span");i.classList.add("lh-audit__adorn"),i.textContent=n.category,r.append(" ",i)}if(n.isFirstParty){let i=this._dom.createElement("span");i.classList.add("lh-audit__adorn","lh-audit__adorn1p"),i.textContent=u.strings.firstPartyChipLabel,r.append(" ",i)}if(n.homepage){let i=this._dom.createElement("a");i.href=n.homepage,i.target="_blank",i.title=u.strings.openInANewTabTooltip,i.classList.add("lh-report-icon--external"),r.append(" ",i)}}_renderEntityGroupRow(e,t){let n={...t[0]};n.valueType="text";let r=[n,...t.slice(1)],i=this._dom.createFragment();return i.append(this._renderTableRow(e,r)),this._dom.find("tr",i).classList.add("lh-row--group"),i}_getEntityGroupItems(e){let{items:t,headings:n,sortedBy:r}=e;if(!t.length||e.isEntityGrouped||!t.some(d=>d.entity))return[];let i=new Set(e.skipSumming||[]),a=[];for(let d of n)!d.key||i.has(d.key)||bt.includes(d.valueType)&&a.push(d.key);let l=n[0].key;if(!l)return[];let s=new Map;for(let d of t){let h=typeof d.entity=="string"?d.entity:void 0,p=s.get(h)||{[l]:h||u.strings.unattributable,entity:h};for(let g of a)p[g]=Number(p[g]||0)+Number(d[g]||0);s.set(h,p)}let c=[...s.values()];return r&&c.sort(k.getTableItemSortComparator(r)),c}_renderTable(e){if(!e.items.length)return this._dom.createElement("span");let t=this._dom.createElement("table","lh-table"),n=this._dom.createChildOf(t,"thead"),r=this._dom.createChildOf(n,"tr");for(let l of e.headings){let c=`lh-table-column--${l.valueType||"text"}`,d=this._dom.createElement("div","lh-text");d.textContent=l.label,this._dom.createChildOf(r,"th",c).append(d)}let i=this._getEntityGroupItems(e),a=this._dom.createChildOf(t,"tbody");if(i.length)for(let l of i){let s=typeof l.entity=="string"?l.entity:void 0,c=this._renderEntityGroupRow(l,e.headings);for(let h of e.items.filter(p=>p.entity===s))c.append(this._renderTableRowsFromItem(h,e.headings));let d=this._dom.findAll("tr",c);s&&d.length&&(d.forEach(h=>h.dataset.entity=s),this._adornEntityGroupRow(d[0])),a.append(c)}else{let l=!0;for(let s of e.items){let c=this._renderTableRowsFromItem(s,e.headings),d=this._dom.findAll("tr",c),h=d[0];if(typeof s.entity=="string"&&(h.dataset.entity=s.entity),e.isEntityGrouped&&s.entity)h.classList.add("lh-row--group"),this._adornEntityGroupRow(h);else for(let p of d)p.classList.add(l?"lh-row--even":"lh-row--odd");l=!l,a.append(c)}}return t}_renderList(e){let t=this._dom.createElement("div","lh-list");return e.items.forEach(n=>{if(n.type==="node"){t.append(this.renderNode(n));return}let r=this.render(n);r&&t.append(r)}),t}_renderChecklist(e){let t=this._dom.createElement("ul","lh-checklist");return Object.values(e.items).forEach(n=>{let r=this._dom.createChildOf(t,"li","lh-checklist-item"),i=n.value?"lh-report-plain-icon--checklist-pass":"lh-report-plain-icon--checklist-fail";this._dom.createChildOf(r,"span",`lh-report-plain-icon ${i}`).textContent=n.label}),t}renderNode(e){let t=this._dom.createElement("span","lh-node");if(e.nodeLabel){let a=this._dom.createElement("div");a.textContent=e.nodeLabel,t.append(a)}if(e.snippet){let a=this._dom.createElement("div");a.classList.add("lh-node__snippet"),a.textContent=e.snippet,t.append(a)}if(e.selector&&(t.title=e.selector),e.path&&t.setAttribute("data-path",e.path),e.selector&&t.setAttribute("data-selector",e.selector),e.snippet&&t.setAttribute("data-snippet",e.snippet),!this._fullPageScreenshot)return t;let n=e.lhId&&this._fullPageScreenshot.nodes[e.lhId];if(!n||n.width===0||n.height===0)return t;let r={width:147,height:100},i=V.render(this._dom,this._fullPageScreenshot.screenshot,n,r);return i&&t.prepend(i),t}renderSourceLocation(e){if(!e.url)return null;let t=`${e.url}:${e.line+1}:${e.column}`,n;e.original&&(n=`${e.original.file||"<unmapped>"}:${e.original.line+1}:${e.original.column}`);let r;if(e.urlProvider==="network"&&n)r=this._renderLink({url:e.url,text:n}),r.title=`maps to generated location ${t}`;else if(e.urlProvider==="network"&&!n)r=this.renderTextURL(e.url),this._dom.find(".lh-link",r).textContent+=`:${e.line+1}:${e.column}`;else if(e.urlProvider==="comment"&&n)r=this._renderText(`${n} (from source map)`),r.title=`${t} (from sourceURL)`;else if(e.urlProvider==="comment"&&!n)r=this._renderText(`${t} (from sourceURL)`);else return null;return r.classList.add("lh-source-location"),r.setAttribute("data-source-url",e.url),r.setAttribute("data-source-line",String(e.line)),r.setAttribute("data-source-column",String(e.column)),r}_renderFilmstrip(e){let t=this._dom.createElement("div","lh-filmstrip");for(let n of e.items){let r=this._dom.createChildOf(t,"div","lh-filmstrip__frame"),i=this._dom.createChildOf(r,"img","lh-filmstrip__thumbnail");i.src=n.data,i.alt="Screenshot"}return t}_renderCode(e){let t=this._dom.createElement("pre","lh-code");return t.textContent=e,t}};var se="\xA0";var te=class{constructor(e){e==="en-XA"&&(e="de"),this._locale=e,this._cachedNumberFormatters=new Map}_formatNumberWithGranularity(e,t,n={}){if(t!==void 0){let a=-Math.log10(t);Number.isInteger(a)||(console.warn(`granularity of ${t} is invalid. Using 1 instead`),t=1),t<1&&(n={...n},n.minimumFractionDigits=n.maximumFractionDigits=Math.ceil(a)),e=Math.round(e/t)*t,Object.is(e,-0)&&(e=0)}else Math.abs(e)<5e-4&&(e=0);let r,i=[n.minimumFractionDigits,n.maximumFractionDigits,n.style,n.unit,n.unitDisplay,this._locale].join("");return r=this._cachedNumberFormatters.get(i),r||(r=new Intl.NumberFormat(this._locale,n),this._cachedNumberFormatters.set(i,r)),r.format(e).replace(" ",se)}formatNumber(e,t){return this._formatNumberWithGranularity(e,t)}formatInteger(e){return this._formatNumberWithGranularity(e,1)}formatPercent(e){return new Intl.NumberFormat(this._locale,{style:"percent"}).format(e)}formatBytesToKiB(e,t=void 0){return this._formatNumberWithGranularity(e/1024,t)+`${se}KiB`}formatBytesToMiB(e,t=void 0){return this._formatNumberWithGranularity(e/1048576,t)+`${se}MiB`}formatBytes(e,t=1){return this._formatNumberWithGranularity(e,t,{style:"unit",unit:"byte",unitDisplay:"long"})}formatBytesWithBestUnit(e,t=.1){return e>=1048576?this.formatBytesToMiB(e,t):e>=1024?this.formatBytesToKiB(e,t):this._formatNumberWithGranularity(e,t,{style:"unit",unit:"byte",unitDisplay:"narrow"})}formatKbps(e,t=void 0){return this._formatNumberWithGranularity(e,t,{style:"unit",unit:"kilobit-per-second",unitDisplay:"short"})}formatMilliseconds(e,t=void 0){return this._formatNumberWithGranularity(e,t,{style:"unit",unit:"millisecond",unitDisplay:"short"})}formatSeconds(e,t=void 0){return this._formatNumberWithGranularity(e/1e3,t,{style:"unit",unit:"second",unitDisplay:"narrow"})}formatDateTime(e){let t={month:"short",day:"numeric",year:"numeric",hour:"numeric",minute:"numeric",timeZoneName:"short"},n;try{n=new Intl.DateTimeFormat(this._locale,t)}catch{t.timeZone="UTC",n=new Intl.DateTimeFormat(this._locale,t)}return n.format(new Date(e))}formatDuration(e){let t=e/1e3;if(Math.round(t)===0)return"None";let n=[],r={day:60*60*24,hour:60*60,minute:60,second:1};return Object.keys(r).forEach(i=>{let a=r[i],l=Math.floor(t/a);if(l>0){t-=l*a;let s=this._formatNumberWithGranularity(l,1,{style:"unit",unit:i,unitDisplay:"narrow"});n.push(s)}}),n.join(" ")}};function Se(o){let e=o.createComponent("explodeyGauge");return o.find(".lh-exp-gauge-component",e)}function Ce(o,e,t){let n=o.find("div.lh-exp-gauge__wrapper",e);n.className="",n.classList.add("lh-exp-gauge__wrapper",`lh-exp-gauge__wrapper--${k.calculateRating(t.score)}`),_t(o,n,t)}function vt(o,e,t){t=t||o/32;let n=o/t,r=.5*t,i=n+r+t,a=2*Math.PI*n,l=Math.acos(1-.5*Math.pow(.5*t/n,2))*n,s=2*Math.PI*i,c=Math.acos(1-.5*Math.pow(.5*t/i,2))*i;return{radiusInner:n,radiusOuter:i,circumferenceInner:a,circumferenceOuter:s,getArcLength:()=>Math.max(0,Number(e*a)),getMetricArcLength:(d,h=!1)=>{let p=h?0:2*c;return Math.max(0,Number(d*s-r-p))},endDiffInner:l,endDiffOuter:c,strokeWidth:t,strokeGap:r}}function _t(o,e,t){let i=Number(t.score),{radiusInner:a,radiusOuter:l,circumferenceInner:s,circumferenceOuter:c,getArcLength:d,getMetricArcLength:h,endDiffInner:p,endDiffOuter:g,strokeWidth:v,strokeGap:_}=vt(128,i),m=o.find("svg.lh-exp-gauge",e);o.find(".lh-exp-gauge__label",m).textContent=t.title,m.setAttribute("viewBox",[-64,-64/2,128,128/2].join(" ")),m.style.setProperty("--stroke-width",`${v}px`),m.style.setProperty("--circle-meas",(2*Math.PI).toFixed(4));let w=o.find("g.lh-exp-gauge__outer",e),f=o.find("g.lh-exp-gauge__inner",e),b=o.find("circle.lh-cover",w),y=o.find("circle.lh-exp-gauge__arc",f),S=o.find("text.lh-exp-gauge__percentage",f);w.style.setProperty("--scale-initial",String(a/l)),w.style.setProperty("--radius",`${l}px`),b.style.setProperty("--radius",`${.5*(a+l)}px`),b.setAttribute("stroke-width",String(_)),m.style.setProperty("--radius",`${a}px`),y.setAttribute("stroke-dasharray",`${d()} ${(s-d()).toFixed(4)}`),y.setAttribute("stroke-dashoffset",String(.25*s-p)),S.textContent=Math.round(i*100).toString();let C=l+v,L=l-v,z=t.auditRefs.filter(x=>x.group==="metrics"&&x.weight),F=z.reduce((x,A)=>x+=A.weight,0),R=.25*c-g-.5*_,N=-.5*Math.PI;w.querySelectorAll(".metric").forEach(x=>{z.map(D=>`metric--${D.id}`).find(D=>x.classList.contains(D))||x.remove()}),z.forEach((x,A)=>{let T=x.acronym??x.id,D=!w.querySelector(`.metric--${T}`),M=o.maybeFind(`g.metric--${T}`,w)||o.createSVGElement("g"),B=o.maybeFind(`.metric--${T} circle.lh-exp-gauge--faded`,w)||o.createSVGElement("circle"),K=o.maybeFind(`.metric--${T} circle.lh-exp-gauge--miniarc`,w)||o.createSVGElement("circle"),q=o.maybeFind(`.metric--${T} circle.lh-exp-gauge-hovertarget`,w)||o.createSVGElement("circle"),P=o.maybeFind(`.metric--${T} text.metric__label`,w)||o.createSVGElement("text"),U=o.maybeFind(`.metric--${T} text.metric__value`,w)||o.createSVGElement("text");M.classList.add("metric",`metric--${T}`),B.classList.add("lh-exp-gauge__arc","lh-exp-gauge__arc--metric","lh-exp-gauge--faded"),K.classList.add("lh-exp-gauge__arc","lh-exp-gauge__arc--metric","lh-exp-gauge--miniarc"),q.classList.add("lh-exp-gauge__arc","lh-exp-gauge__arc--metric","lh-exp-gauge-hovertarget");let j=x.weight/F,he=h(j),pe=x.result.score?x.result.score*j:0,ge=h(pe),Re=j*c,ue=h(j,!0),me=k.calculateRating(x.result.score,x.result.scoreDisplayMode);M.style.setProperty("--metric-rating",me),M.style.setProperty("--metric-color",`var(--color-${me})`),M.style.setProperty("--metric-offset",`${R}`),M.style.setProperty("--i",A.toString()),B.setAttribute("stroke-dasharray",`${he} ${c-he}`),K.style.setProperty("--metric-array",`${ge} ${c-ge}`),q.setAttribute("stroke-dasharray",`${ue} ${c-ue-g}`),P.classList.add("metric__label"),U.classList.add("metric__value"),P.textContent=T,U.textContent=`+${Math.round(pe*100)}`;let fe=N+j*Math.PI,Z=Math.cos(fe),J=Math.sin(fe);switch(!0){case Z>0:U.setAttribute("text-anchor","end");break;case Z<0:P.setAttribute("text-anchor","end");break;case Z===0:P.setAttribute("text-anchor","middle"),U.setAttribute("text-anchor","middle");break}switch(!0){case J>0:P.setAttribute("dominant-baseline","hanging");break;case J<0:U.setAttribute("dominant-baseline","hanging");break;case J===0:P.setAttribute("dominant-baseline","middle"),U.setAttribute("dominant-baseline","middle");break}P.setAttribute("x",(C*Z).toFixed(2)),P.setAttribute("y",(C*J).toFixed(2)),U.setAttribute("x",(L*Z).toFixed(2)),U.setAttribute("y",(L*J).toFixed(2)),D&&(M.appendChild(B),M.appendChild(K),M.appendChild(q),M.appendChild(P),M.appendChild(U),w.appendChild(M)),R-=Re,N+=j*2*Math.PI});let I=w.querySelector(".lh-exp-gauge-underhovertarget")||o.createSVGElement("circle");I.classList.add("lh-exp-gauge__arc","lh-exp-gauge__arc--metric","lh-exp-gauge-hovertarget","lh-exp-gauge-underhovertarget");let H=h(1,!0);if(I.setAttribute("stroke-dasharray",`${H} ${c-H-g}`),I.isConnected||w.prepend(I),m.dataset.listenersSetup)return;m.dataset.listenersSetup=!0,Pe(m),m.addEventListener("pointerover",x=>{if(x.target===m&&m.classList.contains("state--expanded")){m.classList.remove("state--expanded"),m.classList.contains("state--highlight")&&(m.classList.remove("state--highlight"),o.find(".metric--highlight",m).classList.remove("metric--highlight"));return}if(!(x.target instanceof Element))return;let A=x.target.parentNode;if(A instanceof SVGElement){if(A&&A===f){m.classList.contains("state--expanded")?m.classList.contains("state--highlight")&&(m.classList.remove("state--highlight"),o.find(".metric--highlight",m).classList.remove("metric--highlight")):m.classList.add("state--expanded");return}if(A&&A.classList&&A.classList.contains("metric")){let T=A.style.getPropertyValue("--metric-rating");if(e.style.setProperty("--color-highlight",`var(--color-${T}-secondary)`),!m.classList.contains("state--highlight"))m.classList.add("state--highlight"),A.classList.add("metric--highlight");else{let D=o.find(".metric--highlight",m);A!==D&&(D.classList.remove("metric--highlight"),A.classList.add("metric--highlight"))}}}}),m.addEventListener("mouseleave",()=>{m.classList.remove("state--highlight"),m.querySelector(".metric--highlight")?.classList.remove("metric--highlight")});async function Pe(x){if(await new Promise(P=>setTimeout(P,1e3)),x.classList.contains("state--expanded"))return;let A=o.find(".lh-exp-gauge__inner",x),T=`uniq-${Math.random()}`;A.setAttribute("id",T);let D=o.createSVGElement("use");D.setAttribute("href",`#${T}`),x.appendChild(D);let M=2.5;x.style.setProperty("--peek-dur",`${M}s`),x.classList.add("state--peek","state--expanded");let B=()=>{x.classList.remove("state--peek","state--expanded"),D.remove()},K=setTimeout(()=>{x.removeEventListener("mouseenter",q),B()},M*1e3*1.5);function q(){clearTimeout(K),B()}x.addEventListener("mouseenter",q,{once:!0})}}var Ae="__lh__insights_audits_toggle_state",ne=class extends G{_memoryInsightToggleState="DEFAULT";_renderMetric(e){let t=this.dom.createComponent("metric"),n=this.dom.find(".lh-metric",t);n.id=e.result.id;let r=k.calculateRating(e.result.score,e.result.scoreDisplayMode);n.classList.add(`lh-metric--${r}`);let i=this.dom.find(".lh-metric__title",t);i.textContent=e.result.title;let a=this.dom.find(".lh-metric__value",t);a.textContent=e.result.displayValue||"";let l=this.dom.find(".lh-metric__description",t);if(l.append(this.dom.convertMarkdownLinkSnippets(e.result.description)),e.result.scoreDisplayMode==="error"){l.textContent="",a.textContent="Error!";let s=this.dom.createChildOf(l,"span");s.textContent=e.result.errorMessage||"Report error: no metric information"}else e.result.scoreDisplayMode==="notApplicable"&&(a.textContent="--");return n}_getScoringCalculatorHref(e){let t=e.filter(h=>h.group==="metrics"),n=e.find(h=>h.id==="interactive"),r=e.find(h=>h.id==="first-cpu-idle"),i=e.find(h=>h.id==="first-meaningful-paint");n&&t.push(n),r&&t.push(r),i&&typeof i.result.score=="number"&&t.push(i);let a=h=>Math.round(h*100)/100,s=[...t.map(h=>{let p;return typeof h.result.numericValue=="number"?(p=h.id==="cumulative-layout-shift"?a(h.result.numericValue):Math.round(h.result.numericValue),p=p.toString()):p="null",[h.acronym||h.id,p]})];u.reportJson&&(s.push(["device",u.reportJson.configSettings.formFactor]),s.push(["version",u.reportJson.lighthouseVersion]));let c=new URLSearchParams(s),d=new URL("https://googlechrome.github.io/lighthouse/scorecalc/");return d.hash=c.toString(),d.href}overallImpact(e,t){if(!e.result.metricSavings)return{overallImpact:0,overallLinearImpact:0};let n=0,r=0;for(let[i,a]of Object.entries(e.result.metricSavings)){if(a===void 0)continue;let l=t.find(g=>g.acronym===i);if(!l||l.result.score===null)continue;let s=l.result.numericValue;if(!s)continue;let c=a/s*l.weight;r+=c;let d=l.result.scoringOptions;if(!d)continue;let p=(E.computeLogNormalScore(d,s-a)-l.result.score)*l.weight;n+=p}return{overallImpact:n,overallLinearImpact:r}}_persistInsightToggleToStorage(e){try{window.localStorage.setItem(Ae,e)}finally{this._memoryInsightToggleState=e}}_getInsightToggleState(){let e=this._getRawInsightToggleState();return e==="DEFAULT"&&(e="AUDITS"),e}_getRawInsightToggleState(){try{let e=window.localStorage.getItem(Ae);if(e==="AUDITS"||e==="INSIGHTS")return e}catch{return this._memoryInsightToggleState}return"DEFAULT"}_setInsightToggleButtonText(e){let t=this._getInsightToggleState();e.innerText=t==="AUDITS"?u.strings.tryInsights:u.strings.goBackToAudits}_renderInsightsToggle(e){let t=this.dom.createChildOf(e,"div","lh-perf-insights-toggle"),n=this.dom.createChildOf(t,"span","lh-perf-toggle-text"),r=this.dom.createElement("span","lh-perf-insights-icon");r.innerHTML='<svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor" xmlns="http://www.w3.org/2000/svg"><path d="M18 13V11H22V13H18ZM19.2 20L16 17.6L17.2 16L20.4 18.4L19.2 20ZM17.2 8L16 6.4L19.2 4L20.4 5.6L17.2 8ZM5 19V15H4C3.45 15 2.975 14.8083 2.575 14.425C2.19167 14.025 2 13.55 2 13V11C2 10.45 2.19167 9.98333 2.575 9.6C2.975 9.2 3.45 9 4 9H8L13 6V18L8 15H7V19H5ZM11 14.45V9.55L8.55 11H4V13H8.55L11 14.45ZM14 15.35V8.65C14.45 9.05 14.8083 9.54167 15.075 10.125C15.3583 10.6917 15.5 11.3167 15.5 12C15.5 12.6833 15.3583 13.3167 15.075 13.9C14.8083 14.4667 14.45 14.95 14 15.35Z"/>\u003c/svg>',n.appendChild(r),n.appendChild(this.dom.convertMarkdownLinkSnippets(u.strings.insightsNotice));let a=this.dom.createChildOf(t,"button","lh-button lh-button-insight-toggle");this._setInsightToggleButtonText(a),a.addEventListener("click",l=>{l.preventDefault();let s=this.dom.maybeFind(".lh-perf-audits--swappable");s&&this.dom.swapSectionIfPossible(s);let d=this._getInsightToggleState()==="AUDITS"?"INSIGHTS":"AUDITS";this.dom.fireEventOn("lh-analytics",this.dom.document(),{name:"toggle_insights",data:{newState:d}}),this._persistInsightToggleToStorage(d),this._setInsightToggleButtonText(a)}),t.appendChild(a)}render(e,t,n){let r=u.strings,i=this.dom.createElement("div","lh-category");i.id=e.id,i.append(this.renderCategoryHeader(e,t,n));let a=e.auditRefs.filter(g=>g.group==="metrics");if(a.length){let[g,v]=this.renderAuditGroup(t.metrics),_=this.dom.createElement("input","lh-metrics-toggle__input"),m=`lh-metrics-toggle${u.getUniqueSuffix()}`;_.setAttribute("aria-label","Toggle the display of metric descriptions"),_.type="checkbox",_.id=m,g.prepend(_);let w=this.dom.find(".lh-audit-group__header",g),f=this.dom.createChildOf(w,"label","lh-metrics-toggle__label");f.htmlFor=m;let b=this.dom.createChildOf(f,"span","lh-metrics-toggle__labeltext--show"),y=this.dom.createChildOf(f,"span","lh-metrics-toggle__labeltext--hide");b.textContent=u.strings.expandView,y.textContent=u.strings.collapseView;let S=this.dom.createElement("div","lh-metrics-container");if(g.insertBefore(S,v),a.forEach(C=>{S.append(this._renderMetric(C))}),i.querySelector(".lh-gauge__wrapper")){let C=this.dom.find(".lh-category-header__description",i),L=this.dom.createChildOf(C,"div","lh-metrics__disclaimer"),z=this.dom.convertMarkdownLinkSnippets(r.varianceDisclaimer);L.append(z);let F=this.dom.createChildOf(L,"a","lh-calclink");F.target="_blank",F.textContent=r.calculatorLink,this.dom.safelySetHref(F,this._getScoringCalculatorHref(e.auditRefs))}g.classList.add("lh-audit-group--metrics"),i.append(g)}let l=this.dom.createChildOf(i,"div","lh-filmstrip-container"),c=e.auditRefs.find(g=>g.id==="screenshot-thumbnails")?.result;if(c?.details){l.id=c.id;let g=this.detailsRenderer.render(c.details);g&&l.append(g)}this._renderInsightsToggle(i);let d=this.renderFilterableSection(e,t,["diagnostics"],a);d?.classList.add("lh-perf-audits--swappable","lh-perf-audits--legacy");let h=this.renderFilterableSection(e,t,["insights","diagnostics"],a);if(h?.classList.add("lh-perf-audits--swappable","lh-perf-audits--experimental"),d&&(i.append(d),h&&this.dom.registerSwappableSections(d,h)),this._getInsightToggleState()==="INSIGHTS"&&requestAnimationFrame(()=>{let g=this.dom.maybeFind(".lh-perf-audits--swappable");g&&this.dom.swapSectionIfPossible(g)}),this.dom.fireEventOn("lh-analytics",this.dom.document(),{name:"initial_insights_state",data:{state:this._getRawInsightToggleState()}}),(!n||n?.gatherMode==="navigation")&&e.score!==null){let g=Se(this.dom);Ce(this.dom,g,e),this.dom.find(".lh-score__gauge",i).replaceWith(g)}return i}renderFilterableSection(e,t,n,r){if(n.some(f=>!t[f]))return null;let i=this.dom.createElement("div"),a=new Set,l=f=>f.id.endsWith("-insight")?"insights":f.group??"",s=e.auditRefs.filter(f=>n.includes(l(f)));for(let f of s)f.result.replacesAudits?.forEach(b=>{a.add(b)});let c=s.filter(f=>!a.has(f.id)).map(f=>{let{overallImpact:b,overallLinearImpact:y}=this.overallImpact(f,r),S=f.result.guidanceLevel||1,C=this.renderAudit(f);return{auditRef:f,auditEl:C,overallImpact:b,overallLinearImpact:y,guidanceLevel:S}}),d=c.filter(f=>!k.showAsPassed(f.auditRef.result)),h=c.filter(f=>k.showAsPassed(f.auditRef.result)),p={};for(let f of n){let b=this.renderAuditGroup(t[f]);b[0].classList.add(`lh-audit-group--${f}`),p[f]=b}function g(f){for(let b of c)if(f==="All")b.auditEl.hidden=!1;else{let y=b.auditRef.result.metricSavings?.[f]===void 0;b.auditEl.hidden=y}d.sort((b,y)=>{let S=b.auditRef.result.score||0,C=y.auditRef.result.score||0;if(S!==C)return S-C;if(f!=="All"){let L=b.auditRef.result.metricSavings?.[f]??-1,z=y.auditRef.result.metricSavings?.[f]??-1;if(L!==z)return z-L}return b.overallImpact!==y.overallImpact?y.overallImpact*y.guidanceLevel-b.overallImpact*b.guidanceLevel:b.overallImpact===0&&y.overallImpact===0&&b.overallLinearImpact!==y.overallLinearImpact?y.overallLinearImpact*y.guidanceLevel-b.overallLinearImpact*b.guidanceLevel:y.guidanceLevel-b.guidanceLevel});for(let b of d){if(!b.auditRef.group)continue;let y=p[l(b.auditRef)];if(!y)continue;let[S,C]=y;S.insertBefore(b.auditEl,C)}}let v=new Set;for(let f of d){let b=f.auditRef.result.metricSavings||{};for(let[y,S]of Object.entries(b))typeof S=="number"&&v.add(y)}let _=r.filter(f=>f.acronym&&v.has(f.acronym));_.length&&this.renderMetricAuditFilter(_,i,g),g("All");for(let f of n)if(d.some(b=>l(b.auditRef)===f)){let b=p[f];if(!b)continue;i.append(b[0])}if(!h.length)return i;let m={auditRefsOrEls:h.map(f=>f.auditEl),groupDefinitions:t},w=this.renderClump("passed",m);return i.append(w),i}renderMetricAuditFilter(e,t,n){let r=this.dom.createElement("div","lh-metricfilter"),i=this.dom.createChildOf(r,"span","lh-metricfilter__text");i.textContent=u.strings.showRelevantAudits;let a=[{acronym:"All",id:"All"},...e],l=u.getUniqueSuffix();for(let s of a){let c=`metric-${s.acronym}-${l}`,d=this.dom.createChildOf(r,"input","lh-metricfilter__radio");d.type="radio",d.name=`metricsfilter-${l}`,d.id=c;let h=this.dom.createChildOf(r,"label","lh-metricfilter__label");h.htmlFor=c,h.title="result"in s?s.result.title:"",h.textContent=s.acronym||s.id,s.acronym==="All"&&(d.checked=!0,h.classList.add("lh-metricfilter__label--active")),t.append(r),d.addEventListener("input",p=>{for(let v of t.querySelectorAll("label.lh-metricfilter__label"))v.classList.toggle("lh-metricfilter__label--active",v.htmlFor===c);t.classList.toggle("lh-category--filtered",s.acronym!=="All"),n(s.acronym||"All");let g=t.querySelectorAll("div.lh-audit-group, details.lh-audit-group");for(let v of g){v.hidden=!1;let _=Array.from(v.querySelectorAll("div.lh-audit")),m=!!_.length&&_.every(w=>w.hidden);v.hidden=m}})}}};var re=class{constructor(e){this._dom=e,this._opts={}}renderReport(e,t,n){if(!this._dom.rootEl&&t){console.warn("Please adopt the new report API in renderer/api.js.");let i=t.closest(".lh-root");i?this._dom.rootEl=i:(t.classList.add("lh-root","lh-vars"),this._dom.rootEl=t)}else this._dom.rootEl&&t&&(this._dom.rootEl=t);n&&(this._opts=n),this._dom.setLighthouseChannel(e.configSettings.channel||"unknown");let r=k.prepareReportResult(e);return this._dom.rootEl.textContent="",this._dom.rootEl.append(this._renderReport(r)),this._opts.occupyEntireViewport&&this._dom.rootEl.classList.add("lh-max-viewport"),this._dom.rootEl}_renderReportTopbar(e){let t=this._dom.createComponent("topbar"),n=this._dom.find("a.lh-topbar__url",t);return n.textContent=e.finalDisplayedUrl,n.title=e.finalDisplayedUrl,this._dom.safelySetHref(n,e.finalDisplayedUrl),t}_renderReportHeader(){let e=this._dom.createComponent("heading"),t=this._dom.createComponent("scoresWrapper");return this._dom.find(".lh-scores-wrapper-placeholder",e).replaceWith(t),e}_renderReportFooter(e){let t=this._dom.createComponent("footer");return this._renderMetaBlock(e,t),this._dom.find(".lh-footer__version_issue",t).textContent=u.strings.footerIssue,this._dom.find(".lh-footer__version",t).textContent=e.lighthouseVersion,t}_renderMetaBlock(e,t){let n=k.getEmulationDescriptions(e.configSettings||{}),r=e.userAgent.match(/(\w*Chrome\/[\d.]+)/),i=Array.isArray(r)?r[1].replace("/"," ").replace("Chrome","Chromium"):"Chromium",a=e.configSettings.channel,l=e.environment.benchmarkIndex.toFixed(0),s=e.environment.credits?.["axe-core"],c=[`${u.strings.runtimeSettingsBenchmark}: ${l}`,`${u.strings.runtimeSettingsCPUThrottling}: ${n.cpuThrottling}`];n.screenEmulation&&c.push(`${u.strings.runtimeSettingsScreenEmulation}: ${n.screenEmulation}`),s&&c.push(`${u.strings.runtimeSettingsAxeVersion}: ${s}`);let d=u.strings.runtimeAnalysisWindow;e.gatherMode==="timespan"?d=u.strings.runtimeAnalysisWindowTimespan:e.gatherMode==="snapshot"&&(d=u.strings.runtimeAnalysisWindowSnapshot);let h=[["date",`Captured at ${u.i18n.formatDateTime(e.fetchTime)}`],["devices",`${n.deviceEmulation} with Lighthouse ${e.lighthouseVersion}`,c.join(`
`)],["samples-one",u.strings.runtimeSingleLoad,u.strings.runtimeSingleLoadTooltip],["stopwatch",d],["networkspeed",`${n.summary}`,`${u.strings.runtimeSettingsNetworkThrottling}: ${n.networkThrottling}`],["chrome",`Using ${i}`+(a?` with ${a}`:""),`${u.strings.runtimeSettingsUANetwork}: "${e.environment.networkUserAgent}"`]],p=this._dom.find(".lh-meta__items",t);for(let[g,v,_]of h){let m=this._dom.createChildOf(p,"li","lh-meta__item");if(m.textContent=v,_){m.classList.add("lh-tooltip-boundary");let w=this._dom.createChildOf(m,"div","lh-tooltip");w.textContent=_}m.classList.add("lh-report-icon",`lh-report-icon--${g}`)}}_renderReportWarnings(e){if(!e.runWarnings||e.runWarnings.length===0)return this._dom.createElement("div");let t=this._dom.createComponent("warningsToplevel"),n=this._dom.find(".lh-warnings__msg",t);n.textContent=u.strings.toplevelWarningsMessage;let r=[];for(let i of e.runWarnings){let a=this._dom.createElement("li");a.append(this._dom.convertMarkdownLinkSnippets(i)),r.push(a)}return this._dom.find("ul",t).append(...r),t}_renderScoreGauges(e,t,n){let r=[],i=[];for(let a of Object.values(e.categories)){let s=(n[a.id]||t).renderCategoryScore(a,e.categoryGroups||{},{gatherMode:e.gatherMode}),c=this._dom.find("a.lh-gauge__wrapper, a.lh-fraction__wrapper",s);c&&(this._dom.safelySetHref(c,`#${a.id}`),c.addEventListener("click",d=>{if(!c.matches('[href^="#"]'))return;let h=c.getAttribute("href"),p=this._dom.rootEl;if(!h||!p)return;let g=this._dom.find(h,p);d.preventDefault(),g.scrollIntoView()}),this._opts.onPageAnchorRendered?.(c)),k.isPluginCategory(a.id)?i.push(s):r.push(s)}return[...r,...i]}_renderReport(e){u.apply({providedStrings:e.i18n.rendererFormattedStrings,i18n:new te(e.configSettings.locale),reportJson:e});let t=new X(this._dom,{fullPageScreenshot:e.fullPageScreenshot??void 0,entities:e.entities}),n=new G(this._dom,t),r={performance:new ne(this._dom,t)},i=this._dom.createElement("div");i.append(this._renderReportHeader());let a=this._dom.createElement("div","lh-container"),l=this._dom.createElement("div","lh-report");l.append(this._renderReportWarnings(e));let s;Object.keys(e.categories).length===1?i.classList.add("lh-header--solo-category"):s=this._dom.createElement("div","lh-scores-header");let d=this._dom.createElement("div");if(d.classList.add("lh-scorescale-wrap"),d.append(this._dom.createComponent("scorescale")),s){let v=this._dom.find(".lh-scores-container",i);s.append(...this._renderScoreGauges(e,n,r)),v.append(s,d);let _=this._dom.createElement("div","lh-sticky-header");_.append(...this._renderScoreGauges(e,n,r)),a.append(_)}let h=this._dom.createElement("div","lh-categories");l.append(h);let p={gatherMode:e.gatherMode};for(let v of Object.values(e.categories)){let _=r[v.id]||n;_.dom.createChildOf(h,"div","lh-category-wrapper").append(_.render(v,e.categoryGroups,p))}n.injectFinalScreenshot(h,e.audits,d);let g=this._dom.createFragment();return this._opts.omitGlobalStyles||g.append(this._dom.createComponent("styles")),this._opts.omitTopbar||g.append(this._renderReportTopbar(e)),g.append(a),l.append(this._renderReportFooter(e)),a.append(i,l),e.fullPageScreenshot&&V.installFullPageScreenshot(this._dom.rootEl,e.fullPageScreenshot.screenshot),g}};function W(o,e){let t=o.rootEl;typeof e>"u"?t.classList.toggle("lh-dark"):t.classList.toggle("lh-dark",e)}var wt=typeof btoa<"u"?btoa:o=>Buffer.from(o).toString("base64"),yt=typeof atob<"u"?atob:o=>Buffer.from(o,"base64").toString();async function xt(o,e){let t=new TextEncoder().encode(o);if(e.gzip)if(typeof CompressionStream<"u"){let i=new CompressionStream("gzip"),a=i.writable.getWriter();a.write(t),a.close();let l=await new Response(i.readable).arrayBuffer();t=new Uint8Array(l)}else t=window.pako.gzip(o);let n="",r=5e3;for(let i=0;i<t.length;i+=r)n+=String.fromCharCode(...t.subarray(i,i+r));return wt(n)}function kt(o,e){let t=yt(o),n=Uint8Array.from(t,r=>r.charCodeAt(0));return e.gzip?window.pako.ungzip(n,{to:"string"}):new TextDecoder().decode(n)}var Le={toBase64:xt,fromBase64:kt};function ce(){let o=window.location.host.endsWith(".vercel.app"),e=new URLSearchParams(window.location.search).has("dev");return o?`https://${window.location.host}/gh-pages`:e?"http://localhost:7333":"https://googlechrome.github.io/lighthouse"}function de(o){let e=o.generatedTime,t=o.fetchTime||e;return`${o.lighthouseVersion}-${o.finalDisplayedUrl}-${t}`}function Et(o,e,t){let n=new URL(e).origin;window.addEventListener("message",function i(a){a.origin===n&&r&&a.data.opened&&(r.postMessage(o,n),window.removeEventListener("message",i))});let r=window.open(e,t)}async function ze(o,e,t){let n=new URL(e),r=!!window.CompressionStream;n.hash=await Le.toBase64(JSON.stringify(o),{gzip:r}),r&&n.searchParams.set("gzip","1"),window.open(n.toString(),t)}async function Te(o){let e="viewer-"+de(o),t=ce()+"/viewer/";await ze({lhr:o},t,e)}async function Me(o){let e="viewer-"+de(o),t=ce()+"/viewer/";Et({lhr:o},t,e)}function Fe(o){if(!o.audits["script-treemap-data"].details)throw new Error("no script treemap data found");let t={lhr:{mainDocumentUrl:o.mainDocumentUrl,finalUrl:o.finalUrl,finalDisplayedUrl:o.finalDisplayedUrl,audits:{"script-treemap-data":o.audits["script-treemap-data"]},configSettings:{locale:o.configSettings.locale}}},n=ce()+"/treemap/",r="treemap-"+de(o);ze(t,n,r)}var oe=class{constructor(e){this._dom=e,this._toggleEl,this._menuEl,this.onDocumentKeyDown=this.onDocumentKeyDown.bind(this),this.onToggleClick=this.onToggleClick.bind(this),this.onToggleKeydown=this.onToggleKeydown.bind(this),this.onMenuFocusOut=this.onMenuFocusOut.bind(this),this.onMenuKeydown=this.onMenuKeydown.bind(this),this._getNextMenuItem=this._getNextMenuItem.bind(this),this._getNextSelectableNode=this._getNextSelectableNode.bind(this),this._getPreviousMenuItem=this._getPreviousMenuItem.bind(this)}setup(e){this._toggleEl=this._dom.find(".lh-topbar button.lh-tools__button",this._dom.rootEl),this._toggleEl.addEventListener("click",this.onToggleClick),this._toggleEl.addEventListener("keydown",this.onToggleKeydown),this._menuEl=this._dom.find(".lh-topbar div.lh-tools__dropdown",this._dom.rootEl),this._menuEl.addEventListener("keydown",this.onMenuKeydown),this._menuEl.addEventListener("click",e)}close(){this._toggleEl.classList.remove("lh-active"),this._toggleEl.setAttribute("aria-expanded","false"),this._menuEl.contains(this._dom.document().activeElement)&&this._toggleEl.focus(),this._menuEl.removeEventListener("focusout",this.onMenuFocusOut),this._dom.document().removeEventListener("keydown",this.onDocumentKeyDown)}open(e){this._toggleEl.classList.contains("lh-active")?e.focus():this._menuEl.addEventListener("transitionend",()=>{e.focus()},{once:!0}),this._toggleEl.classList.add("lh-active"),this._toggleEl.setAttribute("aria-expanded","true"),this._menuEl.addEventListener("focusout",this.onMenuFocusOut),this._dom.document().addEventListener("keydown",this.onDocumentKeyDown)}onToggleClick(e){e.preventDefault(),e.stopImmediatePropagation(),this._toggleEl.classList.contains("lh-active")?this.close():this.open(this._getNextMenuItem())}onToggleKeydown(e){switch(e.code){case"ArrowUp":e.preventDefault(),this.open(this._getPreviousMenuItem());break;case"ArrowDown":case"Enter":case" ":e.preventDefault(),this.open(this._getNextMenuItem());break;default:}}onMenuKeydown(e){let t=e.target;switch(e.code){case"ArrowUp":e.preventDefault(),this._getPreviousMenuItem(t).focus();break;case"ArrowDown":e.preventDefault(),this._getNextMenuItem(t).focus();break;case"Home":e.preventDefault(),this._getNextMenuItem().focus();break;case"End":e.preventDefault(),this._getPreviousMenuItem().focus();break;default:}}onDocumentKeyDown(e){e.keyCode===27&&this.close()}onMenuFocusOut(e){let t=e.relatedTarget;this._menuEl.contains(t)||this.close()}_getNextSelectableNode(e,t){let n=e.filter(i=>i instanceof HTMLElement).filter(i=>!(i.hasAttribute("disabled")||window.getComputedStyle(i).display==="none")),r=t?n.indexOf(t)+1:0;return r>=n.length&&(r=0),n[r]}_getNextMenuItem(e){let t=Array.from(this._menuEl.childNodes);return this._getNextSelectableNode(t,e)}_getPreviousMenuItem(e){let t=Array.from(this._menuEl.childNodes).reverse();return this._getNextSelectableNode(t,e)}};var ie=class{constructor(e,t){this.lhr,this._reportUIFeatures=e,this._dom=t,this._dropDownMenu=new oe(this._dom),this._copyAttempt=!1,this.topbarEl,this.categoriesEl,this.stickyHeaderEl,this.highlightEl,this.onDropDownMenuClick=this.onDropDownMenuClick.bind(this),this.onKeyUp=this.onKeyUp.bind(this),this.onCopy=this.onCopy.bind(this),this.collapseAllDetails=this.collapseAllDetails.bind(this)}enable(e){this.lhr=e,this._dom.rootEl.addEventListener("keyup",this.onKeyUp),this._dom.document().addEventListener("copy",this.onCopy),this._dropDownMenu.setup(this.onDropDownMenuClick),this._setUpCollapseDetailsAfterPrinting(),this._dom.find(".lh-topbar__logo",this._dom.rootEl).addEventListener("click",()=>W(this._dom)),this._setupStickyHeader()}onDropDownMenuClick(e){e.preventDefault();let t=e.target;if(!(!t||!t.hasAttribute("data-action"))){switch(t.getAttribute("data-action")){case"copy":this.onCopyButtonClick();break;case"print-summary":this.collapseAllDetails(),this._print();break;case"print-expanded":this.expandAllDetails(),this._print();break;case"save-json":{let n=JSON.stringify(this.lhr,null,2);this._reportUIFeatures._saveFile(new Blob([n],{type:"application/json"}));break}case"save-html":{let n=this._reportUIFeatures.getReportHtml();try{this._reportUIFeatures._saveFile(new Blob([n],{type:"text/html"}))}catch(r){this._dom.fireEventOn("lh-log",this._dom.document(),{cmd:"error",msg:"Could not export as HTML. "+r.message})}break}case"open-viewer":{this._dom.isDevTools()?Te(this.lhr):Me(this.lhr);break}case"save-gist":{this._reportUIFeatures.saveAsGist();break}case"toggle-dark":{W(this._dom);break}case"view-unthrottled-trace":this._reportUIFeatures._opts.onViewTrace?.()}this._dropDownMenu.close()}}onCopy(e){this._copyAttempt&&e.clipboardData&&(e.preventDefault(),e.clipboardData.setData("text/plain",JSON.stringify(this.lhr,null,2)),this._dom.fireEventOn("lh-log",this._dom.document(),{cmd:"log",msg:"Report JSON copied to clipboard"})),this._copyAttempt=!1}onCopyButtonClick(){this._dom.fireEventOn("lh-analytics",this._dom.document(),{name:"copy"});try{this._dom.document().queryCommandSupported("copy")&&(this._copyAttempt=!0,this._dom.document().execCommand("copy")||(this._copyAttempt=!1,this._dom.fireEventOn("lh-log",this._dom.document(),{cmd:"warn",msg:"Your browser does not support copy to clipboard."})))}catch(e){this._copyAttempt=!1,this._dom.fireEventOn("lh-log",this._dom.document(),{cmd:"log",msg:e.message})}}onKeyUp(e){(e.ctrlKey||e.metaKey)&&e.keyCode===80&&this._dropDownMenu.close()}expandAllDetails(){this._dom.findAll(".lh-categories details",this._dom.rootEl).map(t=>t.open=!0)}collapseAllDetails(){this._dom.findAll(".lh-categories details",this._dom.rootEl).map(t=>t.open=!1)}_print(){this._reportUIFeatures._opts.onPrintOverride?this._reportUIFeatures._opts.onPrintOverride(this._dom.rootEl):self.print()}resetUIState(){this._dropDownMenu.close()}_getScrollParent(e){let{overflowY:t}=window.getComputedStyle(e);return t!=="visible"&&t!=="hidden"?e:e.parentElement?this._getScrollParent(e.parentElement):document}_setUpCollapseDetailsAfterPrinting(){"onbeforeprint"in self?self.addEventListener("afterprint",this.collapseAllDetails):self.matchMedia("print").addListener(t=>{t.matches?this.expandAllDetails():this.collapseAllDetails()})}_setupStickyHeader(){this.topbarEl=this._dom.find("div.lh-topbar",this._dom.rootEl),this.categoriesEl=this._dom.find("div.lh-categories",this._dom.rootEl),window.requestAnimationFrame(()=>window.requestAnimationFrame(()=>{try{this.stickyHeaderEl=this._dom.find("div.lh-sticky-header",this._dom.rootEl)}catch{return}this.highlightEl=this._dom.createChildOf(this.stickyHeaderEl,"div","lh-highlighter");let e=this._getScrollParent(this._dom.find(".lh-container",this._dom.rootEl));e.addEventListener("scroll",()=>this._updateStickyHeader());let t=e instanceof window.Document?document.documentElement:e;new window.ResizeObserver(()=>this._updateStickyHeader()).observe(t)}))}_updateStickyHeader(){if(!this.stickyHeaderEl)return;let e=this.topbarEl.getBoundingClientRect().bottom,t=this.categoriesEl.getBoundingClientRect().top,n=e>=t,i=Array.from(this._dom.rootEl.querySelectorAll(".lh-category")).filter(h=>h.getBoundingClientRect().top-window.innerHeight/2<0),a=i.length>0?i.length-1:0,l=this.stickyHeaderEl.querySelectorAll(".lh-gauge__wrapper, .lh-fraction__wrapper"),s=l[a],c=l[0].getBoundingClientRect().left,d=s.getBoundingClientRect().left-c;this.highlightEl.style.transform=`translate(${d}px)`,this.stickyHeaderEl.classList.toggle("lh-sticky-header--visible",n)}};function St(o,e){let t=e?new Date(e):new Date,n=t.toLocaleTimeString("en-US",{hour12:!1}),r=t.toLocaleDateString("en-US",{year:"numeric",month:"2-digit",day:"2-digit"}).split("/");r.unshift(r.pop());let i=r.join("-");return`${o}_${i}_${n}`.replace(/[/?<>\\:*|"]/g,"-")}function De(o){let e=new URL(o.finalDisplayedUrl).hostname;return St(e,o.fetchTime)}function Ct(o){return Array.from(o.tBodies[0].rows)}var ae=class{constructor(e,t={}){this.json,this._dom=e,this._opts=t,this._topbar=t.omitTopbar?null:new ie(this,e),this.onMediaQueryChange=this.onMediaQueryChange.bind(this)}initFeatures(e){this.json=e,this._fullPageScreenshot=E.getFullPageScreenshot(e),this._topbar&&(this._topbar.enable(e),this._topbar.resetUIState()),this._setupMediaQueryListeners(),this._setupThirdPartyFilter(),this._setupElementScreenshotOverlay(this._dom.rootEl);let t=this._dom.isDevTools()||this._opts.disableDarkMode||this._opts.disableAutoDarkModeAndFireworks;!t&&window.matchMedia("(prefers-color-scheme: dark)").matches&&W(this._dom,!0);let r=["performance","accessibility","best-practices","seo"].every(s=>{let c=e.categories[s];return c&&c.score===1}),i=this._opts.disableFireworks||this._opts.disableAutoDarkModeAndFireworks;if(r&&!i&&(this._enableFireworks(),t||W(this._dom,!0)),e.categories.performance&&e.categories.performance.auditRefs.some(s=>!!(s.group==="metrics"&&e.audits[s.id].errorMessage))){let s=this._dom.find("input.lh-metrics-toggle__input",this._dom.rootEl);s.checked=!0}this.json.audits["script-treemap-data"]&&this.json.audits["script-treemap-data"].details&&this.addButton({text:u.strings.viewTreemapLabel,icon:"treemap",onClick:()=>Fe(this.json)}),this._opts.onViewTrace&&(e.configSettings.throttlingMethod==="simulate"?this._dom.find('a[data-action="view-unthrottled-trace"]',this._dom.rootEl).classList.remove("lh-hidden"):this.addButton({text:u.strings.viewTraceLabel,onClick:()=>this._opts.onViewTrace?.()})),this._opts.getStandaloneReportHTML&&this._dom.find('a[data-action="save-html"]',this._dom.rootEl).classList.remove("lh-hidden");for(let s of this._dom.findAll("[data-i18n]",this._dom.rootEl)){let d=s.getAttribute("data-i18n");s.textContent=u.strings[d]}}addButton(e){let t=this._dom.rootEl.querySelector(".lh-audit-group--metrics");if(!t)return;let n=t.querySelector(".lh-buttons");n||(n=this._dom.createChildOf(t,"div","lh-buttons"));let r=["lh-button"];e.icon&&(r.push("lh-report-icon"),r.push(`lh-report-icon--${e.icon}`));let i=this._dom.createChildOf(n,"button",r.join(" "));return i.textContent=e.text,i.addEventListener("click",e.onClick),i}resetUIState(){this._topbar&&this._topbar.resetUIState()}getReportHtml(){if(!this._opts.getStandaloneReportHTML)throw new Error("`getStandaloneReportHTML` is not set");return this.resetUIState(),this._opts.getStandaloneReportHTML()}saveAsGist(){throw new Error("Cannot save as gist from base report")}_enableFireworks(){this._dom.find(".lh-scores-container",this._dom.rootEl).classList.add("lh-score100")}_setupMediaQueryListeners(){let e=self.matchMedia("(max-width: 500px)");e.addListener(this.onMediaQueryChange),this.onMediaQueryChange(e)}_resetUIState(){this._topbar&&this._topbar.resetUIState()}onMediaQueryChange(e){this._dom.rootEl.classList.toggle("lh-narrow",e.matches)}_setupThirdPartyFilter(){let e=["uses-rel-preconnect","third-party-facades"],t=["legacy-javascript"];Array.from(this._dom.rootEl.querySelectorAll("table.lh-table")).filter(i=>i.querySelector("td.lh-table-column--url, td.lh-table-column--source-location")).filter(i=>{let a=i.closest(".lh-audit");if(!a)throw new Error(".lh-table not within audit");return!e.includes(a.id)}).forEach(i=>{let a=Ct(i),l=a.filter(m=>!m.classList.contains("lh-sub-item-row")),s=this._getThirdPartyRows(l,E.getFinalDisplayedUrl(this.json)),c=a.some(m=>m.classList.contains("lh-row--even")),d=this._dom.createComponent("3pFilter"),h=this._dom.find("input",d);h.addEventListener("change",m=>{let w=m.target instanceof HTMLInputElement&&!m.target.checked,f=!0,b=l[0];for(;b;){let y=w&&s.includes(b);do b.classList.toggle("lh-row--hidden",y),c&&(b.classList.toggle("lh-row--even",!y&&f),b.classList.toggle("lh-row--odd",!y&&!f)),b=b.nextElementSibling;while(b&&b.classList.contains("lh-sub-item-row"));y||(f=!f)}});let p=s.filter(m=>!m.classList.contains("lh-row--group")).length;this._dom.find(".lh-3p-filter-count",d).textContent=`${p}`,this._dom.find(".lh-3p-ui-string",d).textContent=u.strings.thirdPartyResourcesLabel;let g=s.length===l.length,v=!s.length;if((g||v)&&(this._dom.find("div.lh-3p-filter",d).hidden=!0),!i.parentNode)return;i.parentNode.insertBefore(d,i);let _=i.closest(".lh-audit");if(!_)throw new Error(".lh-table not within audit");t.includes(_.id)&&!g&&h.click()})}_setupElementScreenshotOverlay(e){this._fullPageScreenshot&&V.installOverlayFeature({dom:this._dom,rootEl:e,overlayContainerEl:e,fullPageScreenshot:this._fullPageScreenshot})}_getThirdPartyRows(e,t){let n=E.getEntityFromUrl(t,this.json.entities),r=this.json.entities?.find(a=>a.isFirstParty===!0)?.name,i=[];for(let a of e){if(r){if(!a.dataset.entity||a.dataset.entity===r)continue}else{let l=a.querySelector("div.lh-text__url");if(!l)continue;let s=l.dataset.url;if(!s||!(E.getEntityFromUrl(s,this.json.entities)!==n))continue}i.push(a)}return i}_saveFile(e){let t=e.type.match("json")?".json":".html",n=De({finalDisplayedUrl:E.getFinalDisplayedUrl(this.json),fetchTime:this.json.fetchTime})+t;this._opts.onSaveFileOverride?this._opts.onSaveFileOverride(e,n):this._dom.saveFile(e,n)}};function Ie(o,e={}){let t=document.createElement("article");t.classList.add("lh-root","lh-vars");let n=new ee(t.ownerDocument,t);return new re(n).renderReport(o,t,e),new ae(n,e).initFeatures(o),t}var le=class{constructor(e){this.el=e;let t=document.createElement("style");if(t.textContent=`
      #lh-log {
        position: fixed;
        background-color: #323232;
        color: #fff;
        min-height: 48px;
        min-width: 288px;
        padding: 16px 24px;
        box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.26);
        border-radius: 2px;
        margin: 12px;
        font-size: 14px;
        cursor: default;
        transition: transform 0.3s, opacity 0.3s;
        transform: translateY(100px);
        opacity: 0;
        bottom: 0;
        left: 0;
        z-index: 3;
        display: flex;
        flex-direction: row;
        justify-content: center;
        align-items: center;
      }
      
      #lh-log.lh-show {
        opacity: 1;
        transform: translateY(0);
      }
    `,!this.el.parentNode)throw new Error("element needs to be in the DOM");this.el.parentNode.insertBefore(t,this.el),this._id=void 0}log(e,t=!0){this._id&&clearTimeout(this._id),this.el.textContent=e,this.el.classList.add("lh-show"),t&&(this._id=setTimeout(()=>{this.el.classList.remove("lh-show")},7e3))}warn(e){this.log("Warning: "+e)}error(e){this.log(e),setTimeout(()=>{throw new Error(e)},0)}hide(){this._id&&clearTimeout(this._id),this.el.classList.remove("lh-show")}};function At(){let o=window.__LIGHTHOUSE_JSON__,e=Ie(o,{occupyEntireViewport:!0,getStandaloneReportHTML(){return document.documentElement.outerHTML}});document.body.append(e),document.addEventListener("lh-analytics",t=>{let n=t;"gtag"in window&&window.gtag("event",n.detail.name,n.detail.data??{})}),document.addEventListener("lh-log",t=>{let n=document.querySelector("div#lh-log");if(!n)return;let r=new le(n),i=t.detail;switch(i.cmd){case"log":r.log(i.msg);break;case"warn":r.warn(i.msg);break;case"error":r.error(i.msg);break;case"hide":r.hide();break}})}window.__initLighthouseReport__=At;})();
/**
 * @license
 * Copyright 2017 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */
/**
 * @license
 * Copyright 2023 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */
/**
 * @license
 * Copyright 2020 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */
/**
 * @license Copyright 2023 The Lighthouse Authors. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use this file except in compliance with the License. You may obtain a copy of the License at http://www.apache.org/licenses/LICENSE-2.0
 * Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the specific language governing permissions and limitations under the License.
*/
/**
 * @license
 * Copyright 2018 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */
/**
 * @license
 * Copyright 2017 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 *
 * Dummy text for ensuring report robustness: <\/script> pre$`post %%LIGHTHOUSE_JSON%%
 * (this is handled by terser)
 */
/**
 * @license
 * Copyright 2021 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

  __initLighthouseReport__();
  //# sourceURL=compiled-reportrenderer.js
  </script>
  <script>console.log('window.__LIGHTHOUSE_JSON__', __LIGHTHOUSE_JSON__);</script>
</body>
</html>
