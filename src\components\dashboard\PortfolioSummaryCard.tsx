"use client";

import React, { useMemo } from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Progress } from "@/components/ui/progress";
import { 
  DollarSign, 
  TrendingUp, 
  TrendingDown, 
  Wallet,
  PieChart,
  Activity,
  Zap,
  Shield,
  Target
} from "lucide-react";
import { cn } from "@/lib/utils";

interface PortfolioSummaryCardProps {
  totalPortfolioValueUSDT: number;
  totalSpotValueUSDT: number;
  totalFuturesValueUSDT: number;
  spotPnl24hUSDT: number;
  spotPnl24hPercentage: number;
  totalUnrealizedFuturesPnl: number | null;
  isLoading?: boolean;
}

// Componente otimizado com React.memo
export const PortfolioSummaryCard = React.memo<PortfolioSummaryCardProps>(({
  totalPortfolioValueUSDT,
  totalSpotValueUSDT,
  totalFuturesValueUSDT,
  spotPnl24hUSDT,
  spotPnl24hPercentage,
  totalUnrealizedFuturesPnl,
  isLoading = false
}) => {
  // Memoizar funções de formatação
  const formatCurrency = useMemo(() => 
    new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }), []
  );

  const formatPercentage = useMemo(() => {
    const fn = (value: number): string => {
      return `${value >= 0 ? '+' : ''}${value.toFixed(2)}%`;
    };
    fn.displayName = 'formatPercentage';
    return fn;
  }, []);

  const getPnlColor = useMemo(() => {
    const fn = (value: number) => {
      if (value > 0) return "profit-text";
      if (value < 0) return "loss-text";
      return "neutral-text";
    };
    fn.displayName = 'getPnlColor';
    return fn;
  }, []);

  const getPnlIcon = useMemo(() => {
    const fn = (value: number) => {
      if (value > 0) return <TrendingUp className="h-4 w-4" />;
      if (value < 0) return <TrendingDown className="h-4 w-4" />;
      return <Activity className="h-4 w-4" />;
    };
    fn.displayName = 'getPnlIcon';
    return fn;
  }, []);

  // Memoizar cálculos pesados
  const calculations = useMemo(() => {
    const spotPercentage = totalPortfolioValueUSDT > 0 
      ? (totalSpotValueUSDT / totalPortfolioValueUSDT) * 100 
      : 0;
    
    const futuresPercentage = totalPortfolioValueUSDT > 0 
      ? (totalFuturesValueUSDT / totalPortfolioValueUSDT) * 100 
      : 0;

    const totalPnl = spotPnl24hUSDT + (totalUnrealizedFuturesPnl || 0);
    const totalPnlPercentage = totalPortfolioValueUSDT > 0 
      ? (totalPnl / totalPortfolioValueUSDT) * 100 
      : 0;

    return {
      spotPercentage,
      futuresPercentage,
      totalPnl,
      totalPnlPercentage,
    };
  }, [totalPortfolioValueUSDT, totalSpotValueUSDT, totalFuturesValueUSDT, spotPnl24hUSDT, totalUnrealizedFuturesPnl]);

  // Component para valores sem animações - apenas mostra dados
  const StaticValue = useMemo(() => {
    const Component = ({ value, className = "" }: { 
      value: string, 
      className?: string 
    }) => (
      <span className={className}>
        {value}
      </span>
    );
    Component.displayName = 'StaticValue';
    return Component;
  }, []);

  return (
    <Card className="trading-card overflow-hidden critical-layer">
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2 text-lg font-semibold">
            <Wallet className="h-5 w-5 text-primary" />
            Portfolio Overview
          </CardTitle>
          <Badge 
            variant="outline" 
            className={cn(
              "flex items-center gap-1",
              getPnlColor(calculations.totalPnl)
            )}
          >
            {getPnlIcon(calculations.totalPnl)}
            <StaticValue 
              value={formatPercentage(calculations.totalPnlPercentage)}
            />
          </Badge>
        </div>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* Total Portfolio Value */}
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium text-muted-foreground">Total Value</span>
            <div className="flex items-center gap-1">
              <Shield className="h-3 w-3 text-green-400" />
              <span className="text-xs text-green-400">
                Secured
              </span>
            </div>
          </div>
          <div className="flex items-baseline gap-2">
            <StaticValue 
              value={formatCurrency.format(totalPortfolioValueUSDT)}
              className="text-3xl font-bold tracking-tight"
            />
            <div className={cn("flex items-center gap-1 text-sm", getPnlColor(calculations.totalPnl))}>
              {getPnlIcon(calculations.totalPnl)}
              <StaticValue 
                value={formatCurrency.format(Math.abs(calculations.totalPnl))}
              />
            </div>
          </div>
        </div>

        <Separator />

        {/* Portfolio Distribution */}
        <div className="space-y-4">
          <div className="flex items-center gap-2">
            <PieChart className="h-4 w-4 text-muted-foreground" />
            <span className="text-sm font-medium">Portfolio Distribution</span>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Spot Trading */}
            <div className="space-y-3 p-4 rounded-lg bg-muted/30 border hover:bg-muted/40 transform-gpu">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 rounded-full bg-blue-400"></div>
                  <span className="text-sm font-medium">Spot</span>
                </div>
                <Badge variant="secondary" className="text-xs">
                  {calculations.spotPercentage.toFixed(1)}%
                </Badge>
              </div>
              <div className="space-y-1">
                <StaticValue 
                  value={formatCurrency.format(totalSpotValueUSDT)}
                  className="text-lg font-semibold block"
                />
                <Progress 
                  value={calculations.spotPercentage} 
                  className="h-2"
                />
              </div>
              <div className={cn("flex items-center gap-1 text-xs", getPnlColor(spotPnl24hUSDT))}>
                {getPnlIcon(spotPnl24hUSDT)}
                <StaticValue 
                  value={`24h: ${formatCurrency.format(spotPnl24hUSDT)} (${formatPercentage(spotPnl24hPercentage)})`}
                />
              </div>
            </div>

            {/* Futures Trading */}
            <div className="space-y-3 p-4 rounded-lg bg-muted/30 border hover:bg-muted/40 transform-gpu">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 rounded-full bg-purple-400"></div>
                  <span className="text-sm font-medium">Futures</span>
                </div>
                <Badge variant="secondary" className="text-xs">
                  {calculations.futuresPercentage.toFixed(1)}%
                </Badge>
              </div>
              <div className="space-y-1">
                <StaticValue 
                  value={formatCurrency.format(totalFuturesValueUSDT)}
                  className="text-lg font-semibold block"
                />
                <Progress 
                  value={calculations.futuresPercentage} 
                  className="h-2"
                />
              </div>
              <div className={cn("flex items-center gap-1 text-xs", getPnlColor(totalUnrealizedFuturesPnl || 0))}>
                {getPnlIcon(totalUnrealizedFuturesPnl || 0)}
                <StaticValue 
                  value={`PnL: ${formatCurrency.format(totalUnrealizedFuturesPnl || 0)}`}
                />
              </div>
            </div>
          </div>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 pt-4 border-t">
          <div className="text-center space-y-1">
            <div className="flex items-center justify-center">
              <Target className="h-4 w-4 text-blue-400" />
            </div>
            <StaticValue 
              value={calculations.spotPercentage.toFixed(1)}
              className="text-sm font-semibold"
            />
            <p className="text-xs text-muted-foreground">Spot %</p>
          </div>
          
          <div className="text-center space-y-1">
            <div className="flex items-center justify-center">
              <Zap className="h-4 w-4 text-purple-400" />
            </div>
            <StaticValue 
              value={calculations.futuresPercentage.toFixed(1)}
              className="text-sm font-semibold"
            />
            <p className="text-xs text-muted-foreground">Futures %</p>
          </div>
          
          <div className="text-center space-y-1">
            <div className="flex items-center justify-center">
              <TrendingUp className="h-4 w-4 text-green-400" />
            </div>
            <StaticValue 
              value={formatPercentage(calculations.totalPnlPercentage)}
              className={cn("text-sm font-semibold", getPnlColor(calculations.totalPnl))}
            />
            <p className="text-xs text-muted-foreground">Total PnL</p>
          </div>
          
          <div className="text-center space-y-1">
            <div className="flex items-center justify-center">
              <DollarSign className="h-4 w-4 text-yellow-400" />
            </div>
            <StaticValue 
              value={(totalPortfolioValueUSDT / 1000).toFixed(1)}
              className="text-sm font-semibold"
            />
            <p className="text-xs text-muted-foreground">Total ($K)</p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
});

PortfolioSummaryCard.displayName = 'PortfolioSummaryCard';
