"use client";

import { useEffect, useState, useRef, useCallback } from "react";
import dynamic from "next/dynamic";
import { PortfolioSummaryCard } from "@/components/dashboard/PortfolioSummaryCard";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Separator } from "@/components/ui/separator";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import type { PortfolioAsset, Transaction, OpenPosition, PriceAlert } from "@/lib/types";
import { 
  DollarSign, 
  TrendingUp, 
  ListCollapse, 
  Activity, 
  AlertTriangle, 
  Loader2, 
  Info, 
  Briefcase, 
  BellRing, 
  Wallet, 
  ServerCrash, 
  RefreshCw,
  BarChart3,
  LineChart,
  PieChart,
  Target,
  Zap,
  Shield,
  Clock,
  Users,
  Globe,
  Settings
} from "lucide-react";
import Link from "next/link";
import { toast } from "@/hooks/use-toast";
import { cn } from "@/lib/utils";

// Dynamic imports para lazy loading de componentes pesados
const AssetTable = dynamic(() => import("@/components/dashboard/AssetTable").then(mod => ({ default: mod.AssetTable })), {
  loading: () => <div className="flex justify-center items-center py-10"><Loader2 className="h-8 w-8 animate-spin text-primary"/></div>,
  ssr: false
});

const TransactionHistoryTable = dynamic(() => import("@/components/dashboard/TransactionHistoryTable").then(mod => ({ default: mod.TransactionHistoryTable })), {
  loading: () => <div className="flex justify-center items-center py-10"><Loader2 className="h-8 w-8 animate-spin text-primary"/></div>,
  ssr: false
});

const OpenPositionsTable = dynamic(() => import("@/components/dashboard/OpenPositionsTable").then(mod => ({ default: mod.OpenPositionsTable })), {
  loading: () => <div className="flex justify-center items-center py-10"><Loader2 className="h-8 w-8 animate-spin text-primary"/></div>,
  ssr: false
});

const FuturesTradingTerminal = dynamic(() => import("@/components/trading/FuturesTradingTerminal").then(mod => ({ default: mod.FuturesTradingTerminal })), {
  loading: () => <div className="flex justify-center items-center py-10"><Loader2 className="h-8 w-8 animate-spin text-primary"/><span className="ml-2">Loading Trading Terminal...</span></div>,
  ssr: false
});

const CreateAlertForm = dynamic(() => import("@/components/alerts/CreateAlertForm").then(mod => ({ default: mod.CreateAlertForm })), {
  loading: () => <div className="flex justify-center items-center py-4"><Loader2 className="h-6 w-6 animate-spin text-primary"/></div>,
  ssr: false
});

const ActiveAlertsList = dynamic(() => import("@/components/alerts/ActiveAlertsList").then(mod => ({ default: mod.ActiveAlertsList })), {
  loading: () => <div className="flex justify-center items-center py-4"><Loader2 className="h-6 w-6 animate-spin text-primary"/></div>,
  ssr: false
});

interface AccountOverviewData {
  totalPortfolioValueUSDT: number;
  totalSpotValueUSDT: number;
  totalFuturesValueUSDT: number;
  spotPnl24hUSDT: number;
  spotPnl24hPercentage: number;
  assets: PortfolioAsset[];
  allPrices?: Record<string, string>;
}

interface CryptoInfo {
  symbol: string;
  name: string;
}

const DASHBOARD_REFRESH_INTERVAL = 30000; // Refresh main dashboard data every 30 seconds

// Loading component for skeleton states
const DashboardSkeleton = () => (
  <div className="space-y-6">
    <div className="flex justify-between items-center">
      <div className="h-8 bg-muted rounded w-48 animate-pulse"></div>
      <div className="h-10 bg-muted rounded w-32 animate-pulse"></div>
    </div>
    
    <div className="h-96 bg-muted rounded animate-pulse"></div>
    
    <div className="grid gap-4 md:grid-cols-2">
      <div className="h-64 bg-muted rounded animate-pulse"></div>
      <div className="h-64 bg-muted rounded animate-pulse"></div>
    </div>
  </div>
);

// Error state component
const ErrorState = ({ error, onRetry }: { error: string; onRetry: () => void }) => (
  <Card className="alert-error">
    <CardHeader>
      <CardTitle className="flex items-center gap-2">
        <AlertTriangle className="h-5 w-5" />
        Connection Error
      </CardTitle>
    </CardHeader>
    <CardContent className="space-y-4">
      <p className="text-sm">{error}</p>
      <div className="flex gap-2">
        <Button onClick={onRetry} size="sm">
          <RefreshCw className="h-4 w-4 mr-2" />
          Retry
        </Button>
        <Button variant="outline" size="sm" asChild>
          <Link href="/settings">Settings</Link>
        </Button>
      </div>
    </CardContent>
  </Card>
);

export default function DashboardPage() {
  const [apiKey, setApiKey] = useState<string | null>(null);
  const [apiSecret, setApiSecret] = useState<string | null>(null);
  const [isKeysLoading, setIsKeysLoading] = useState(true);

  const [accountOverview, setAccountOverview] = useState<AccountOverviewData | null>(null);
  const [isLoadingAccountOverview, setIsLoadingAccountOverview] = useState(true);
  const [accountOverviewError, setAccountOverviewError] = useState<string | null>(null);

  const [openPositions, setOpenPositions] = useState<OpenPosition[]>([]);
  const [totalUnrealizedFuturesPnl, setTotalUnrealizedFuturesPnl] = useState<number | null>(null);
  const [isLoadingOpenPositions, setIsLoadingOpenPositions] = useState(true);
  const [openPositionsError, setOpenPositionsError] = useState<string | null>(null);

  const [recentTransactions, setRecentTransactions] = useState<Transaction[]>([]);
  const [isLoadingTransactions, setIsLoadingTransactions] = useState(true);
  const [transactionsError, setTransactionsError] = useState<string | null>(null);

  const [alerts, setAlerts] = useState<PriceAlert[]>([]);
  const [cryptocurrencies, setCryptocurrencies] = useState<CryptoInfo[]>([]);
  const [isLoadingCryptos, setIsLoadingCryptos] = useState(true);
  const [errorCryptos, setErrorCryptos] = useState<string | null>(null);
  const [activeAlertsCount, setActiveAlertsCount] = useState<number>(0);
  const alertsSectionRef = useRef<HTMLDivElement>(null);
  const [currentPrices, setCurrentPrices] = useState<Record<string, string>>({});
  const [isBackgroundRefreshing, setIsBackgroundRefreshing] = useState(false);

  const dashboardRefreshIntervalRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    const storedApiKey = localStorage.getItem("binanceApiKey");
    const storedApiSecret = localStorage.getItem("binanceApiSecret");
    setApiKey(storedApiKey);
    setApiSecret(storedApiSecret);
    setIsKeysLoading(false);
  }, []);

  const fetchAccountOverview = useCallback(async (isInitialLoad = false) => {
    if (!apiKey || !apiSecret) {
      if (!isKeysLoading) setAccountOverviewError("Chaves de API da Binance não configuradas.");
      setIsLoadingAccountOverview(false);
      return;
    }
    if (isInitialLoad || !accountOverview) setIsLoadingAccountOverview(true);
    setAccountOverviewError(null);

    try {
      const response = await fetch('/api/binance/account-overview', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ apiKey, apiSecret }),
      });
       if (!response.ok) {
        let serverMessage = `HTTP error! status: ${response.status}`;
        try {
          const errorResult = await response.json();
          if (errorResult && errorResult.message) {
            serverMessage = errorResult.message;
          }
        } catch (e) {
          serverMessage = response.statusText || serverMessage;
        }
        throw new Error(serverMessage);
      }
      const result = await response.json();
      if (result.success && result.data && result.data.accountOverviewData) {
        setAccountOverview(result.data.accountOverviewData);
        if (result.data.accountOverviewData.allPrices) {
          setCurrentPrices(result.data.accountOverviewData.allPrices);
        }
        setAccountOverviewError(null);
      } else {
        setAccountOverviewError(result.message || `Falha ao buscar dados da visão geral da conta.`);
        setCurrentPrices({});
      }
    } catch (err: any) {
      setAccountOverviewError(`Erro ao buscar visão geral: ${err.message}`);
      setCurrentPrices({});
      console.error(`Error fetching account overview:`, err);
    } finally {
      setIsLoadingAccountOverview(false);
    }
  }, [apiKey, apiSecret, isKeysLoading, accountOverview]);

  const fetchOpenPositions = useCallback(async (isInitialLoad = false) => {
    if (!apiKey || !apiSecret) {
        if (!isKeysLoading) setOpenPositionsError("Chaves de API da Binance não configuradas.");
        if (openPositions.length === 0) setIsLoadingOpenPositions(false);
        return;
    }
    if (isInitialLoad || openPositions.length === 0) setIsLoadingOpenPositions(true);
    setOpenPositionsError(null);
    try {
        const response = await fetch('/api/binance/futures-positions', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ apiKey, apiSecret }),
        });
        if (!response.ok) {
          let serverMessage = `HTTP error! status: ${response.status}`;
          try {
            const errorResult = await response.json();
            if (errorResult && errorResult.message) {
              serverMessage = errorResult.message;
            }
          } catch (e) {
            serverMessage = response.statusText || serverMessage;
          }
          throw new Error(serverMessage);
        }
        const result = await response.json();
        if (result.success && result.data) {
            setOpenPositions(result.data.positions || []);
            setTotalUnrealizedFuturesPnl(result.data.totalUnrealizedFuturesPnlUSDT !== undefined ? result.data.totalUnrealizedFuturesPnlUSDT : 0);
            setOpenPositionsError(null);
        } else {
            setOpenPositionsError(result.message || `Falha ao buscar posições de futuros.`);
            setOpenPositions([]);
            setTotalUnrealizedFuturesPnl(0);
        }
    } catch (err: any) {
        setOpenPositionsError(`Erro ao buscar posições: ${err.message}`);
        setOpenPositions([]);
        setTotalUnrealizedFuturesPnl(0);
        console.error(`Error fetching open positions:`, err);
    } finally {
       setIsLoadingOpenPositions(false);
    }
  }, [apiKey, apiSecret, isKeysLoading, openPositions]);

  const fetchRecentTransactions = useCallback(async (isInitialLoad = false) => {
     if (!apiKey || !apiSecret) {
      if (!isKeysLoading) setTransactionsError("Chaves de API da Binance não configuradas.");
      if (recentTransactions.length === 0) setIsLoadingTransactions(false);
      return;
    }
    if (isInitialLoad || recentTransactions.length === 0) setIsLoadingTransactions(true);
    setTransactionsError(null);
    try {
      const response = await fetch('/api/binance/recent-trades', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ apiKey, apiSecret }),
      });
      if (!response.ok) {
        let serverMessage = `HTTP error! status: ${response.status}`;
        try {
          const errorResult = await response.json();
          if (errorResult && errorResult.message) {
            serverMessage = errorResult.message;
          }
        } catch (e) {
          serverMessage = response.statusText || serverMessage;
        }
        throw new Error(serverMessage);
      }
      const result = await response.json();
      if (result.success && result.data && result.data.transactions) {
        setRecentTransactions(result.data.transactions);
        setTransactionsError(null);
      } else {
        setTransactionsError(result.message || `Falha ao buscar transações recentes (dados inválidos).`);
        setRecentTransactions([]);
      }
    } catch (err: any) {
      setTransactionsError(`Erro ao buscar transações recentes: ${err.message}`);
      setRecentTransactions([]);
      console.error(`Error fetching recent transactions:`, err);
    } finally {
      setIsLoadingTransactions(false);
    }
  }, [apiKey, apiSecret, isKeysLoading, recentTransactions]);

  const fetchCryptos = async () => {
    try {
      setIsLoadingCryptos(true);
      setErrorCryptos(null);
      const response = await fetch('/api/binance/exchange-info');
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      const result = await response.json();
      if (result.success && Array.isArray(result.data)) {
        const formattedCryptos = result.data.map((symbol: any) => ({
          symbol: symbol.symbol,
          name: symbol.baseAsset === 'BTC' ? 'Bitcoin' : 
                symbol.baseAsset === 'ETH' ? 'Ethereum' :
                symbol.baseAsset === 'ADA' ? 'Cardano' :
                symbol.baseAsset === 'SOL' ? 'Solana' :
                symbol.baseAsset,
        }));
        setCryptocurrencies(formattedCryptos);
        setErrorCryptos(null);
      } else {
        setErrorCryptos('Formato de resposta inválido da API.');
        setCryptocurrencies([]);
      }
    } catch (err: any) {
      setErrorCryptos(`Erro ao buscar criptomoedas: ${err.message}`);
      setCryptocurrencies([]);
      console.error('Error fetching cryptos:', err);
    } finally {
      setIsLoadingCryptos(false);
    }
  };

  // Load alerts from localStorage and fetch cryptos
  useEffect(() => {
    const storedAlerts = localStorage.getItem('price-alerts');
    if (storedAlerts) {
      try {
        const parsedAlerts = JSON.parse(storedAlerts);
        setAlerts(Array.isArray(parsedAlerts) ? parsedAlerts : []);
      } catch (error) {
        console.error('Error parsing stored alerts:', error);
        setAlerts([]);
      }
    }
    fetchCryptos();
  }, []);

  // Update active alerts count
  useEffect(() => {
    const activeCount = alerts.filter(alert => alert.status === 'active').length;
    setActiveAlertsCount(activeCount);
  }, [alerts]);

  // Start dashboard refresh interval
  useEffect(() => {
    if (apiKey && apiSecret) {
      // Initial load with loading states
      fetchAccountOverview(true);
      fetchOpenPositions(true);
      fetchRecentTransactions(true);

      // Set up interval for background updates (no loading states)
      dashboardRefreshIntervalRef.current = setInterval(async () => {
        setIsBackgroundRefreshing(true);
        try {
          await Promise.all([
            fetchAccountOverview(false),
            fetchOpenPositions(false),
            fetchRecentTransactions(false)
          ]);
        } finally {
          setTimeout(() => setIsBackgroundRefreshing(false), 1000); // Show indicator for 1 second
        }
      }, DASHBOARD_REFRESH_INTERVAL);
    }

    return () => {
      if (dashboardRefreshIntervalRef.current) {
        clearInterval(dashboardRefreshIntervalRef.current);
      }
    };
  }, [apiKey, apiSecret]); // eslint-disable-line react-hooks/exhaustive-deps

  const handleAlertCreated = (newAlertData: { cryptoSymbol: string, condition: 'above' | 'below', targetPrice: number }) => {
    const cryptoDetail = cryptocurrencies.find(c => c.symbol === newAlertData.cryptoSymbol);
    const newAlert: PriceAlert = {
      id: Date.now().toString(),
      cryptoSymbol: newAlertData.cryptoSymbol,
      cryptoName: cryptoDetail?.name || newAlertData.cryptoSymbol,
      condition: newAlertData.condition,
      targetPrice: newAlertData.targetPrice,
      status: 'active',
      createdAt: new Date().toISOString(),
      triggeredAt: null,
      triggeredPrice: null,
    };

    const updatedAlerts = [newAlert, ...alerts].sort((a, b) => b.createdAt.localeCompare(a.createdAt));
    setAlerts(updatedAlerts);
    localStorage.setItem('price-alerts', JSON.stringify(updatedAlerts));

    toast({
      title: "Alerta Criado",
      description: `Alerta criado para ${newAlertData.cryptoSymbol} ${newAlertData.condition === 'above' ? 'acima de' : 'abaixo de'} $${newAlertData.targetPrice.toFixed(2)}.`,
    });
  };

  const handleDeleteAlert = (alertId: string) => {
    const updatedAlerts = alerts.filter(alert => alert.id !== alertId).sort((a, b) => b.createdAt.localeCompare(a.createdAt));
    setAlerts(updatedAlerts);
    localStorage.setItem('price-alerts', JSON.stringify(updatedAlerts));

    toast({
      title: "Alerta Excluído",
      description: "O alerta de preço foi removido.",
    });
  };

  const handleToggleAlertStatus = (alertId: string, currentStatus: PriceAlert['status']) => {
    const newStatus: PriceAlert['status'] = (currentStatus === 'active' || currentStatus === 'triggered') ? 'cancelled' : 'active';
    const updatedAlerts = alerts.map(alert => {
        if (alert.id === alertId) {
          return {
            ...alert,
            status: newStatus,
            triggeredAt: (newStatus === 'active') ? null : alert.triggeredAt,
            triggeredPrice: (newStatus === 'active') ? null : alert.triggeredPrice,
          };
        }
        return alert;
      }).sort((a, b) => b.createdAt.localeCompare(a.createdAt));
    setAlerts(updatedAlerts);
    localStorage.setItem('price-alerts', JSON.stringify(updatedAlerts));
    toast({
      title: `Alerta ${currentStatus === 'active' || currentStatus === 'triggered' ? 'Desativado' : 'Ativado'}`,
      description: `O status do alerta de preço foi atualizado.`,
    });
  };

  const handleScrollToAlerts = () => {
    alertsSectionRef.current?.scrollIntoView({ behavior: 'smooth', block: 'start' });
  };

  // Calculate derived state before any early returns
  const isAnyDataLoading = isLoadingAccountOverview || isLoadingOpenPositions || isLoadingTransactions;
  const hasAnyError = accountOverviewError || openPositionsError || transactionsError;

  const handleManualRefresh = useCallback(() => {
    if (apiKey && apiSecret) {
        toast({ title: "Atualizando Dados...", description: "Buscando as informações mais recentes da Binance.", duration: 2000});
        
        // Manual refresh shows loading states
        fetchAccountOverview(true);
        fetchOpenPositions(true);
        fetchRecentTransactions(true);
    } else {
        toast({ title: "Chaves de API Ausentes", description: "Configure suas chaves de API para atualizar os dados.", variant: "destructive"});
    }
  }, [apiKey, apiSecret, fetchAccountOverview, fetchOpenPositions, fetchRecentTransactions]);

  if (isKeysLoading) {
    return <DashboardSkeleton />;
  }

  if (!apiKey || !apiSecret) {
    return (
      <div className="flex flex-col items-center justify-center h-full text-center space-y-6 p-8">
        <div className="relative">
          <AlertTriangle className="h-20 w-20 text-amber-500 animate-pulse" />
          <div className="absolute -top-2 -right-2 h-6 w-6 bg-red-500 rounded-full flex items-center justify-center">
            <span className="text-xs text-white font-bold">!</span>
          </div>
        </div>
        <div className="space-y-2">
          <h2 className="text-3xl font-bold">API Configuration Required</h2>
          <p className="text-muted-foreground max-w-md">
            Please configure your Binance API keys to access your dashboard and start trading.
          </p>
        </div>
        <div className="flex gap-3">
          <Button asChild size="lg" className="btn-trading">
            <Link href="/settings">
              <Settings className="h-4 w-4 mr-2" />
              Configure API
            </Link>
          </Button>
          <Button variant="outline" size="lg" asChild>
            <Link href="/onboarding">Learn More</Link>
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4 md:space-y-6 px-2 sm:px-4 lg:px-0" key="dashboard-content-wrapper">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div className="space-y-1 min-w-0 flex-1">
          <h1 className="text-2xl sm:text-3xl lg:text-4xl font-bold tracking-tight gradient-primary bg-clip-text text-transparent">
            Trading Dashboard
          </h1>
          <p className="text-muted-foreground text-sm sm:text-base">
            Real-time portfolio overview and trading insights
          </p>
        </div>
        <div className="flex items-center gap-2 flex-shrink-0">
          <Badge variant="outline" className="flex items-center gap-1 text-xs sm:text-sm">
            <div className={cn("status-dot", isBackgroundRefreshing ? "animate-pulse bg-amber-400" : "status-online")}></div>
            <span className="hidden sm:inline">{isBackgroundRefreshing ? "Updating..." : "Live Data"}</span>
            <span className="sm:hidden">{isBackgroundRefreshing ? "..." : "●"}</span>
          </Badge>
          <Button 
            onClick={handleManualRefresh} 
            variant="outline" 
            size="sm" 
            disabled={isAnyDataLoading}
            className="btn-trading text-xs sm:text-sm"
          >
            <RefreshCw className={cn("h-3 w-3 sm:h-4 sm:w-4", isAnyDataLoading ? "animate-spin" : "", "sm:mr-2")} />
            <span className="hidden sm:inline">Refresh</span>
          </Button>
        </div>
      </div>

      {/* Error State */}
      {hasAnyError && (
        <ErrorState 
          error="Failed to load some data. Please check your API configuration." 
          onRetry={handleManualRefresh}
        />
      )}

      {/* Portfolio Summary */}
      {accountOverview && (
        <PortfolioSummaryCard
          totalPortfolioValueUSDT={accountOverview.totalPortfolioValueUSDT}
          totalSpotValueUSDT={accountOverview.totalSpotValueUSDT}
          totalFuturesValueUSDT={accountOverview.totalFuturesValueUSDT}
          spotPnl24hUSDT={accountOverview.spotPnl24hUSDT}
          spotPnl24hPercentage={accountOverview.spotPnl24hPercentage}
          totalUnrealizedFuturesPnl={totalUnrealizedFuturesPnl}
          isLoading={isLoadingAccountOverview}
        />
      )}

      {/* Main Content Tabs */}
      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList className="grid w-full grid-cols-2 sm:grid-cols-4 h-auto transform-gpu">
          <TabsTrigger value="overview" className="flex items-center gap-1 sm:gap-2 text-xs sm:text-sm py-2">
            <BarChart3 className="h-3 w-3 sm:h-4 sm:w-4" />
            <span className="hidden sm:inline">Overview</span>
            <span className="sm:hidden">View</span>
          </TabsTrigger>
          <TabsTrigger value="positions" className="flex items-center gap-1 sm:gap-2 text-xs sm:text-sm py-2">
            <Target className="h-3 w-3 sm:h-4 sm:w-4" />
            <span className="hidden sm:inline">Positions</span>
            <span className="sm:hidden">Pos</span>
          </TabsTrigger>
          <TabsTrigger value="trading" className="flex items-center gap-1 sm:gap-2 text-xs sm:text-sm py-2">
            <Zap className="h-3 w-3 sm:h-4 sm:w-4" />
            <span className="hidden sm:inline">Trading</span>
            <span className="sm:hidden">Trade</span>
          </TabsTrigger>
          <TabsTrigger value="alerts" className="flex items-center gap-1 sm:gap-2 text-xs sm:text-sm py-2">
            <BellRing className="h-3 w-3 sm:h-4 sm:w-4" />
            <span className="hidden sm:inline">Alerts</span>
            <span className="sm:hidden">Alert</span>
          </TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4 md:space-y-6">
          {/* Assets Overview */}
          <Card className="trading-card critical-layer">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <PieChart className="h-5 w-5" />
                Asset Portfolio
              </CardTitle>
              <CardDescription>
                Your cryptocurrency holdings across all wallets
              </CardDescription>
            </CardHeader>
            <CardContent>
              {isLoadingAccountOverview && !accountOverview?.assets?.length ? (
                <div className="flex justify-center items-center py-10">
                  <Loader2 className="h-8 w-8 animate-spin text-primary"/>
                </div>
              ) : accountOverviewError ? (
                <p className="text-destructive text-center py-10">{accountOverviewError}</p>
              ) : (
                <AssetTable assets={accountOverview?.assets || []} />
              )}
            </CardContent>
          </Card>

          {/* Transaction History */}
          <Card className="trading-card critical-layer">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Activity className="h-5 w-5" />
                Recent Activity
              </CardTitle>
              <CardDescription>
                Your latest trading transactions
              </CardDescription>
            </CardHeader>
            <CardContent>
              {isLoadingTransactions && !recentTransactions.length ? (
                <div className="flex justify-center items-center py-10">
                  <Loader2 className="h-8 w-8 animate-spin text-primary"/>
                </div>
              ) : transactionsError ? (
                <p className="text-destructive text-center py-10">{transactionsError}</p>
              ) : (
                <TransactionHistoryTable transactions={recentTransactions} />
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="positions" className="space-y-4 md:space-y-6">
          <Card className="trading-card critical-layer">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Target className="h-5 w-5" />
                Open Positions
              </CardTitle>
              <CardDescription>
                Your active futures trading positions
              </CardDescription>
            </CardHeader>
            <CardContent>
              {isLoadingOpenPositions && !openPositions.length ? (
                <div className="flex justify-center items-center py-10">
                  <Loader2 className="h-8 w-8 animate-spin text-primary"/>
                </div>
              ) : openPositionsError ? (
                <p className="text-destructive text-center py-10">{openPositionsError}</p>
              ) : (
                <OpenPositionsTable 
                  positions={openPositions} 
                  apiKey={apiKey}
                  apiSecret={apiSecret}
                  onPositionUpdate={fetchOpenPositions}
                />
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="trading" className="space-y-4 md:space-y-6">
          <FuturesTradingTerminal 
            availableCryptos={cryptocurrencies}
            activeAlerts={alerts.filter(alert => alert.status === 'active')}
            currentPrices={currentPrices}
            onScrollToAlerts={handleScrollToAlerts}
            apiKey={apiKey}
            apiSecret={apiSecret}
          />
        </TabsContent>

        <TabsContent value="alerts" className="space-y-4 md:space-y-6">
          <div ref={alertsSectionRef} className="grid gap-6 md:grid-cols-2">
            <Card className="trading-card">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BellRing className="h-5 w-5" />
                  Create Price Alert
                </CardTitle>
                <CardDescription>
                  Set up notifications for price movements
                </CardDescription>
              </CardHeader>
              <CardContent>
                <CreateAlertForm
                  availableCryptos={cryptocurrencies}
                  onAlertCreated={handleAlertCreated}
                />
              </CardContent>
            </Card>

            <Card className="trading-card">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <ListCollapse className="h-5 w-5" />
                  Active Alerts
                  <Badge variant="secondary">{activeAlertsCount}</Badge>
                </CardTitle>
                <CardDescription>
                  Manage your price monitoring alerts
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ActiveAlertsList
                  alerts={alerts}
                  onDeleteAlert={handleDeleteAlert}
                  onToggleAlertStatus={handleToggleAlertStatus}
                />
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
