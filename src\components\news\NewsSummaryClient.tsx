"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, <PERSON><PERSON><PERSON>er, CardHeader, CardTitle } from "@/components/ui/card";
import { Textarea } from "@/components/ui/textarea";
// import { summarizeMarketNews, SummarizeMarketNewsInput } from "@/ai/flows/summarize-market-news";
import { Loader2, <PERSON>rkles } from "lucide-react";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";

// Mock portfolio and news articles
const mockPortfolio: string[] = ["BTC", "ETH", "SOL"];
const mockNewsArticles: string[] = [
  "Major Bitcoin conference discusses future of BTC payments and Lightning Network adoption.",
  "Ethereum's upcoming EIP-4844 'Proto-Danksharding' upgrade aims to significantly reduce Layer 2 transaction fees.",
  "Solana network experiences intermittent congestion issues, developers are working on a fix. SOL price sees slight dip.",
  "Global regulators announce new framework for cryptocurrency taxation and reporting.",
  "DeFi protocol on Ethereum suffers exploit, $10M lost. ETH community debates security measures.",
  "New report indicates growing institutional interest in Bitcoin ETFs.",
  "Aptos (APT) announces major partnership with a leading gaming company to build web3 games.",
];

export function NewsSummaryClient() {
  const [isLoading, setIsLoading] = useState(false);
  const [summary, setSummary] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [portfolioInput, setPortfolioInput] = useState(mockPortfolio.join(", "));
  const [articlesInput, setArticlesInput] = useState(mockNewsArticles.join("\n\n"));

  const handleSummarizeNews = async () => {
    setIsLoading(true);
    setSummary(null);
    setError(null);

    const portfolio = portfolioInput.split(",").map(s => s.trim()).filter(s => s.length > 0);
    const newsArticles = articlesInput.split("\n\n").map(s => s.trim()).filter(s => s.length > 0);

    if (portfolio.length === 0 || newsArticles.length === 0) {
      setError("Please provide both portfolio symbols and news articles.");
      setIsLoading(false);
      return;
    }

    const input = { portfolio, newsArticles };

    try {
      // Mock summary for production build
      const result = { summary: `Based on your portfolio [${portfolio.join(", ")}] and ${newsArticles.length} news articles, here's a summary:\n\n• Overall market sentiment appears mixed\n• Your portfolio assets may be affected by recent market developments\n• Consider monitoring for further updates\n\nNote: AI summarization temporarily disabled in production build.` };
      setSummary(result.summary);
    } catch (e) {
      console.error("Error summarizing news:", e);
      setError("Failed to generate news summary. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="grid gap-6 lg:grid-cols-2">
      <Card className="card-interactive">
        <CardHeader>
          <CardTitle>Input Your Data</CardTitle>
          <CardDescription>Provide your portfolio and news articles to get a personalized summary.</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <label htmlFor="portfolio" className="block text-sm font-medium mb-1">
              Your Portfolio (comma-separated symbols, e.g., BTC, ETH)
            </label>
            <Input
              id="portfolio"
              value={portfolioInput}
              onChange={(e) => setPortfolioInput(e.target.value)}
              placeholder="e.g., BTC, ETH, SOL"
            />
          </div>
          <div>
            <label htmlFor="articles" className="block text-sm font-medium mb-1">
              News Articles (paste articles, separated by double line breaks)
            </label>
            <Textarea
              id="articles"
              value={articlesInput}
              onChange={(e) => setArticlesInput(e.target.value)}
              placeholder="Paste news articles here..."
              rows={10}
              className="min-h-[200px]"
            />
          </div>
        </CardContent>
        <CardFooter>
          <Button onClick={handleSummarizeNews} disabled={isLoading} className="w-full bg-accent hover:bg-accent/90 text-accent-foreground">
            {isLoading ? (
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            ) : (
              <Sparkles className="mr-2 h-4 w-4" />
            )}
            Generate Summary
          </Button>
        </CardFooter>
      </Card>

      <Card className="card-interactive">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Sparkles className="h-6 w-6 text-primary" />
            AI-Powered News Summary
          </CardTitle>
          <CardDescription>
            Personalized market insights based on your portfolio and the latest news.
            Relevant coins from your portfolio: {portfolioInput.split(",").map(s => s.trim()).filter(s => s.length > 0).map(p => <Badge key={p} variant="secondary" className="mr-1">{p}</Badge>)}
          </CardDescription>
        </CardHeader>
        <CardContent className="min-h-[300px]">
          {isLoading && (
            <div className="flex flex-col items-center justify-center h-full">
              <Loader2 className="h-12 w-12 animate-spin text-primary mb-4" />
              <p className="text-muted-foreground">Generating your personalized summary...</p>
            </div>
          )}
          {error && <p className="text-destructive">{error}</p>}
          {summary && !isLoading && (
            <ScrollArea className="h-[calc(100%-2rem)] pr-4"> {/* Adjust height as needed */}
              <div className="prose prose-sm dark:prose-invert max-w-none whitespace-pre-line">
                {summary}
              </div>
            </ScrollArea>
          )}
          {!summary && !isLoading && !error && (
            <div className="flex flex-col items-center justify-center h-full text-center">
              <Sparkles className="h-12 w-12 text-muted-foreground mb-4" />
              <p className="text-muted-foreground">Your news summary will appear here once generated.</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
