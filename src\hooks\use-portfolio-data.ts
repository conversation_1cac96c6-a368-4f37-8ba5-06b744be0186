"use client";

import { useState, useEffect, useCallback, useRef } from 'react';
import { toast } from '@/hooks/use-toast';

interface PortfolioData {
  totalPortfolioValueUSDT: number;
  totalSpotValueUSDT: number;
  totalFuturesValueUSDT: number;
  spotPnl24hUSDT: number;
  spotPnl24hPercentage: number;
  assets: any[];
  allPrices?: Record<string, string>;
}

interface UsePortfolioDataReturn {
  data: PortfolioData | null;
  isLoading: boolean;
  error: string | null;
  isRefreshing: boolean;
  refetch: () => Promise<void>;
  lastUpdated: Date | null;
}

const CACHE_KEY = 'portfolio_data_cache';
const CACHE_DURATION = 30000; // 30 seconds

export function usePortfolioData(
  apiKey: string | null,
  apiSecret: string | null,
  autoRefresh = true,
  refreshInterval = 30000
): UsePortfolioDataReturn {
  const [data, setData] = useState<PortfolioData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);
  
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const abortControllerRef = useRef<AbortController | null>(null);

  // Cache management
  const getCachedData = useCallback(() => {
    try {
      const cached = localStorage.getItem(CACHE_KEY);
      if (cached) {
        const { data: cachedData, timestamp } = JSON.parse(cached);
        if (Date.now() - timestamp < CACHE_DURATION) {
          return cachedData;
        }
      }
    } catch (error) {
      console.warn('Failed to parse cached portfolio data:', error);
    }
    return null;
  }, []);

  const setCachedData = useCallback((portfolioData: PortfolioData) => {
    try {
      localStorage.setItem(CACHE_KEY, JSON.stringify({
        data: portfolioData,
        timestamp: Date.now()
      }));
    } catch (error) {
      console.warn('Failed to cache portfolio data:', error);
    }
  }, []);

  const fetchPortfolioData = useCallback(async (isInitialLoad = false, showToast = false) => {
    if (!apiKey || !apiSecret) {
      setError("API keys not configured");
      setIsLoading(false);
      return;
    }

    // Cancel previous request
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    // Create new abort controller
    abortControllerRef.current = new AbortController();

    try {
      if (isInitialLoad) {
        // Try to load from cache first
        const cachedData = getCachedData();
        if (cachedData) {
          setData(cachedData);
          setIsLoading(false);
          setError(null);
        }
      }

      if (!isInitialLoad && data) {
        setIsRefreshing(true);
      } else {
        setIsLoading(true);
      }

      setError(null);

      const response = await fetch('/api/binance/account-overview', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ apiKey, apiSecret }),
        signal: abortControllerRef.current.signal,
      });

      if (!response.ok) {
        let serverMessage = `HTTP error! status: ${response.status}`;
        try {
          const errorResult = await response.json();
          if (errorResult?.message) {
            serverMessage = errorResult.message;
          }
        } catch (e) {
          serverMessage = response.statusText || serverMessage;
        }
        throw new Error(serverMessage);
      }

      const result = await response.json();
      
      if (result.success && result.data?.accountOverviewData) {
        const portfolioData = result.data.accountOverviewData;
        setData(portfolioData);
        setCachedData(portfolioData);
        setError(null);
        setLastUpdated(new Date());

        if (showToast) {
          toast({
            title: "Portfolio Updated",
            description: "Your portfolio data has been refreshed successfully.",
            duration: 2000,
          });
        }
      } else {
        throw new Error(result.message || 'Failed to fetch portfolio data');
      }
    } catch (err: any) {
      if (err.name === 'AbortError') {
        return; // Request was cancelled, don't update state
      }

      const errorMessage = `Error fetching portfolio: ${err.message}`;
      setError(errorMessage);
      console.error('Portfolio fetch error:', err);

      if (showToast) {
        toast({
          title: "Update Failed",
          description: errorMessage,
          variant: "destructive",
          duration: 3000,
        });
      }
    } finally {
      setIsLoading(false);
      setIsRefreshing(false);
    }
  }, [apiKey, apiSecret, data, getCachedData, setCachedData]);

  const refetch = useCallback(async () => {
    await fetchPortfolioData(false, true);
  }, [fetchPortfolioData]);

  // Initial load
  useEffect(() => {
    if (apiKey && apiSecret) {
      fetchPortfolioData(true);
    } else {
      setIsLoading(false);
      setError("API keys not configured");
    }
  }, [apiKey, apiSecret]); // Only depend on API keys for initial load

  // Auto-refresh setup
  useEffect(() => {
    if (!autoRefresh || !apiKey || !apiSecret) {
      return;
    }

    intervalRef.current = setInterval(() => {
      fetchPortfolioData(false);
    }, refreshInterval);

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [autoRefresh, apiKey, apiSecret, refreshInterval, fetchPortfolioData]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, []);

  return {
    data,
    isLoading,
    error,
    isRefreshing,
    refetch,
    lastUpdated,
  };
}
