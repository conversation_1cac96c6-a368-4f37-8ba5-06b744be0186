"use client";

import React from "react";
import { PortfolioSummaryCard } from "@/components/dashboard/PortfolioSummaryCard";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { 
  DollarSign, 
  TrendingUp, 
  Activity, 
  RefreshCw,
  BarChart3,
  Target,
  Zap,
  BellRing
} from "lucide-react";

// Dados fake para demonstração
const mockPortfolioData = {
  totalPortfolioValueUSDT: 150000,
  totalSpotValueUSDT: 100000,
  totalFuturesValueUSDT: 50000,
  spotPnl24hUSDT: 2500,
  spotPnl24hPercentage: 1.67,
  totalUnrealizedFuturesPnl: 1250,
};

const mockAssets = [
  { symbol: "BTC", name: "Bitcoin", balance: 2.5, balanceUSDT: 87500, price24hChangePercent: 3.2 },
  { symbol: "ETH", name: "Ethereum", balance: 15, balanceUSDT: 37500, price24hChangePercent: -1.8 },
  { symbol: "BNB", name: "Binance Coin", balance: 50, balanceUSDT: 15000, price24hChangePercent: 0.9 },
  { symbol: "ADA", name: "Cardano", balance: 10000, balanceUSDT: 7500, price24hChangePercent: 4.1 },
  { symbol: "SOL", name: "Solana", balance: 25, balanceUSDT: 2500, price24hChangePercent: -2.3 },
];

const mockTransactions = [
  { symbol: "BTC", side: "BUY", quantity: 0.1, price: 35000, time: "2024-01-15 14:30:25", commission: 3.5 },
  { symbol: "ETH", side: "SELL", quantity: 2, price: 2500, time: "2024-01-15 13:15:10", commission: 2.5 },
  { symbol: "BNB", side: "BUY", quantity: 10, price: 300, time: "2024-01-15 12:45:55", commission: 1.5 },
  { symbol: "ADA", side: "BUY", quantity: 1000, price: 0.75, time: "2024-01-15 11:20:33", commission: 0.75 },
];

const mockPositions = [
  { symbol: "BTCUSDT", side: "LONG", size: 1.5, entryPrice: 34500, markPrice: 35200, unrealizedPnl: 1050, percentage: 2.03 },
  { symbol: "ETHUSDT", side: "SHORT", size: 5, entryPrice: 2520, markPrice: 2480, unrealizedPnl: 200, percentage: 1.59 },
];

export default function DemoPage() {
  return (
    <div className="space-y-4 md:space-y-6 px-2 sm:px-4 lg:px-0">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div className="space-y-1 min-w-0 flex-1">
          <h1 className="text-2xl sm:text-3xl lg:text-4xl font-bold tracking-tight gradient-primary bg-clip-text text-transparent">
            Trading Dashboard Demo
          </h1>
          <p className="text-muted-foreground text-sm sm:text-base">
            Real-time portfolio overview and trading insights (Demo Data)
          </p>
        </div>
        <div className="flex items-center gap-2 flex-shrink-0">
          <Badge variant="outline" className="flex items-center gap-1 text-xs sm:text-sm">
            <div className="status-dot status-online"></div>
            <span className="hidden sm:inline">Demo Mode</span>
            <span className="sm:hidden">Demo</span>
          </Badge>
          <Button variant="outline" size="sm" className="btn-trading text-xs sm:text-sm">
            <RefreshCw className="h-3 w-3 sm:h-4 sm:w-4 sm:mr-2" />
            <span className="hidden sm:inline">Refresh</span>
          </Button>
        </div>
      </div>

      {/* Portfolio Summary */}
      <PortfolioSummaryCard
        totalPortfolioValueUSDT={mockPortfolioData.totalPortfolioValueUSDT}
        totalSpotValueUSDT={mockPortfolioData.totalSpotValueUSDT}
        totalFuturesValueUSDT={mockPortfolioData.totalFuturesValueUSDT}
        spotPnl24hUSDT={mockPortfolioData.spotPnl24hUSDT}
        spotPnl24hPercentage={mockPortfolioData.spotPnl24hPercentage}
        totalUnrealizedFuturesPnl={mockPortfolioData.totalUnrealizedFuturesPnl}
        isLoading={false}
      />

      {/* Main Content Tabs */}
      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList className="grid w-full grid-cols-2 sm:grid-cols-4 h-auto transform-gpu">
          <TabsTrigger value="overview" className="flex items-center gap-1 sm:gap-2 text-xs sm:text-sm py-2">
            <BarChart3 className="h-3 w-3 sm:h-4 sm:w-4" />
            <span className="hidden sm:inline">Overview</span>
            <span className="sm:hidden">View</span>
          </TabsTrigger>
          <TabsTrigger value="positions" className="flex items-center gap-1 sm:gap-2 text-xs sm:text-sm py-2">
            <Target className="h-3 w-3 sm:h-4 sm:w-4" />
            <span className="hidden sm:inline">Positions</span>
            <span className="sm:hidden">Pos</span>
          </TabsTrigger>
          <TabsTrigger value="trading" className="flex items-center gap-1 sm:gap-2 text-xs sm:text-sm py-2">
            <Zap className="h-3 w-3 sm:h-4 sm:w-4" />
            <span className="hidden sm:inline">Trading</span>
            <span className="sm:hidden">Trade</span>
          </TabsTrigger>
          <TabsTrigger value="alerts" className="flex items-center gap-1 sm:gap-2 text-xs sm:text-sm py-2">
            <BellRing className="h-3 w-3 sm:h-4 sm:w-4" />
            <span className="hidden sm:inline">Alerts</span>
            <span className="sm:hidden">Alert</span>
          </TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4 md:space-y-6">
          {/* Assets Overview */}
          <Card className="trading-card critical-layer">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <DollarSign className="h-5 w-5" />
                Asset Portfolio
              </CardTitle>
              <CardDescription>
                Your cryptocurrency holdings across all wallets
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {mockAssets.map((asset, index) => (
                  <div key={asset.symbol} className="flex items-center justify-between p-4 bg-muted/30 rounded-lg">
                    <div className="flex items-center gap-3">
                      <div className="w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center">
                        <span className="text-sm font-bold text-primary">{asset.symbol.charAt(0)}</span>
                      </div>
                      <div>
                        <p className="font-medium">{asset.name}</p>
                        <p className="text-sm text-muted-foreground">{asset.symbol}</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="font-medium">${asset.balanceUSDT.toLocaleString()}</p>
                      <p className="text-sm text-muted-foreground">{asset.balance} {asset.symbol}</p>
                    </div>
                    <div className="text-right">
                      <Badge variant={asset.price24hChangePercent > 0 ? "default" : "destructive"}>
                        {asset.price24hChangePercent > 0 ? "+" : ""}{asset.price24hChangePercent}%
                      </Badge>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Transaction History */}
          <Card className="trading-card critical-layer">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Activity className="h-5 w-5" />
                Recent Activity
              </CardTitle>
              <CardDescription>
                Your latest trading transactions
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {mockTransactions.map((transaction, index) => (
                  <div key={index} className="flex items-center justify-between p-4 bg-muted/30 rounded-lg">
                    <div className="flex items-center gap-3">
                      <Badge variant={transaction.side === "BUY" ? "default" : "destructive"}>
                        {transaction.side}
                      </Badge>
                      <div>
                        <p className="font-medium">{transaction.symbol}</p>
                        <p className="text-sm text-muted-foreground">{transaction.time}</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="font-medium">{transaction.quantity} @ ${transaction.price}</p>
                      <p className="text-sm text-muted-foreground">Fee: ${transaction.commission}</p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="positions" className="space-y-4 md:space-y-6">
          <Card className="trading-card critical-layer">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Target className="h-5 w-5" />
                Open Positions
              </CardTitle>
              <CardDescription>
                Your active futures trading positions
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {mockPositions.map((position, index) => (
                  <div key={index} className="flex items-center justify-between p-4 bg-muted/30 rounded-lg">
                    <div className="flex items-center gap-3">
                      <Badge variant={position.side === "LONG" ? "default" : "destructive"}>
                        {position.side}
                      </Badge>
                      <div>
                        <p className="font-medium">{position.symbol}</p>
                        <p className="text-sm text-muted-foreground">Size: {position.size}</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="font-medium">Entry: ${position.entryPrice}</p>
                      <p className="text-sm text-muted-foreground">Mark: ${position.markPrice}</p>
                    </div>
                    <div className="text-right">
                      <p className={`font-medium ${position.unrealizedPnl > 0 ? 'text-green-400' : 'text-red-400'}`}>
                        ${position.unrealizedPnl}
                      </p>
                      <p className="text-sm text-muted-foreground">{position.percentage}%</p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="trading" className="space-y-4 md:space-y-6">
          <Card className="trading-card critical-layer">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Zap className="h-5 w-5" />
                Trading Terminal
              </CardTitle>
              <CardDescription>
                Advanced trading interface (Demo Mode)
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-20">
                <TrendingUp className="h-16 w-16 mx-auto text-primary/50 mb-4" />
                <h3 className="text-lg font-medium mb-2">Trading Terminal</h3>
                <p className="text-muted-foreground">
                  Advanced futures trading interface would be loaded here
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="alerts" className="space-y-4 md:space-y-6">
          <Card className="trading-card critical-layer">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BellRing className="h-5 w-5" />
                Price Alerts
              </CardTitle>
              <CardDescription>
                Manage your price monitoring alerts
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-20">
                <BellRing className="h-16 w-16 mx-auto text-primary/50 mb-4" />
                <h3 className="text-lg font-medium mb-2">Price Alerts</h3>
                <p className="text-muted-foreground">
                  Create and manage price alerts for your favorite cryptocurrencies
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
} 