"use client";

import { useState } from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  <PERSON>alogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Loader2, DollarSign, AlertTriangle } from "lucide-react";
import { toast } from "@/hooks/use-toast";
import type { OpenPosition } from "@/lib/types";

const preprocessedPrice = z.preprocess(
  (val) => {
    if (val === "" || val === null || val === undefined) {
      return undefined;
    }
    let stringVal = String(val);
    stringVal = stringVal.replace(/,/g, '.');
    stringVal = stringVal.replace(/[^\d.]/g, "");
    const firstDotIndex = stringVal.indexOf('.');
    if (firstDotIndex !== -1) {
      const integerPart = stringVal.substring(0, firstDotIndex);
      let fractionalPart = stringVal.substring(firstDotIndex + 1);
      fractionalPart = fractionalPart.replace(/\./g, "");
      stringVal = `${integerPart}.${fractionalPart}`;
    }
    if (stringVal === "" || stringVal === ".") {
      return undefined;
    }
    return stringVal;
  },
  z.coerce
    .number({ invalid_type_error: "Preço deve ser um número." })
    .positive("Preço deve ser positivo.")
    .optional()
);

const modifyPositionSchema = z.object({
  stopLoss: preprocessedPrice,
  takeProfit: preprocessedPrice,
}).refine(
  (data) => data.stopLoss !== undefined || data.takeProfit !== undefined,
  {
    message: "Pelo menos Stop Loss ou Take Profit deve ser especificado.",
    path: ["stopLoss"],
  }
);

type ModifyPositionFormValues = z.infer<typeof modifyPositionSchema>;

interface ModifyPositionModalProps {
  position: OpenPosition | null;
  isOpen: boolean;
  onClose: () => void;
  apiKey: string | null;
  apiSecret: string | null;
  onSuccess?: () => void;
}

export function ModifyPositionModal({
  position,
  isOpen,
  onClose,
  apiKey,
  apiSecret,
  onSuccess,
}: ModifyPositionModalProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);

  const form = useForm<ModifyPositionFormValues>({
    resolver: zodResolver(modifyPositionSchema),
    defaultValues: {
      stopLoss: undefined,
      takeProfit: undefined,
    },
  });

  const handleClose = () => {
    form.reset();
    onClose();
  };

  const onSubmit = async (data: ModifyPositionFormValues) => {
    if (!position || !apiKey || !apiSecret) {
      toast({
        title: "Erro de Configuração",
        description: "Posição ou chaves de API não estão disponíveis.",
        variant: "destructive",
      });
      return;
    }

    // Validate prices against current position
    const currentPrice = position.currentPrice;
    const isLongPosition = position.type === 'long';

    if (data.stopLoss) {
      const isValidStopLoss = isLongPosition 
        ? data.stopLoss < currentPrice  // For long positions, stop loss should be below current price
        : data.stopLoss > currentPrice; // For short positions, stop loss should be above current price
      
      if (!isValidStopLoss) {
        toast({
          title: "Stop Loss Inválido",
          description: `Para posições ${isLongPosition ? 'long' : 'short'}, o Stop Loss deve estar ${isLongPosition ? 'abaixo' : 'acima'} do preço atual (${currentPrice.toFixed(2)}).`,
          variant: "destructive",
        });
        return;
      }
    }

    if (data.takeProfit) {
      const isValidTakeProfit = isLongPosition 
        ? data.takeProfit > currentPrice  // For long positions, take profit should be above current price
        : data.takeProfit < currentPrice; // For short positions, take profit should be below current price
      
      if (!isValidTakeProfit) {
        toast({
          title: "Take Profit Inválido",
          description: `Para posições ${isLongPosition ? 'long' : 'short'}, o Take Profit deve estar ${isLongPosition ? 'acima' : 'abaixo'} do preço atual (${currentPrice.toFixed(2)}).`,
          variant: "destructive",
        });
        return;
      }
    }

    setIsSubmitting(true);
    try {
      const response = await fetch('/api/binance/futures/modify-position', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          apiKey,
          apiSecret,
          symbol: position.cryptoSymbol + 'USDT',
          stopLoss: data.stopLoss?.toString(),
          takeProfit: data.takeProfit?.toString(),
        }),
      });

      const result = await response.json();

      if (response.ok && result.success) {
        toast({
          title: "Sucesso!",
          description: result.message,
          className: "bg-green-100 dark:bg-green-800 border-green-500",
        });
        handleClose();
        if (onSuccess) {
          onSuccess();
        }
      } else {
        throw new Error(result.message || 'Falha ao modificar posição');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Erro desconhecido";
      toast({
        title: "Erro ao Modificar Posição",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!position) return null;

  const isLongPosition = position.type === 'long';
  const currentPrice = position.currentPrice;

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Modificar Stop Loss/Take Profit</DialogTitle>
          <DialogDescription>
            Configure Stop Loss e Take Profit para sua posição em {position.cryptoSymbol}.
            Posição atual: {position.type.toUpperCase()} • Preço: ${currentPrice.toFixed(2)}
          </DialogDescription>
        </DialogHeader>

        <div className="p-3 mb-4 border border-amber-500/50 bg-amber-500/10 rounded-md flex items-start gap-3">
          <AlertTriangle className="h-5 w-5 text-amber-500 flex-shrink-0 mt-0.5" />
          <div className="text-sm">
            <p className="font-semibold text-amber-700 dark:text-amber-400">Operação Real</p>
            <p className="text-amber-600 dark:text-amber-300">
              Isso criará ordens reais na Binance que executarão automaticamente quando os preços forem atingidos.
            </p>
          </div>
        </div>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="stopLoss"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="flex items-center gap-1">
                    <DollarSign className="h-4 w-4" />
                    Stop Loss (USD)
                  </FormLabel>
                  <FormControl>
                    <Input
                      type="text"
                      inputMode="decimal"
                      placeholder={`${isLongPosition ? 'Abaixo' : 'Acima'} de ${currentPrice.toFixed(2)}`}
                      {...field}
                      value={field.value === undefined || field.value === null || Number.isNaN(field.value) ? '' : String(field.value)}
                      onChange={(e) => field.onChange(e.target.value)}
                    />
                  </FormControl>
                  <FormMessage />
                  <p className="text-xs text-muted-foreground">
                    Para posições {isLongPosition ? 'long' : 'short'}, o Stop Loss deve estar {isLongPosition ? 'abaixo' : 'acima'} do preço atual.
                  </p>
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="takeProfit"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="flex items-center gap-1">
                    <DollarSign className="h-4 w-4" />
                    Take Profit (USD)
                  </FormLabel>
                  <FormControl>
                    <Input
                      type="text"
                      inputMode="decimal"
                      placeholder={`${isLongPosition ? 'Acima' : 'Abaixo'} de ${currentPrice.toFixed(2)}`}
                      {...field}
                      value={field.value === undefined || field.value === null || Number.isNaN(field.value) ? '' : String(field.value)}
                      onChange={(e) => field.onChange(e.target.value)}
                    />
                  </FormControl>
                  <FormMessage />
                  <p className="text-xs text-muted-foreground">
                    Para posições {isLongPosition ? 'long' : 'short'}, o Take Profit deve estar {isLongPosition ? 'acima' : 'abaixo'} do preço atual.
                  </p>
                </FormItem>
              )}
            />

            <DialogFooter className="gap-2">
              <Button type="button" variant="outline" onClick={handleClose} disabled={isSubmitting}>
                Cancelar
              </Button>
              <Button 
                type="submit" 
                disabled={isSubmitting || !apiKey || !apiSecret}
                className="bg-blue-600 hover:bg-blue-700"
              >
                {isSubmitting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Configurando...
                  </>
                ) : (
                  "Configurar SL/TP"
                )}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
} 