
import type { NextRequest } from 'next/server';
import { NextResponse } from 'next/server';
import { writeFile, mkdir } from 'fs/promises';
import path from 'path';
import { randomBytes } from 'crypto';

// Helper function to ensure the uploads directory exists
async function ensureUploadsDirectoryExists() {
  const uploadsPath = path.join(process.cwd(), 'public', 'uploads');
  try {
    await mkdir(uploadsPath, { recursive: true });
  } catch (error: any) {
    // Ignore error if directory already exists
    if (error.code !== 'EEXIST') {
      console.error('Failed to create uploads directory:', error);
      throw new Error('Server configuration error: Could not create uploads directory.');
    }
  }
}

export async function POST(request: NextRequest) {
  try {
    await ensureUploadsDirectoryExists();

    const formData = await request.formData();
    const file = formData.get('profileImage') as File | null;

    if (!file) {
      return NextResponse.json({ success: false, message: 'No file uploaded.' }, { status: 400 });
    }

    // Basic validation for file type (can be expanded)
    const allowedTypes = ['image/png', 'image/jpeg', 'image/gif', 'image/webp'];
    if (!allowedTypes.includes(file.type)) {
      return NextResponse.json({ success: false, message: 'Invalid file type. Only PNG, JPEG, GIF, WEBP are allowed.' }, { status: 400 });
    }

    // Basic validation for file size (e.g., 5MB)
    const maxSize = 5 * 1024 * 1024; // 5MB
    if (file.size > maxSize) {
      return NextResponse.json({ success: false, message: 'File is too large. Maximum size is 5MB.' }, { status: 400 });
    }

    const bytes = await file.arrayBuffer();
    const buffer = Buffer.from(bytes);

    // Generate a unique filename to prevent overwrites and add some randomness
    const uniqueSuffix = randomBytes(8).toString('hex');
    const fileExtension = path.extname(file.name) || '.png'; // Default to .png if no extension
    const uniqueFileName = `${Date.now()}-${uniqueSuffix}${fileExtension}`;
    
    // Construct the full path to save the file
    // Files in /public will be accessible from the root URL path
    const filePath = path.join(process.cwd(), 'public', 'uploads', uniqueFileName);

    await writeFile(filePath, buffer);

    const imageUrl = `/uploads/${uniqueFileName}`; // URL path to access the file

    return NextResponse.json({ success: true, imageUrl: imageUrl });

  } catch (error: any) {
    console.error('Upload error:', error);
    // Distinguish between known errors and general server errors
    if (error.message.startsWith('Server configuration error')) {
         return NextResponse.json({ success: false, message: error.message }, { status: 500 });
    }
    if (error.name === 'PayloadTooLargeError' || (error.message && error.message.includes('large'))) { // Next.js might throw its own error for large payloads
        return NextResponse.json({ success: false, message: 'File is too large. Maximum size is 5MB.' }, { status: 413 });
    }
    return NextResponse.json({ success: false, message: 'Failed to upload image. ' + (error instanceof Error ? error.message : 'Unknown error') }, { status: 500 });
  }
}

    